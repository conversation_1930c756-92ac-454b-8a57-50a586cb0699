# 公安内保系统 - 背景审查模块

## 项目概述

背景审查模块是公安内保系统的核心子系统，专门用于对安保人员进行背景审查数据的展示、分析和管理。系统通过数据同步模块获取背景审查数据，为公安内保部门提供全面的安保人员管理和风险控制能力。

## 业务背景

### 安保人员分类

系统管理三类安保人员：

1. **第一类**：保卫科有编制人员 - 正式编制的保卫科工作人员
2. **第二类**：安保部门无编制人员 - 属于安保部门但无正式编制的人员
3. **第三类**：安保外包人员 - 外包公司提供的安保服务人员

### 核心业务流程

- 数据同步：通过数据同步模块自动获取背景审查数据
- 数据管理：查询、调整、审核背景审查数据
- 风险控制：黑名单管理、预警通知
- 统计分析：多维度数据统计和报表生成

## 功能模块

### 1. 背景审查数据管理

**核心功能：**
- 数据查询与展示
- 数据调整与审核
- 黑名单操作
- 建议通知下发

**页面特性：**
- 支持全部人员/三类人员页签切换
- 多条件筛选查询
- 数据导入导出
- 批量操作支持

### 2. 黑名单管理

**核心功能：**
- 黑名单人员管理
- 自动预警机制
- 黑名单历史记录
- 解除黑名单流程

**业务规则：**
- 黑名单人员在任何单位出现时自动报警
- 支持临时黑名单和永久黑名单
- 黑名单操作需要审批流程

### 3. 数据统计分析

**统计维度：**
- 年度增量统计
- 各单位异常人员增量
- 区域分布分析
- 背景审查结果统计
- 人员类别分布

**可视化展示：**
- 图表展示（柱状图、饼图、折线图）
- 地图分布展示
- 趋势分析

### 4. 报表管理

**报表类型：**
- 背景审查汇总报表
- 异常人员统计报表
- 单位安保人员报表
- 黑名单人员报表
- 区域风险评估报表

**报表功能：**
- 自定义报表模板
- 定时生成报表
- 多格式导出（PDF、Excel、Word）

## 数据模型

### 核心字段

| 字段名 | 类型 | 说明 | 必填 |
|--------|------|------|------|
| 姓名 | String | 人员姓名 | ✓ |
| 手机号 | String | 联系电话 | ✓ |
| 身份证 | String | 身份证号码 | ✓ |
| 民族 | String | 民族信息 | ✓ |
| 职位 | String | 工作职位 | ✓ |
| 保安公司 | String | 所属保安公司 | - |
| 人脸照片 | File | 人员照片 | ✓ |
| 学历 | String | 教育背景 | ✓ |
| 区域 | String | 所在区域 | ✓ |
| 详细地址 | String | 详细住址 | ✓ |
| 政治面貌 | String | 政治面貌 | ✓ |
| 所属单位 | String | 工作单位 | ✓ |
| 人员类别 | Enum | 1-有编制/2-无编制/3-外包 | ✓ |
| 背景审查结果 | JSON | 审查结果详情 | ✓ |

### 背景审查结果字段

```json
{
  "mentalHealth": "精神健康状况",
  "criminalRecord": "违法犯罪记录",
  "drugUse": "吸毒记录",
  "creditRecord": "信用记录",
  "politicalBackground": "政治背景",
  "riskLevel": "风险等级",
  "reviewDate": "审查日期",
  "reviewer": "审查人员",
  "remarks": "备注信息"
}
```

## 技术架构

### 前端技术栈
- **框架**：Vue 3 + TypeScript
- **UI组件**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **构建工具**：Vite
- **图表库**：ECharts
- **地图**：高德地图API

### 后端技术栈
- **框架**：Nest.js + TypeScript
- **数据库**：MySQL 8.0
- **ORM**：TypeORM
- **认证**：JWT
- **文件存储**：阿里云OSS
- **缓存**：Redis

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API      │    │   数据同步模块   │
│   Vue 3        │◄──►│   Nest.js      │◄──►│   数据源系统     │
│   Element Plus │    │   MySQL        │    │   定时同步       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │   业务逻辑      │    │   数据存储       │
│   - 数据管理    │    │   - 权限控制    │    │   - 人员信息     │
│   - 黑名单      │    │   - 业务规则    │    │   - 审查结果     │
│   - 统计分析    │    │   - 数据验证    │    │   - 操作日志     │
│   - 报表生成    │    │   - 通知推送    │    │   - 系统配置     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 开发计划

### 第一阶段：基础框架搭建（1-2周）

**后端开发：**
- [ ] 数据库设计与建表
- [ ] 基础实体类创建
- [ ] 认证授权模块
- [ ] 基础CRUD接口
- [ ] 数据同步接口设计

**前端开发：**
- [ ] 项目结构搭建
- [ ] 路由配置
- [ ] 基础组件开发
- [ ] 状态管理设计
- [ ] API接口封装

### 第二阶段：核心功能开发（3-4周）

**数据管理模块：**
- [ ] 人员信息展示页面
- [ ] 多条件筛选功能
- [ ] 三类人员页签切换
- [ ] 数据编辑功能
- [ ] 批量操作功能

**黑名单管理：**
- [ ] 黑名单列表页面
- [ ] 加入/移除黑名单
- [ ] 黑名单预警机制
- [ ] 操作审批流程

### 第三阶段：统计分析功能（2-3周）

**数据统计：**
- [ ] 年度增量统计
- [ ] 单位异常人员统计
- [ ] 区域分布分析
- [ ] 图表可视化展示

**报表功能：**
- [ ] 报表模板设计
- [ ] 报表生成功能
- [ ] 多格式导出
- [ ] 定时报表任务

### 第四阶段：系统优化与测试（1-2周）

**性能优化：**
- [ ] 数据库查询优化
- [ ] 前端性能优化
- [ ] 缓存策略实施
- [ ] 接口响应优化

**测试与部署：**
- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户验收测试
- [ ] 生产环境部署

## 项目结构

```
背景审查模块/
├── backend/
│   ├── src/
│   │   ├── background-check/          # 背景审查核心模块
│   │   │   ├── entities/              # 数据实体
│   │   │   ├── dto/                   # 数据传输对象
│   │   │   ├── services/              # 业务逻辑
│   │   │   ├── controllers/           # 控制器
│   │   │   └── background-check.module.ts
│   │   ├── blacklist/                 # 黑名单管理
│   │   ├── statistics/                # 统计分析
│   │   ├── reports/                   # 报表管理
│   │   ├── data-sync/                 # 数据同步
│   │   └── common/                    # 公共模块
│   └── ...
├── frontend/
│   ├── src/
│   │   ├── views/
│   │   │   ├── background-check/      # 背景审查页面
│   │   │   │   ├── DataManagement.vue
│   │   │   │   ├── PersonnelDetail.vue
│   │   │   │   └── BatchOperation.vue
│   │   │   ├── blacklist/             # 黑名单管理
│   │   │   │   ├── BlacklistManagement.vue
│   │   │   │   └── BlacklistHistory.vue
│   │   │   ├── statistics/            # 统计分析
│   │   │   │   ├── DataStatistics.vue
│   │   │   │   └── RegionalAnalysis.vue
│   │   │   └── reports/               # 报表管理
│   │   │       ├── ReportGeneration.vue
│   │   │       └── ReportTemplates.vue
│   │   ├── components/                # 公共组件
│   │   │   ├── PersonnelCard.vue
│   │   │   ├── FilterPanel.vue
│   │   │   └── StatisticsChart.vue
│   │   └── ...
│   └── ...
└── docs/                              # 项目文档
    ├── API文档.md
    ├── 数据库设计.md
    └── 部署指南.md
```

## 部署说明

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0
- Nginx >= 1.18

### 部署步骤

1. **数据库初始化**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE background_check_system;

# 导入初始数据
mysql -u root -p background_check_system < database/init.sql
```

2. **后端部署**
```bash
cd backend
npm install
npm run build
npm run start:prod
```

3. **前端部署**
```bash
cd frontend
npm install
npm run build
# 将dist目录部署到Nginx
```

4. **Nginx配置**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 联系方式

- **项目负责人**：[负责人姓名]
- **开发团队**：公安局内保部门技术组
- **技术支持**：[联系邮箱]
- **项目地址**：[Git仓库地址]

## 更新日志

### v1.0.0 (计划中)
- 初始版本发布
- 基础功能实现
- 数据管理模块
- 黑名单管理模块
- 统计分析功能
- 报表生成功能

---

**注意：本系统涉及敏感数据，请严格按照公安部门信息安全要求进行开发和部署。**
