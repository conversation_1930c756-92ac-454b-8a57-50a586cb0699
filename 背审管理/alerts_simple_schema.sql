-- 预警管理系统 - 简化单表设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS background_check_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE background_check_system;

-- 预警表 (alerts) - 简化版单表设计
CREATE TABLE `alerts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '预警ID',
  `unit_id` bigint(20) NOT NULL COMMENT '预警单位ID',
  `unit_name` varchar(200) NOT NULL COMMENT '单位名称（冗余字段，避免关联查询）',
  `title` varchar(255) NOT NULL COMMENT '预警标题',
  `alert_type` varchar(50) NOT NULL COMMENT '预警类型：personnel人员异常,financial资金异常,operational经营异常,information信息安全,related关联企业,facility设施安全',
  `level` varchar(20) NOT NULL COMMENT '预警级别：high紧急,medium重要,low一般',
  `description` text NOT NULL COMMENT '预警描述详情',
  `source` varchar(50) NOT NULL DEFAULT 'manual' COMMENT '预警来源：manual手动添加,system系统预警,inspection检查发现,report举报线索',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending未处理,processing处理中,resolved已处理,ignored已忽略',
  `suggested_handler` varchar(100) DEFAULT NULL COMMENT '建议处理人',
  `actual_handler` varchar(100) DEFAULT NULL COMMENT '实际处理人',
  `deadline` date DEFAULT NULL COMMENT '处理期限',
  
  -- 处理信息字段（简化处理记录）
  `action_taken` text DEFAULT NULL COMMENT '已采取的处理措施',
  `process_result` text DEFAULT NULL COMMENT '处理结果',
  `process_note` text DEFAULT NULL COMMENT '处理备注',
  `resolved_at` datetime DEFAULT NULL COMMENT '解决时间',
  
  -- 系统字段
  `created_by` varchar(100) NOT NULL COMMENT '创建人',
  `updated_by` varchar(100) DEFAULT NULL COMMENT '最后更新人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0否,1是',
  
  PRIMARY KEY (`id`),
  KEY `idx_unit_id` (`unit_id`),
  KEY `idx_alert_type` (`alert_type`),
  KEY `idx_level` (`level`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deadline` (`deadline`),
  KEY `idx_unit_name` (`unit_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预警管理表';

-- 创建统计视图
CREATE VIEW `v_alert_stats` AS
SELECT 
    COUNT(*) as total_alerts,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_alerts,
    COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_alerts,
    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_alerts,
    COUNT(CASE WHEN status = 'ignored' THEN 1 END) as ignored_alerts,
    COUNT(CASE WHEN level = 'high' THEN 1 END) as high_alerts,
    COUNT(CASE WHEN level = 'medium' THEN 1 END) as medium_alerts,
    COUNT(CASE WHEN level = 'low' THEN 1 END) as low_alerts,
    COUNT(CASE WHEN deadline < CURDATE() AND status IN ('pending', 'processing') THEN 1 END) as overdue_alerts
FROM alerts 
WHERE is_deleted = 0;

-- 插入测试数据
INSERT INTO `alerts` (
    `unit_id`, `unit_name`, `title`, `alert_type`, `level`, `description`, `source`, `status`,
    `suggested_handler`, `actual_handler`, `deadline`, `action_taken`, `process_result`, `process_note`,
    `resolved_at`, `created_by`, `updated_by`
) VALUES
(1, '保定市竞秀区建设银行', '关键岗位人员频繁变动', 'personnel', 'high', 
 '该单位近期关键岗位人员频繁变动，存在潜在风险。近3个月内财务部和信息安全部门负责人变动3次，高于行业平均水平。', 
 'inspection', 'processing', '李警官', '李警官', '2024-03-20', 
 '1. 联系单位人事部门获取人员变动记录\n2. 分析变动原因和背景\n3. 评估潜在风险', 
 '发现3名关键岗位人员离职原因存疑，需进一步调查', 
 '正在深入调查离职人员背景', NULL, 'system', '李警官'),

(2, '保定市莲池区工商银行', '数据库访问异常', 'information', 'high',
 '系统监测到异常数据库访问行为，发现非工作时间大量数据查询请求，访问IP地址异常。',
 'system', 'processing', '王警官', '王警官', '2024-03-15', 
 '1. 立即启动应急响应\n2. 分析访问日志\n3. 临时封禁异常IP', 
 '已封禁异常IP，正在追查访问来源', 
 '需要进一步确认是否存在数据泄露', NULL, 'system', '王警官'),

(3, '保定市徐水区人民医院', '资金流向异常', 'financial', 'medium',
 '该医院近期出现大额资金异常流向，发现多笔对外转账记录与业务往来不符。',
 'manual', 'pending', '张警官', NULL, '2024-03-25', 
 NULL, NULL, NULL, NULL, '财务审计员', NULL),

(4, '保定市第一中学', '师生关系异常', 'personnel', 'low',
 '接到举报称该学校某教师与学生存在不当关系，需要调查核实情况。',
 'report', 'pending', '刘警官', NULL, '2024-03-28', 
 NULL, NULL, NULL, NULL, '举报人', NULL),

(5, '保定市高新区物流管理有限公司', '物流路线异常', 'operational', 'high',
 '该物流公司运输路线出现异常，多次偏离正常路线，停留在敏感区域附近。',
 'system', 'resolved', '赵警官', '赵警官', '2024-02-30', 
 '1. 联系物流公司核实情况\n2. 检查运输许可和路线申报\n3. 实地调研路线变更原因', 
 '经核实为临时绕行，公司已提供道路施工证明', 
 '确认为正常业务调整，无安全风险', '2024-02-15 14:30:00', 'system', '赵警官'),

(6, '保定市满城区党政机关', '网络安全漏洞', 'information', 'medium',
 '安全扫描发现该单位信息系统存在多个安全漏洞，包括SQL注入和XSS漏洞。',
 'inspection', 'resolved', '孙警官', '孙警官', '2024-02-25', 
 '1. 通知单位立即修复漏洞\n2. 提供技术指导\n3. 进行安全加固', 
 '所有漏洞已修复完成，系统通过安全测试', 
 '已完成安全加固，建立了定期扫描机制', '2024-02-20 16:00:00', '网络安全检查组', '孙警官'),

(7, '保定市满城区某商贸有限公司', '经营异常变化', 'operational', 'medium',
 '该商贸公司经营模式突然发生重大变化，从传统贸易转向高风险投资业务。',
 'manual', 'ignored', '周警官', NULL, '2024-02-10', 
 '1. 核实公司经营变更手续\n2. 评估新业务风险等级\n3. 检查相关资质', 
 '经评估属于正常商业调整，相关手续齐全', 
 '确认为合规经营变更，无需特殊关注', NULL, '监管部门', '周警官'),

(8, '保定市涿州市某运输物流公司', '设施安全隐患', 'facility', 'high',
 '该运输公司仓储设施存在重大安全隐患，发现危化品存储不规范，存在爆炸风险。',
 'inspection', 'processing', '郑警官', '郑警官', '2024-03-05', 
 '1. 要求立即停止危化品存储\n2. 督促整改安全设施\n3. 制定整改计划', 
 '单位已开始整改，正在验收整改效果', 
 '整改进度良好，计划月底完成验收', NULL, '安全检查组', '郑警官');

-- 常用查询示例
-- 1. 预警列表查询（支持分页和筛选）
SELECT id, unit_name, title, alert_type, level, status, created_at, deadline,
       CASE 
           WHEN deadline < CURDATE() AND status IN ('pending', 'processing') THEN 1 
           ELSE 0 
       END as is_overdue
FROM alerts 
WHERE is_deleted = 0
  AND (unit_id = ? OR ? IS NULL)  -- 单位筛选
  AND (alert_type = ? OR ? IS NULL)  -- 类型筛选
  AND (level = ? OR ? IS NULL)  -- 级别筛选
  AND (status = ? OR ? IS NULL)  -- 状态筛选
ORDER BY 
  CASE level WHEN 'high' THEN 3 WHEN 'medium' THEN 2 ELSE 1 END DESC,
  created_at DESC
LIMIT ? OFFSET ?;

-- 2. 预警详情查询
SELECT * FROM alerts WHERE id = ? AND is_deleted = 0;

-- 3. 预警统计查询
SELECT * FROM v_alert_stats;

-- 4. 单位预警统计
SELECT 
    unit_id,
    unit_name,
    COUNT(*) as total_alerts,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_alerts,
    COUNT(CASE WHEN level = 'high' THEN 1 END) as high_alerts,
    MAX(created_at) as latest_alert_time
FROM alerts 
WHERE is_deleted = 0
GROUP BY unit_id, unit_name
ORDER BY total_alerts DESC;

-- 5. 超期预警查询
SELECT id, unit_name, title, level, deadline, 
       DATEDIFF(CURDATE(), deadline) as overdue_days
FROM alerts 
WHERE deadline < CURDATE() 
  AND status IN ('pending', 'processing') 
  AND is_deleted = 0
ORDER BY overdue_days DESC; 