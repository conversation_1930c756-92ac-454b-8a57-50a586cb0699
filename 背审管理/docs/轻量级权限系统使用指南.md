# 轻量级权限系统使用指南

## 📋 概述

本文档介绍如何使用已实现的轻量级权限系统，包括基本用法、API调用和最佳实践。

## 🚀 快速开始

### 1. 访问权限测试页面

启动开发服务器后，访问权限测试页面：
```
http://localhost:5173/auth-test
```

### 2. 角色切换

页面右上角有一个角色切换器，可以在管理员和下级单位之间切换：
- **管理员模式**: 拥有全部权限，可以查看所有数据
- **下级单位模式**: 权限受限，只能查看本组织数据

## 🔧 基本用法

### 1. 在组件中使用权限Store

```vue
<template>
  <div>
    <!-- 显示当前用户信息 -->
    <div v-if="user">
      <p>当前用户: {{ user.name }}</p>
      <p>所属组织: {{ user.organizationName }}</p>
      <p>角色: {{ isAdmin ? '管理员' : '下级单位' }}</p>
    </div>
    
    <!-- 根据权限显示不同内容 -->
    <el-button v-if="hasPermission('task:manage')" @click="manageTask">
      任务管理
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/authStore'

const authStore = useAuthStore()

// 获取用户信息
const user = computed(() => authStore.user)
const isAdmin = computed(() => authStore.isAdmin)

// 权限检查
const hasPermission = (permission: string) => {
  return authStore.hasPermission(permission)
}

const manageTask = () => {
  console.log('执行任务管理操作')
}
</script>
```

### 2. 使用权限指令

```vue
<template>
  <div>
    <!-- 单个权限检查 -->
    <el-button v-permission="'personnel:view:all'">
      查看所有人员
    </el-button>
    
    <!-- 多个权限检查（任一即可） -->
    <el-button v-permission="['task:assign', 'task:review']">
      任务操作
    </el-button>
    
    <!-- 多个权限检查（必须全部拥有） -->
    <el-button v-permission:all="['personnel:edit', 'personnel:delete']">
      人员编辑
    </el-button>
    
    <!-- 角色检查 -->
    <div v-role="'admin'">
      管理员专用内容
    </div>
    
    <div v-role="'unit'">
      下级单位专用内容
    </div>
  </div>
</template>
```

### 3. 使用权限工具函数

```typescript
import { 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions,
  isAdmin, 
  isUnit,
  getCurrentUser,
  getCurrentRole,
  getDataScope,
  PERMISSIONS,
  ROLES
} from '@/utils/auth'

// 权限检查
if (hasPermission(PERMISSIONS.PERSONNEL_VIEW_ALL)) {
  console.log('可以查看所有人员')
}

// 多权限检查
if (hasAnyPermission([PERMISSIONS.TASK_ASSIGN, PERMISSIONS.TASK_REVIEW])) {
  console.log('可以进行任务操作')
}

// 角色检查
if (isAdmin()) {
  console.log('当前是管理员')
}

// 获取当前用户
const user = getCurrentUser()
console.log('当前用户:', user?.name)

// 获取数据范围
const scope = getDataScope()
console.log('数据访问范围:', scope) // 'all' 或 'organization'
```

## 📊 权限配置

### 1. 用户角色和权限

**管理员角色 (admin)**:
- `personnel:view:all` - 查看所有人员
- `task:manage` - 任务管理
- `task:assign` - 分配任务
- `task:review` - 审核任务
- `dashboard:admin` - 管理员工作台

**下级单位角色 (unit)**:
- `personnel:view:org` - 查看本组织人员
- `task:process` - 处理任务
- `task:submit` - 提交任务结果
- `dashboard:unit` - 下级单位工作台

### 2. 权限常量

使用预定义的权限常量，避免硬编码：

```typescript
import { PERMISSIONS, ROLES } from '@/utils/auth'

// 使用权限常量
hasPermission(PERMISSIONS.PERSONNEL_VIEW_ALL)
hasPermission(PERMISSIONS.TASK_MANAGE)

// 使用角色常量
if (getCurrentRole() === ROLES.ADMIN) {
  // 管理员逻辑
}
```

## 🔄 角色切换功能

### 1. 手动切换角色

```typescript
import { useAuthStore } from '@/stores/authStore'

const authStore = useAuthStore()

// 切换到管理员
await authStore.switchRole('admin')

// 切换到下级单位
await authStore.switchRole('unit')
```

### 2. 监听角色变化

```typescript
// 监听角色变化事件
window.addEventListener('auth-role-changed', (event: any) => {
  const { role, user } = event.detail
  console.log(`角色已切换为: ${role}`, user)
  
  // 执行角色切换后的逻辑
  handleRoleChange(role)
})
```

## 🛡️ 最佳实践

### 1. 权限检查原则

- **前端权限仅用于UI控制**: 不要依赖前端权限做安全控制
- **后端必须验证权限**: 所有API调用都应在后端验证权限
- **最小权限原则**: 只给用户必需的权限

### 2. 组件设计

```vue
<template>
  <div class="component">
    <!-- 根据权限显示不同的操作按钮 -->
    <div class="actions">
      <el-button 
        v-permission="PERMISSIONS.PERSONNEL_EDIT"
        @click="editPersonnel"
      >
        编辑
      </el-button>
      
      <el-button 
        v-permission="PERMISSIONS.PERSONNEL_DELETE"
        type="danger"
        @click="deletePersonnel"
      >
        删除
      </el-button>
    </div>
    
    <!-- 根据数据范围显示不同内容 -->
    <div class="data-display">
      <template v-if="dataScope === 'all'">
        <!-- 管理员可以看到所有数据 -->
        <AllDataComponent />
      </template>
      <template v-else>
        <!-- 下级单位只能看到本组织数据 -->
        <OrgDataComponent :org-id="currentUser?.organizationId" />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/authStore'
import { PERMISSIONS } from '@/utils/auth'

const authStore = useAuthStore()

const dataScope = computed(() => authStore.dataScope)
const currentUser = computed(() => authStore.user)

const editPersonnel = () => {
  // 编辑逻辑
}

const deletePersonnel = () => {
  // 删除逻辑
}
</script>
```

### 3. API调用权限控制

```typescript
// 在API调用前检查权限
async function fetchPersonnelList() {
  const authStore = useAuthStore()
  
  // 检查权限
  if (!authStore.hasPermission('personnel:view:all') && 
      !authStore.hasPermission('personnel:view:org')) {
    throw new Error('权限不足')
  }
  
  // 根据权限范围调用不同的API
  const params: any = {}
  if (authStore.dataScope === 'organization') {
    params.organizationId = authStore.user?.organizationId
  }
  
  return await api.getPersonnelList(params)
}
```

## 🔍 调试和测试

### 1. 权限测试页面

访问 `/auth-test` 页面可以：
- 查看当前用户信息和权限
- 测试权限指令的显示效果
- 进行角色切换测试
- 验证编程式权限检查

### 2. 控制台调试

```javascript
// 在浏览器控制台中调试
const authStore = useAuthStore()

// 查看当前用户
console.log('当前用户:', authStore.user)

// 查看权限列表
console.log('用户权限:', authStore.userPermissions)

// 测试权限检查
console.log('是否管理员:', authStore.isAdmin)
console.log('数据范围:', authStore.dataScope)
```

## 🚨 注意事项

### 1. 开发环境 vs 生产环境

- **开发环境**: 使用Mock数据，支持角色切换
- **生产环境**: 需要集成真实的外部权限API

### 2. 权限缓存

- 权限信息会在本地缓存，减少API调用
- 角色切换会清除缓存，重新获取权限信息
- 页面刷新会重新初始化权限

### 3. 错误处理

```typescript
try {
  await authStore.initAuth()
} catch (error) {
  console.error('权限初始化失败:', error)
  // 处理权限初始化失败的情况
  // 可以显示错误提示或跳转到登录页
}
```

## 📈 后续扩展

### 1. 集成真实API

将Mock服务替换为真实的外部API调用：

```typescript
// 修改 authService.ts
async getCurrentUser(): Promise<UserInfo> {
  if (import.meta.env.DEV) {
    // 开发环境使用Mock
    return this.getMockUser()
  } else {
    // 生产环境调用真实API
    const response = await fetch('/api/external/auth/user/current')
    return response.json()
  }
}
```

### 2. 添加权限缓存

实现更完善的权限缓存机制：
- 设置缓存过期时间
- 实现缓存刷新策略
- 添加离线权限支持

### 3. 扩展权限类型

根据业务需要添加更多权限类型：
- 字段级权限控制
- 时间范围权限
- 地域权限控制

这个轻量级权限系统为后续的功能开发提供了坚实的基础，可以根据实际需求进行扩展和完善。
