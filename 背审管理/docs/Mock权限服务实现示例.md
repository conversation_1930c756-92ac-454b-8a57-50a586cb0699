# 轻量级权限服务实现示例（外部API集成）

## 📋 概述

本文档提供了基于外部API集成的轻量级权限管理系统实现示例，包括API调用服务、权限缓存、Mock功能等，用于开发阶段的功能验证和测试。

## 🔧 轻量级权限服务实现

### 1. 外部API调用服务

```typescript
// src/services/externalAuthApi.ts
import request from '@/utils/request'

interface ExternalUserInfo {
  userId: string
  username: string
  name: string
  phone?: string
  email?: string
  organizationId: string
  organizationName: string
  roles: string[]
  permissions: string[]
}

class ExternalAuthApi {
  private baseUrl = '/api/external/auth'

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<ExternalUserInfo> {
    const response = await request.get(`${this.baseUrl}/user/current`)
    return response.data.data
  }

  /**
   * 获取用户权限列表
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    const response = await request.get(`${this.baseUrl}/permissions/${userId}`)
    return response.data.data
  }

  /**
   * 获取组织信息
   */
  async getOrganization(orgId: string): Promise<any> {
    const response = await request.get(`/api/external/organizations/${orgId}`)
    return response.data.data
  }
}

export const externalAuthApi = new ExternalAuthApi()
```

### 2. 权限服务类 (AuthService)

```typescript
// src/services/authService.ts
import { externalAuthApi } from './externalAuthApi'

class AuthService {
  private isDev = import.meta.env.DEV
  private mockUserRole: 'admin' | 'unit' = 'admin'
  private userCache: ExternalUserInfo | null = null
  private cacheExpiry: number = 0
  private readonly CACHE_TTL = 30 * 60 * 1000 // 30分钟缓存
  
  // Mock数据（仅开发环境使用）
  private mockUsers: Record<string, ExternalUserInfo> = {
    admin: {
      userId: 'admin-001',
      username: 'admin',
      name: '系统管理员',
      phone: '13800138000',
      email: '<EMAIL>',
      organizationId: 'org-admin',
      organizationName: '保定市公安局',
      roles: ['admin'],
      permissions: ['personnel:view:all', 'task:manage', 'task:assign', 'task:review']
    },
    unit: {
      userId: 'unit-001',
      username: 'unit001',
      name: '莲池区分局',
      phone: '13800138001',
      email: '<EMAIL>',
      organizationId: 'org-unit-001',
      organizationName: '莲池区分局',
      roles: ['unit'],
      permissions: ['personnel:view:org', 'task:process', 'task:submit']
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return this.userCache !== null && Date.now() < this.cacheExpiry
  }

  /**
   * 清除缓存
   */
  private clearCache(): void {
    this.userCache = null
    this.cacheExpiry = 0
  }

  /**
   * 获取Mock用户数据
   */
  private getMockUser(): ExternalUserInfo {
    return this.mockUsers[this.mockUserRole]
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<ExternalUserInfo> {
    // 检查缓存
    if (this.isCacheValid()) {
      return this.userCache!
    }

    try {
      let userInfo: ExternalUserInfo

      if (this.isDev) {
        // 开发环境使用Mock数据
        await new Promise(resolve => setTimeout(resolve, 100)) // 模拟网络延迟
        userInfo = this.getMockUser()
      } else {
        // 生产环境调用外部API
        userInfo = await externalAuthApi.getCurrentUser()
      }

      // 更新缓存
      this.userCache = userInfo
      this.cacheExpiry = Date.now() + this.CACHE_TTL

      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)

      // 降级处理：如果API调用失败，使用缓存数据或默认数据
      if (this.userCache) {
        console.warn('使用缓存的用户信息')
        return this.userCache
      }

      // 最后的降级方案：返回默认用户信息
      console.warn('使用默认用户信息')
      return this.getMockUser()
    }
  }

  /**
   * 检查是否为管理员
   */
  isAdmin(): boolean {
    if (this.isDev) {
      return this.mockUserRole === 'admin'
    }
    return this.userCache?.roles.includes('admin') || false
  }

  /**
   * 检查是否有指定权限
   */
  hasPermission(permission: string): boolean {
    if (this.isDev) {
      const user = this.getMockUser()
      return user.permissions.includes(permission)
    }
    return this.userCache?.permissions.includes(permission) || false
  }

  /**
   * 获取用户权限列表
   */
  async getUserPermissions(): Promise<string[]> {
    const user = await this.getCurrentUser()
    return user.permissions
  }

  /**
   * 获取数据访问范围
   */
  getDataScope(): 'all' | 'organization' {
    return this.isAdmin() ? 'all' : 'organization'
  }

  /**
   * 获取可访问的组织ID列表
   */
  async getAccessibleOrganizationIds(): Promise<string[]> {
    const user = await this.getCurrentUser()

    if (this.isAdmin()) {
      // 管理员可以访问所有组织，这里需要调用相应的API获取
      return ['all'] // 特殊标识，表示所有组织
    } else {
      // 下级单位只能访问自己的组织
      return [user.organizationId]
    }
  }

  /**
   * 切换用户角色（仅开发阶段使用）
   */
  mockSwitchRole(role: 'admin' | 'unit'): void {
    if (!this.isDev) {
      console.warn('角色切换功能仅在开发环境可用')
      return
    }

    this.mockUserRole = role
    this.clearCache() // 清除缓存，强制重新获取

    // 触发全局状态更新
    window.dispatchEvent(new CustomEvent('auth-role-changed', {
      detail: { role, user: this.getMockUser() }
    }))

    console.log(`🔄 角色已切换为: ${role === 'admin' ? '管理员' : '下级单位'}`)
  }

  /**
   * 获取当前角色（仅开发环境）
   */
  getCurrentRole(): 'admin' | 'unit' {
    return this.mockUserRole
  }

  /**
   * 刷新用户信息缓存
   */
  async refreshUserInfo(): Promise<ExternalUserInfo> {
    this.clearCache()
    return await this.getCurrentUser()
  }

  /**
   * 登出
   */
  logout(): void {
    this.clearCache()

    if (this.isDev) {
      this.mockUserRole = 'admin' // 重置为默认角色
    }

    window.dispatchEvent(new CustomEvent('auth-logout'))
    console.log('🚪 用户已登出')
  }
}

// 创建单例实例
export const authService = new AuthService()
export default authService
```

### 2. 权限状态管理 (Pinia Store)

```typescript
// src/stores/authStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authService from '@/services/authService'
import type { User, Organization } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const organizations = ref<Organization[]>([])
  const loading = ref(false)
  const initialized = ref(false)

  // 计算属性
  const isAdmin = computed(() => user.value?.organizationType === 'admin')
  const isLoggedIn = computed(() => !!user.value)
  const currentRole = computed(() => authService.getCurrentRole())
  const userPermissions = computed(() => user.value?.permissions || [])
  const currentOrganization = computed(() => 
    organizations.value.find(org => org.id === user.value?.organizationId)
  )

  // 方法
  const initAuth = async () => {
    if (initialized.value) return
    
    try {
      loading.value = true
      user.value = await authService.getCurrentUser()
      organizations.value = await authService.getOrganizations()
      initialized.value = true
    } catch (error) {
      console.error('初始化权限失败:', error)
    } finally {
      loading.value = false
    }
  }

  const login = async (username: string, password: string) => {
    try {
      loading.value = true
      user.value = await authService.login(username, password)
      organizations.value = await authService.getOrganizations()
      return user.value
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    authService.logout()
    user.value = null
    organizations.value = []
    initialized.value = false
  }

  const switchRole = async (role: 'admin' | 'unit') => {
    try {
      loading.value = true
      authService.switchRole(role)
      user.value = await authService.getCurrentUser()
      organizations.value = await authService.getOrganizations()
    } catch (error) {
      console.error('切换角色失败:', error)
    } finally {
      loading.value = false
    }
  }

  const hasPermission = (permission: string): boolean => {
    return authService.hasPermission(permission)
  }

  const getDataScope = (): 'all' | 'organization' => {
    return authService.getDataScope()
  }

  const getAccessibleOrganizationIds = async (): Promise<string[]> => {
    return await authService.getAccessibleOrganizationIds()
  }

  // 监听角色变化事件
  if (typeof window !== 'undefined') {
    window.addEventListener('auth-role-changed', async (event: any) => {
      const { role, user: newUser } = event.detail
      user.value = newUser
      organizations.value = await authService.getOrganizations()
    })

    window.addEventListener('auth-logout', () => {
      logout()
    })
  }

  return {
    // 状态
    user,
    organizations,
    loading,
    initialized,
    
    // 计算属性
    isAdmin,
    isLoggedIn,
    currentRole,
    userPermissions,
    currentOrganization,
    
    // 方法
    initAuth,
    login,
    logout,
    switchRole,
    hasPermission,
    getDataScope,
    getAccessibleOrganizationIds
  }
})
```

### 3. 角色切换组件

```vue
<!-- src/components/common/RoleSwitcher.vue -->
<template>
  <div class="role-switcher">
    <el-dropdown @command="handleRoleSwitch" trigger="click">
      <el-button type="primary" size="small">
        <el-icon><User /></el-icon>
        {{ currentRoleText }}
        <el-icon class="el-icon--right"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item 
            command="admin" 
            :disabled="currentRole === 'admin'"
          >
            <el-icon><UserFilled /></el-icon>
            管理员模式
          </el-dropdown-item>
          <el-dropdown-item 
            command="unit" 
            :disabled="currentRole === 'unit'"
          >
            <el-icon><OfficeBuilding /></el-icon>
            下级单位模式
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 当前用户信息 -->
    <div class="user-info" v-if="user">
      <el-tag :type="isAdmin ? 'danger' : 'success'" size="small">
        {{ user.name }}
      </el-tag>
      <el-tag type="info" size="small" style="margin-left: 8px;">
        {{ user.organizationName }}
      </el-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { User, UserFilled, OfficeBuilding, ArrowDown } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/authStore'

const authStore = useAuthStore()

// 计算属性
const currentRole = computed(() => authStore.currentRole)
const user = computed(() => authStore.user)
const isAdmin = computed(() => authStore.isAdmin)

const currentRoleText = computed(() => {
  return currentRole.value === 'admin' ? '管理员' : '下级单位'
})

// 方法
const handleRoleSwitch = async (role: 'admin' | 'unit') => {
  try {
    await authStore.switchRole(role)
    ElMessage.success(`已切换到${role === 'admin' ? '管理员' : '下级单位'}模式`)
    
    // 刷新页面以应用新的权限设置
    setTimeout(() => {
      window.location.reload()
    }, 500)
  } catch (error) {
    ElMessage.error('角色切换失败')
    console.error('角色切换失败:', error)
  }
}
</script>

<style scoped>
.role-switcher {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  align-items: center;
}

.el-dropdown {
  cursor: pointer;
}
</style>
```

### 4. 权限指令

```typescript
// src/directives/permission.ts
import { Directive, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/authStore'

/**
 * 权限指令
 * 用法: v-permission="'permission:code'"
 * 或: v-permission="['permission1', 'permission2']"
 */
export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const authStore = useAuthStore()
    const { value } = binding
    
    if (!value) return
    
    let hasPermission = false
    
    if (Array.isArray(value)) {
      // 数组形式：需要拥有其中任意一个权限
      hasPermission = value.some(permission => authStore.hasPermission(permission))
    } else {
      // 字符串形式：需要拥有指定权限
      hasPermission = authStore.hasPermission(value)
    }
    
    if (!hasPermission) {
      // 没有权限则隐藏元素
      el.style.display = 'none'
      // 或者移除元素
      // el.parentNode?.removeChild(el)
    }
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    // 权限变化时重新检查
    const authStore = useAuthStore()
    const { value } = binding
    
    if (!value) return
    
    let hasPermission = false
    
    if (Array.isArray(value)) {
      hasPermission = value.some(permission => authStore.hasPermission(permission))
    } else {
      hasPermission = authStore.hasPermission(value)
    }
    
    el.style.display = hasPermission ? '' : 'none'
  }
}

// 注册指令
export default {
  install(app: any) {
    app.directive('permission', permission)
  }
}
```

### 5. 路由守卫

```typescript
// src/router/guards.ts
import { Router } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { ElMessage } from 'element-plus'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    
    // 初始化权限信息
    if (!authStore.initialized) {
      try {
        await authStore.initAuth()
      } catch (error) {
        console.error('权限初始化失败:', error)
        ElMessage.error('系统初始化失败')
        return next('/login')
      }
    }
    
    // 检查是否需要登录
    if (to.meta.requiresAuth && !authStore.isLoggedIn) {
      ElMessage.warning('请先登录')
      return next('/login')
    }
    
    // 检查管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      ElMessage.error('权限不足，无法访问该页面')
      return next('/403')
    }
    
    // 检查特定权限
    if (to.meta.permissions) {
      const permissions = Array.isArray(to.meta.permissions) 
        ? to.meta.permissions 
        : [to.meta.permissions]
      
      const hasPermission = permissions.some(permission => 
        authStore.hasPermission(permission)
      )
      
      if (!hasPermission) {
        ElMessage.error('权限不足，无法访问该页面')
        return next('/403')
      }
    }
    
    // 根据角色重定向首页
    if (to.path === '/' || to.path === '/dashboard') {
      if (authStore.isAdmin) {
        return next('/admin/dashboard')
      } else {
        return next('/unit/dashboard')
      }
    }
    
    next()
  })
  
  // 全局后置钩子
  router.afterEach((to) => {
    // 设置页面标题
    document.title = to.meta.title 
      ? `${to.meta.title} - 背审管理系统` 
      : '背审管理系统'
  })
}
```

### 6. 使用示例

```vue
<!-- 在组件中使用权限控制 -->
<template>
  <div class="page-container">
    <!-- 角色切换器（仅开发环境显示） -->
    <RoleSwitcher v-if="isDev" style="position: fixed; top: 10px; right: 10px; z-index: 9999;" />
    
    <!-- 管理员专用功能 -->
    <el-card v-if="isAdmin" class="admin-panel">
      <h3>管理员功能</h3>
      <el-button 
        v-permission="'task:assign'" 
        type="primary" 
        @click="assignTask"
      >
        分配任务
      </el-button>
      <el-button 
        v-permission="'task:review'" 
        type="success" 
        @click="reviewTask"
      >
        审核任务
      </el-button>
    </el-card>
    
    <!-- 下级单位功能 -->
    <el-card v-else class="unit-panel">
      <h3>单位功能</h3>
      <el-button 
        v-permission="'task:process'" 
        type="primary" 
        @click="processTask"
      >
        处理任务
      </el-button>
      <el-button 
        v-permission="'task:submit'" 
        type="success" 
        @click="submitResult"
      >
        提交结果
      </el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/authStore'
import RoleSwitcher from '@/components/common/RoleSwitcher.vue'

const authStore = useAuthStore()

// 计算属性
const isAdmin = computed(() => authStore.isAdmin)
const isDev = computed(() => import.meta.env.DEV)

// 方法
const assignTask = () => {
  console.log('分配任务')
}

const reviewTask = () => {
  console.log('审核任务')
}

const processTask = () => {
  console.log('处理任务')
}

const submitResult = () => {
  console.log('提交结果')
}
</script>
```

## 🚀 使用说明

### 1. 安装和配置

```typescript
// main.ts
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import permissionDirective from '@/directives/permission'
import { setupRouterGuards } from '@/router/guards'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(permissionDirective)

// 设置路由守卫
setupRouterGuards(router)

app.mount('#app')
```

### 2. 开发阶段使用

1. **角色切换**: 使用RoleSwitcher组件在管理员和下级单位角色间切换
2. **权限测试**: 使用v-permission指令测试不同权限下的界面显示
3. **数据权限**: 通过getDataScope()方法控制数据查询范围
4. **路由权限**: 在路由meta中配置权限要求

### 3. 生产环境适配

1. **移除Mock**: 将Mock服务替换为真实的API调用
2. **Token管理**: 添加JWT token的存储和刷新机制
3. **权限缓存**: 实现权限信息的本地缓存
4. **安全加固**: 添加更严格的权限验证和错误处理

这个Mock权限服务为开发阶段提供了完整的权限管理功能，支持角色切换、权限控制、数据权限等核心特性，便于功能开发和测试验证。
