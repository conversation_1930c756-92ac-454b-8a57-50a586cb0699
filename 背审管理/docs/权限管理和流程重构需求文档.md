# 背审管理系统权限管理和流程重构需求文档

## 📋 项目概述

根据客户需求，对背审管理系统进行大规模调整，主要集中在权限管理和整个背审、处理流程的重构。系统将区分管理员和下级单位两种角色，提供不同的功能界面和权限控制。

## 🎯 核心需求

### 1. 权限管理系统
- **角色区分**: 基于用户角色（admin/非admin）进入不同的管理界面
- **权限控制**: 管理员可查看全局数据，下级单位只能查看自己的数据
- **接口设计**: 提供获取当前用户角色的接口，支持手动mock切换

### 2. 工作台差异化
- **管理员工作台**: 显示全局统计数据和管理功能入口
- **下级单位工作台**: 显示本单位相关数据和处理功能入口

### 3. 背审人员管理重构
- **分类管理**: 按人员类型分类（专职保卫、保安人员、物流人员等）
- **全局查看**: 不区分状态，仅按类别展示
- **操作简化**: 当前仅保留查看功能，后续扩展背审、处理操作
- **批量操作**: 支持多选，为后续批量操作做准备

### 4. 背审任务管理（管理员端）
- **待背审**: 对待背审人员下发/查询背审任务
- **待处理**: 对背审异常的待处理人员查询/下发处理任务  
- **已处理**: 对已处理人员进行查询，可再次下发处理任务

### 5. 背审工作处理（下级单位端）
- **待背审**: 对收到的背审任务进行上报
- **待处理**: 对收到的处理任务进行批量处理
- **已处理**: 对已处理的任务进行查询

## 🏗️ 系统架构设计

### 权限管理架构

```typescript
// 用户角色定义
interface UserRole {
  id: string
  name: string
  permissions: string[]
  level: 'admin' | 'unit' // 管理员 | 下级单位
}

// 权限检查接口
interface AuthService {
  getCurrentUser(): Promise<User>
  getUserRoles(): Promise<UserRole[]>
  hasPermission(permission: string): boolean
  isAdmin(): boolean
}
```

### 菜单结构重构

#### 管理员菜单结构
```
工作台（管理员）

背审人员管理
├── 专职保卫
├── 保安人员  
├── 物流人员
└── 其他人员

背审任务管理
├── 待背审
├── 待处理
└── 已处理

统计分析
├── 数据看板
└── 统计报表
```

#### 下级单位菜单结构
```
工作台（下级单位）

背审人员管理
├── 专职保卫
├── 保安人员
├── 物流人员
└── 其他人员

背审工作处理
├── 待背审
├── 待处理
└── 已处理

统计分析
├── 数据看板
└── 统计报表
```

## 📊 数据结构设计

### 1. 用户和权限相关

```typescript
// 用户信息
interface User {
  id: string
  username: string
  name: string
  organizationId: string
  organizationName: string
  roles: UserRole[]
  isAdmin: boolean
  permissions: string[]
}

// 组织机构
interface Organization {
  id: string
  name: string
  code: string
  level: number
  parentId?: string
  type: 'admin' | 'unit' // 管理机构 | 下级单位
}
```

### 2. 背审任务相关

```typescript
// 背审任务
interface BackgroundCheckTask {
  id: string
  taskNo: string // 任务编号
  title: string
  description: string
  type: 'background_check' | 'processing' // 背审任务 | 处理任务
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // 任务相关人员
  personnelIds: string[]
  personnelCount: number
  
  // 任务发起方（管理员）
  createdBy: string
  createdByName: string
  createdByOrg: string
  
  // 任务接收方（下级单位）
  assignedTo: string
  assignedToName: string
  assignedToOrg: string
  
  // 时间信息
  createdAt: string
  assignedAt?: string
  dueDate?: string
  completedAt?: string
  
  // 任务结果
  result?: TaskResult
}

// 任务结果
interface TaskResult {
  id: string
  taskId: string
  status: 'submitted' | 'approved' | 'rejected'
  content: string
  attachments?: string[]
  submittedBy: string
  submittedAt: string
  reviewedBy?: string
  reviewedAt?: string
  reviewComments?: string
}
```

### 3. 人员处理记录

```typescript
// 人员处理记录
interface PersonnelProcessingRecord {
  id: string
  personnelId: string
  taskId?: string // 关联的任务ID
  processingType: 'background_check' | 'focus' | 'transfer' | 'dismiss'
  status: 'pending' | 'in_progress' | 'completed'
  
  // 处理结果
  result: 'normal' | 'focus' | 'transfer' | 'dismiss'
  reason: string
  description: string
  
  // 处理人员
  processedBy: string
  processedByName: string
  processedByOrg: string
  processedAt: string
  
  // 审核信息
  reviewedBy?: string
  reviewedByName?: string
  reviewedAt?: string
  reviewComments?: string
}
```

## 🔧 技术实现方案

### 1. 权限管理实现

#### Mock权限服务
```typescript
// src/services/authService.ts
class AuthService {
  private mockUserRole: 'admin' | 'unit' = 'admin' // 可手动切换
  
  async getCurrentUser(): Promise<User> {
    // Mock实现，返回当前用户信息
  }
  
  isAdmin(): boolean {
    return this.mockUserRole === 'admin'
  }
  
  // 手动切换角色（开发阶段使用）
  switchRole(role: 'admin' | 'unit') {
    this.mockUserRole = role
    // 触发页面重新渲染
  }
}
```

#### 路由守卫
```typescript
// src/router/guards.ts
router.beforeEach(async (to, from, next) => {
  const authService = useAuthService()
  const user = await authService.getCurrentUser()
  
  // 根据用户角色重定向到对应的工作台
  if (to.path === '/') {
    if (user.isAdmin) {
      next('/admin/dashboard')
    } else {
      next('/unit/dashboard')
    }
  }
  
  // 权限检查
  if (to.meta.requiresAdmin && !user.isAdmin) {
    next('/403')
  }
  
  next()
})
```

### 2. 组件复用策略

#### 人员管理组件复用
```typescript
// 复用现有PersonnelTable组件，通过props控制显示内容
interface PersonnelTableProps {
  personnelType?: 'security' | 'guard' | 'logistics' // 人员类型过滤
  showActions?: string[] // 控制显示的操作按钮
  selectable?: boolean // 是否支持多选
  viewMode?: 'admin' | 'unit' // 视图模式
}
```

#### 任务管理组件设计
```typescript
// 新增任务管理相关组件
- TaskList.vue // 任务列表
- TaskDetail.vue // 任务详情
- TaskCreate.vue // 创建任务
- TaskProcess.vue // 处理任务
- TaskResult.vue // 任务结果
```

### 3. API接口设计

#### 权限相关接口
```typescript
// 获取当前用户信息
GET /api/auth/current-user

// 获取用户权限
GET /api/auth/permissions

// 切换角色（Mock）
POST /api/auth/switch-role
```

#### 任务管理接口
```typescript
// 获取任务列表
GET /api/tasks?type=background_check&status=pending&page=1&size=20

// 创建背审任务
POST /api/tasks/background-check

// 创建处理任务  
POST /api/tasks/processing

// 提交任务结果
POST /api/tasks/{taskId}/result

// 审核任务结果
PUT /api/tasks/{taskId}/review
```

#### 人员管理接口调整
```typescript
// 按类型获取人员列表
GET /api/personnel?type=security&organization=xxx&page=1&size=20

// 批量操作人员
POST /api/personnel/batch-operation
```

## 📅 开发计划

### 第一阶段：权限管理和基础架构（预计3天）
1. **权限服务设计和实现**
   - Mock权限服务
   - 角色切换功能
   - 路由守卫实现

2. **菜单结构调整**
   - 管理员菜单配置
   - 下级单位菜单配置
   - 动态菜单渲染

3. **工作台差异化**
   - 管理员工作台
   - 下级单位工作台

### 第二阶段：人员管理重构（预计2天）
1. **人员分类展示**
   - 专职保卫页面
   - 保安人员页面
   - 物流人员页面

2. **组件优化**
   - PersonnelTable组件增强
   - 多选功能实现
   - 操作按钮配置化

### 第三阶段：任务管理功能（预计5天）
1. **管理员端任务管理**
   - 待背审任务管理
   - 待处理任务管理
   - 已处理任务查询

2. **下级单位端工作处理**
   - 待背审任务处理
   - 待处理任务处理
   - 已处理任务查询

3. **任务流程实现**
   - 任务创建流程
   - 任务分配流程
   - 任务处理流程
   - 任务审核流程

### 第四阶段：数据结构和API（预计2天）
1. **数据结构调整**
   - 任务相关表结构
   - 处理记录表结构
   - 权限相关表结构

2. **API接口实现**
   - 任务管理接口
   - 权限管理接口
   - 人员管理接口调整

### 第五阶段：测试和优化（预计2天）
1. **功能测试**
   - 权限控制测试
   - 任务流程测试
   - 数据权限测试

2. **性能优化**
   - 列表查询优化
   - 权限检查优化
   - 前端渲染优化

## 🔍 关键技术点

### 1. 权限控制粒度
- **页面级权限**: 通过路由守卫控制页面访问
- **功能级权限**: 通过组件props控制功能显示
- **数据级权限**: 通过API参数控制数据范围

### 2. 状态管理
- **用户状态**: 使用Pinia管理用户信息和权限
- **任务状态**: 管理任务列表和处理状态
- **权限状态**: 缓存权限信息，减少API调用

### 3. 组件设计原则
- **高复用性**: 现有组件最大化复用
- **配置化**: 通过props控制组件行为
- **模块化**: 新功能独立组件，便于维护

## 📈 预期效果

### 1. 用户体验提升
- **角色明确**: 不同角色看到不同的界面和功能
- **操作简化**: 根据权限显示相关操作，减少混淆
- **流程清晰**: 任务管理流程明确，状态跟踪完整

### 2. 系统架构优化
- **权限清晰**: 基于角色的权限管理，安全可控
- **扩展性强**: 模块化设计，便于后续功能扩展
- **维护性好**: 代码结构清晰，组件复用度高

### 3. 业务价值实现
- **管理效率**: 管理员可统一管理和分配任务
- **处理效率**: 下级单位可快速处理分配的任务
- **追溯性**: 完整的任务和处理记录，便于追溯

## 🚀 后续扩展规划

### 短期扩展（1-2个月）
- **消息通知**: 任务分配和状态变更通知
- **报表增强**: 任务处理效率统计
- **移动端**: 移动端任务处理功能

### 中期扩展（3-6个月）
- **工作流引擎**: 可配置的任务处理流程
- **智能分析**: 基于历史数据的风险预警
- **集成对接**: 与其他系统的数据对接

### 长期规划（6个月以上）
- **AI辅助**: 智能背审结果分析
- **大数据分析**: 跨区域数据分析
- **云端部署**: 支持多租户的云端部署
