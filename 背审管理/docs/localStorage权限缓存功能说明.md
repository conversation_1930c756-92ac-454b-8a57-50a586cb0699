# localStorage权限缓存功能说明

## 📋 功能概述

已成功将Mock权限数据改为使用localStorage存储，解决了页面刷新后权限状态丢失的问题。现在用户的角色选择和权限信息会持久化保存在浏览器本地存储中。

## ✅ 实现的功能

### 1. localStorage工具类

**文件**: `src/utils/storage.ts`

**主要功能**:
- ✅ 统一的localStorage操作接口
- ✅ 数据过期时间管理
- ✅ 自动JSON序列化/反序列化
- ✅ 错误处理和异常捕获
- ✅ 带前缀的键名管理

**使用示例**:
```typescript
import { storage, STORAGE_KEYS } from '@/utils/storage'

// 设置数据（24小时过期）
storage.set(STORAGE_KEYS.CURRENT_ROLE, 'admin', 24 * 60 * 60 * 1000)

// 获取数据
const role = storage.get<UserRole>(STORAGE_KEYS.CURRENT_ROLE)

// 检查是否过期
const isExpired = storage.isExpired(STORAGE_KEYS.USER_INFO)

// 清除所有数据
storage.clear()
```

### 2. 权限服务localStorage集成

**文件**: `src/services/authService.ts`

**主要改进**:
- ✅ 角色信息持久化存储
- ✅ 用户信息缓存机制
- ✅ 自动缓存过期检查
- ✅ 角色切换时自动更新缓存
- ✅ 缓存状态查询功能

**核心方法**:
```typescript
// 获取当前角色（从localStorage）
private getCurrentRole(): UserRole {
  return storage.get<UserRole>(STORAGE_KEYS.CURRENT_ROLE) || 'admin'
}

// 切换角色（保存到localStorage）
switchRole(role: UserRole): void {
  this.setCurrentRole(role)
  storage.remove(STORAGE_KEYS.USER_INFO)
  // ... 更新缓存逻辑
}

// 获取缓存状态
getCacheStatus(): { hasCache: boolean; timestamp: number | null; isExpired: boolean }
```

### 3. 存储键管理

**存储键定义**:
```typescript
export const STORAGE_KEYS = {
  CURRENT_ROLE: 'current_role',      // 当前用户角色
  USER_INFO: 'user_info',            // 用户信息
  PERMISSIONS: 'permissions',         // 权限列表
  LAST_LOGIN: 'last_login'           // 最后登录时间
} as const
```

**实际存储键**（带前缀）:
- `auth_system_current_role`
- `auth_system_user_info`
- `auth_system_permissions`
- `auth_system_last_login`

### 4. 缓存策略

**缓存时间**: 24小时
**缓存内容**:
- 用户角色信息
- 用户基本信息
- 权限列表
- 组织信息

**缓存更新时机**:
- 角色切换时
- 用户信息刷新时
- 缓存过期时自动更新

## 🎯 使用效果

### 1. 持久化角色状态

**之前的问题**:
```
用户选择角色 → 刷新页面 → 角色重置为默认值 ❌
```

**现在的效果**:
```
用户选择角色 → 保存到localStorage → 刷新页面 → 角色保持不变 ✅
```

### 2. 缓存状态可视化

在权限测试页面 (`/auth-test`) 可以看到：
- **有缓存**: 显示是否存在localStorage缓存
- **缓存时间**: 显示缓存创建的具体时间
- **是否过期**: 显示缓存是否已过期
- **操作按钮**: 清除缓存、刷新状态

### 3. 自动缓存管理

```typescript
// 获取用户信息时的缓存逻辑
async getCurrentUser(): Promise<UserInfo> {
  // 1. 先检查缓存
  const cachedUser = storage.get<UserInfo>(STORAGE_KEYS.USER_INFO)
  if (cachedUser && !storage.isExpired(STORAGE_KEYS.USER_INFO)) {
    return cachedUser // 使用缓存
  }
  
  // 2. 缓存无效，重新获取
  const user = this.getMockUser()
  
  // 3. 更新缓存
  storage.set(STORAGE_KEYS.USER_INFO, user, this.CACHE_EXPIRY)
  
  return user
}
```

## 🔧 技术实现

### 1. 数据结构

```typescript
interface StorageItem<T = any> {
  value: T              // 实际数据
  timestamp: number     // 存储时间戳
  expiry?: number      // 过期时间（毫秒）
}
```

### 2. 存储格式

localStorage中的实际存储格式：
```json
{
  "value": {
    "userId": "admin-001",
    "username": "admin",
    "name": "系统管理员",
    "roles": ["admin"],
    "permissions": ["personnel:view:all", "task:manage"]
  },
  "timestamp": 1703123456789,
  "expiry": 86400000
}
```

### 3. 错误处理

```typescript
try {
  localStorage.setItem(key, value)
} catch (error) {
  console.error('localStorage 设置失败:', error)
  // 降级处理：使用内存存储
}
```

## 🎮 测试方法

### 1. 基本功能测试

1. **访问测试页面**: `http://localhost:5173/auth-test`
2. **切换角色**: 使用右上角的角色切换器
3. **刷新页面**: 验证角色是否保持
4. **查看缓存状态**: 观察localStorage状态区域

### 2. 缓存管理测试

1. **清除缓存**: 点击"清除缓存"按钮
2. **刷新状态**: 点击"刷新状态"按钮
3. **过期测试**: 等待缓存过期（或手动修改过期时间）

### 3. 开发者工具验证

打开浏览器开发者工具 → Application → Local Storage：
```
Key: auth_system_current_role
Value: "admin"

Key: auth_system_user_info  
Value: {"value":{"userId":"admin-001",...},"timestamp":1703123456789,"expiry":86400000}
```

## 🚀 优势特性

### 1. 用户体验提升
- ✅ 页面刷新后状态保持
- ✅ 无需重新选择角色
- ✅ 快速加载用户信息

### 2. 开发体验优化
- ✅ 开发过程中状态持久化
- ✅ 调试时状态可控
- ✅ 缓存状态可视化

### 3. 性能优化
- ✅ 减少重复的权限检查
- ✅ 本地缓存提升响应速度
- ✅ 智能缓存更新策略

### 4. 可维护性
- ✅ 统一的存储接口
- ✅ 完善的错误处理
- ✅ 清晰的缓存管理

## 🔍 调试功能

### 1. 控制台调试

```javascript
// 在浏览器控制台中
import { storage, STORAGE_KEYS } from '@/utils/storage'

// 查看当前角色
console.log('当前角色:', storage.get(STORAGE_KEYS.CURRENT_ROLE))

// 查看用户信息
console.log('用户信息:', storage.get(STORAGE_KEYS.USER_INFO))

// 查看缓存状态
console.log('缓存状态:', authService.getCacheStatus())
```

### 2. 权限测试页面

访问 `/auth-test` 页面可以：
- 实时查看localStorage状态
- 手动清除缓存测试
- 观察缓存时间和过期状态
- 验证角色切换效果

## 📝 注意事项

### 1. 浏览器兼容性
- localStorage在所有现代浏览器中都支持
- 隐私模式下可能有限制
- 存储空间通常为5-10MB

### 2. 数据安全
- localStorage数据明文存储
- 不适合存储敏感信息
- 用户可以手动清除数据

### 3. 缓存策略
- 默认24小时过期
- 角色切换时自动清除相关缓存
- 支持手动清除所有缓存

## 🎉 总结

localStorage权限缓存功能已成功实现，主要改进包括：

✅ **持久化存储**: 角色和权限信息不再因页面刷新而丢失  
✅ **智能缓存**: 自动管理缓存过期和更新  
✅ **可视化调试**: 提供缓存状态查看和管理功能  
✅ **错误处理**: 完善的异常处理和降级方案  
✅ **开发友好**: 便于开发和调试的工具函数  

这个改进大大提升了开发体验和用户体验，为后续功能开发提供了更稳定的权限基础。
