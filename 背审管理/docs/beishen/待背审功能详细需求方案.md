# 待背审功能详细需求方案

## 📋 需求概述

基于现有的背审管理系统，设计一套完整的背审任务管理功能，支持人员背审状态管理、任务发起、进度跟踪和历史记录查询等核心功能。

## 🎯 核心需求分析

### 1. 背审人员状态管理

#### 1.1 人员状态分类
- **待背审**: 新入职或需要重新背审的人员
- **背审中**: 已发起背审任务，正在处理中的人员
- **已完成背审**: 背审任务已完成的人员

#### 1.2 状态流转逻辑
```
待背审 → [发起背审任务] → 背审中 → [完成背审] → 已完成背审
   ↑                        ↓                      ↓
   └─── [撤销任务] ←─────────┘      [重新发起] ─────┘
```

### 2. 背审任务操作需求

#### 2.1 发起背审任务
**触发条件**: 人员状态为"待背审"
**操作方式**:
- **选择性发起**: 勾选特定人员后批量发起
- **条件性发起**: 基于当前筛选条件批量发起所有符合条件的人员

**发起参数**:
- 处理人员/部门选择
- 任务截止时间
- 任务优先级
- 任务描述/备注

#### 2.2 撤销背审任务
**触发条件**: 人员状态为"背审中"
**操作方式**:
- **选择性撤销**: 勾选特定人员后批量撤销
- **条件性撤销**: 基于当前筛选条件批量撤销

**撤销效果**: 人员状态回到"待背审"，任务状态变为"已取消"

#### 2.3 重新发起背审
**触发条件**: 人员状态为"已完成背审"
**操作方式**: 同发起背审任务
**重新发起效果**: 人员状态变为"背审中"，创建新的背审任务

### 3. 历史记录查询需求

#### 3.1 人员背审历史
- 查看每个人员的所有背审记录
- 包括任务发起时间、处理人、完成时间、结果等
- 支持时间范围筛选和状态筛选

#### 3.2 任务执行历史
- 查看任务的完整执行过程
- 包括任务创建、分配、处理、完成的时间线
- 支持任务状态变更记录

### 4. 背审任务视图需求

#### 4.1 任务概览
- 显示所有发起的背审任务
- 任务完成进度统计
- 逾期任务提醒
- 任务状态分布

#### 4.2 任务详情
- 任务基本信息（编号、标题、创建时间等）
- 关联人员列表及处理状态
- 任务执行进度
- 处理结果汇总

#### 4.3 催办功能
- 对逾期或即将逾期的任务发送催办通知
- 支持批量催办
- 催办记录追踪

## 🎨 页面设计方案

### 1. 菜单结构设计

#### 管理员菜单（最小化原则）
```
背审管理
├── 背审人员 (主要页面)
└── 背审任务 (任务视图)
```

#### 下级单位菜单
```
背审工作
├── 待处理背审 (接收到的背审任务)
└── 背审记录 (已处理的背审记录)
```

### 2. 主要页面设计

#### 2.1 背审人员管理页面 (管理员)
**页面路径**: `/background-check/personnel`

**页面布局**:
```
┌─────────────────────────────────────────────────────────┐
│ 筛选条件区域                                              │
│ [人员类型] [背审状态] [组织] [时间范围] [搜索] [重置]        │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│ 操作工具栏                                               │
│ [发起背审] [撤销任务] [重新发起] [导出] [批量操作▼]        │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│ 状态统计卡片                                             │
│ [待背审: 123] [背审中: 45] [已完成: 678]                  │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│ 人员列表表格                                             │
│ □ 姓名 | 身份证 | 人员类型 | 背审状态 | 最后背审时间 | 操作 │
│ □ 张三 | 130*** | 专职保卫 | 待背审   | 2024-01-01  | 详情 │
│ □ 李四 | 130*** | 保安人员 | 背审中   | 2024-01-15  | 详情 │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│ 分页组件                                                 │
└─────────────────────────────────────────────────────────┘
```

**核心功能**:
- 状态切换Tab: 待背审 | 背审中 | 已完成背审
- 批量操作: 支持多选和条件筛选两种模式
- 快速筛选: 人员类型、组织、时间范围等
- 操作按钮: 根据当前状态显示对应操作

#### 2.2 背审任务管理页面 (管理员)
**页面路径**: `/background-check/tasks`

**页面布局**:
```
┌─────────────────────────────────────────────────────────┐
│ 任务概览卡片                                             │
│ [总任务: 89] [进行中: 23] [已完成: 45] [逾期: 3]          │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│ 筛选条件区域                                             │
│ [任务状态] [处理部门] [创建时间] [截止时间] [搜索] [重置]   │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│ 任务列表表格                                             │
│ 任务编号 | 任务标题 | 人员数量 | 处理部门 | 进度 | 状态 | 操作 │
│ BG001   | 新员工背审 | 15人    | 莲池分局 | 60% | 进行中 | 详情 │
│ BG002   | 定期背审   | 8人     | 竞秀分局 | 100%| 已完成 | 详情 │
└─────────────────────────────────────────────────────────┘
```

**核心功能**:
- 任务进度可视化
- 逾期任务高亮显示
- 催办功能
- 任务详情查看

#### 2.3 人员背审详情弹窗
**触发方式**: 点击人员列表中的"详情"按钮

**弹窗内容**:
```
┌─────────────────────────────────────────────────────────┐
│ 人员基本信息                                             │
│ 姓名: 张三    身份证: 130***    人员类型: 专职保卫        │
│ 部门: 保安部  入职时间: 2024-01-01                       │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│ 当前背审状态                                             │
│ 状态: 背审中  任务编号: BG001  截止时间: 2024-02-01      │
│ 处理部门: 莲池分局  发起时间: 2024-01-15                 │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│ 背审历史记录                                             │
│ 时间线展示所有背审记录                                   │
│ 2024-01-15: 发起背审任务 (BG001)                        │
│ 2023-06-01: 完成背审 (正常)                             │
│ 2023-01-01: 入职背审 (正常)                             │
└─────────────────────────────────────────────────────────┘
```

### 3. 数据流转设计

#### 3.1 背审状态流转
```mermaid
graph TD
    A[新增人员] --> B[待背审]
    B --> |发起背审任务| C[背审中]
    C --> |完成背审| D[已完成背审]
    C --> |撤销任务| B
    D --> |重新发起| C
```

#### 3.2 任务状态流转
```mermaid
graph TD
    A[创建任务] --> B[待分配]
    B --> |分配给部门| C[进行中]
    C --> |提交结果| D[待审核]
    D --> |审核通过| E[已完成]
    D --> |审核拒绝| C
    C --> |撤销任务| F[已取消]
```

## 📊 数据模型设计

### 1. 人员背审状态扩展
```typescript
interface PersonnelBackgroundCheck {
  personnelId: string
  currentStatus: 'pending' | 'in_progress' | 'completed'
  currentTaskId?: string
  lastCheckDate?: string
  nextCheckDate?: string
  checkCount: number
  riskLevel: 'low' | 'medium' | 'high'
}
```

### 2. 背审任务模型
```typescript
interface BackgroundCheckTask {
  id: string
  taskNo: string
  title: string
  description?: string
  type: 'background_check'
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // 人员信息
  personnelCount: number
  personnelIds: string[]
  
  // 任务分配
  assignedToOrg: string
  assignedToOrgName: string
  assignedToUser?: string
  assignedToUserName?: string
  
  // 时间管理
  createdAt: string
  assignedAt?: string
  dueDate: string
  completedAt?: string
  
  // 进度统计
  progress: {
    total: number
    completed: number
    pending: number
    percentage: number
  }
  
  // 逾期状态
  isOverdue: boolean
  overdueBy?: number // 逾期天数
}
```

### 3. 背审记录模型
```typescript
interface BackgroundCheckRecord {
  id: string
  personnelId: string
  taskId: string
  checkType: 'initial' | 'periodic' | 'special'
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  result?: 'normal' | 'abnormal' | 'risk'
  resultDetails?: string
  
  // 时间记录
  startedAt: string
  completedAt?: string
  
  // 处理信息
  processedBy?: string
  processedByName?: string
  processedByOrg?: string
  
  // 审核信息
  reviewedBy?: string
  reviewedByName?: string
  reviewedAt?: string
  reviewComments?: string
}
```

## 🔄 业务流程设计

### 1. 发起背审任务流程
```
1. 管理员进入背审人员页面
2. 筛选待背审人员
3. 选择发起方式（勾选 or 条件筛选）
4. 填写任务信息（处理部门、截止时间等）
5. 确认发起任务
6. 系统创建任务记录
7. 更新人员状态为"背审中"
8. 发送任务通知给处理部门
```

### 2. 处理背审任务流程（下级单位）
```
1. 下级单位接收背审任务通知
2. 进入待处理背审页面
3. 查看任务详情和人员列表
4. 逐个处理人员背审
5. 填写背审结果
6. 提交背审结果
7. 系统更新任务进度
8. 全部完成后任务状态变为"已完成"
```

### 3. 催办流程
```
1. 系统自动检测逾期任务
2. 管理员查看逾期任务列表
3. 选择需要催办的任务
4. 发送催办通知
5. 记录催办历史
6. 跟踪催办效果
```

## 📱 交互设计要点

### 1. 批量操作交互
- **勾选模式**: 用户手动勾选人员，操作按钮显示选中数量
- **筛选模式**: 用户设置筛选条件，操作按钮显示"对当前筛选结果"
- **确认机制**: 批量操作前必须二次确认，显示影响人员数量

### 2. 状态可视化
- **进度条**: 任务完成进度用进度条展示
- **状态标签**: 不同状态用不同颜色的标签区分
- **逾期提醒**: 逾期任务用红色高亮显示

### 3. 快速操作
- **一键发起**: 常用的发起背审操作提供快捷按钮
- **批量催办**: 支持一键催办所有逾期任务
- **快速筛选**: 提供常用筛选条件的快捷按钮

## 🎯 核心价值

1. **效率提升**: 支持批量操作，大幅提升背审任务处理效率
2. **流程规范**: 标准化的背审流程，确保操作规范性
3. **进度可控**: 实时跟踪任务进度，及时发现和处理问题
4. **历史追溯**: 完整的操作记录，支持审计和问题追查
5. **用户友好**: 简洁的界面设计，降低操作复杂度

这个需求方案涵盖了您提出的所有核心需求，并且在菜单设计上遵循了最小化原则。
