# 背审结果处理模块开发计划

## 项目概述

基于背审管理模块的成功经验，开发背审结果处理模块。该模块主要处理背审过程中发现的异常人员，提供完整的异常处理流程管理。

## 开发目标

### 主要目标
- 复用背审管理模块的成熟组件和设计模式
- 提供完整的异常人员处理流程
- 保持与背审管理模块一致的用户体验
- 实现高质量、可维护的代码

### 成功指标
- 组件复用率 > 80%
- 代码质量与背审模块保持一致
- 用户操作流程与背审模块相似
- 开发周期控制在预期范围内

## 技术方案

### 架构设计
```
result-processing/
├── components/              # 业务组件
│   ├── StartProcessingTaskDialog.vue    # 发起处理任务弹窗
│   ├── ProcessingTaskDetailDrawer.vue   # 处理任务详情抽屉
│   └── AbnormalPersonnelDetailDialog.vue # 异常人员详情弹窗
├── views/                   # 页面组件
│   ├── AbnormalPersonnelPage.vue        # 异常人员管理页面
│   └── ProcessingTaskPage.vue           # 处理任务管理页面
├── config/                  # 配置文件
│   └── resultProcessingConfig.ts        # 状态配置等
└── data/                    # Mock数据
    └── resultProcessingMockData.ts      # Mock数据定义
```

### 组件复用策略

#### 直接复用的组件
1. **SearchForm**: 搜索表单组件
2. **PersonnelAvatar**: 人员头像组件
3. **样式系统**: 复用所有CSS样式和布局

#### 改造复用的组件
1. **StartCheckTaskDialog** → **StartProcessingTaskDialog**
   - 调整表单字段（处理要求、处理期限等）
   - 保持相同的交互逻辑和UI设计

2. **TaskDetailDrawer** → **ProcessingTaskDetailDrawer**
   - 调整Tab内容（任务信息、处理历史、关联人员）
   - 保持相同的抽屉交互和布局

#### 新开发的组件
1. **AbnormalPersonnelDetailDialog**: 异常人员详情弹窗
2. **ProcessingHistoryTable**: 处理历史表格
3. **AbnormalInfoCard**: 异常信息卡片

## 详细开发计划

### 第一阶段：基础设施搭建 (2天)

#### Day 1: 项目结构和配置
**任务清单**:
- [ ] 创建目录结构
- [ ] 配置路由 (`/result-processing/personnel`, `/result-processing/tasks`)
- [ ] 设计数据模型接口
- [ ] 创建基础配置文件

**输出物**:
- 完整的目录结构
- 路由配置文件
- TypeScript接口定义
- 基础配置文件

#### Day 2: Mock数据设计
**任务清单**:
- [ ] 设计异常人员Mock数据
- [ ] 设计处理任务Mock数据
- [ ] 创建数据生成函数
- [ ] 测试数据完整性

**输出物**:
- `resultProcessingMockData.ts`
- 数据模型验证
- 基础数据测试

### 第二阶段：异常人员管理页面 (3天)

#### Day 3: 页面框架搭建
**任务清单**:
- [ ] 创建 `AbnormalPersonnelPage.vue`
- [ ] 复用SearchForm组件配置
- [ ] 实现基础布局和样式
- [ ] 配置状态标签页（待处理、已处理）

**技术要点**:
```vue
<!-- 搜索表单配置示例 -->
const searchFormConfig = computed(() => [
  [
    {
      key: 'name',
      type: 'input' as const,
      prop: 'name',
      label: '姓名',
      placeholder: '请输入姓名'
    },
    {
      key: 'abnormalType',
      type: 'select' as const,
      prop: 'abnormalType',
      label: '异常类型',
      options: abnormalTypeOptions
    },
    {
      key: 'processingStatus',
      type: 'select' as const,
      prop: 'processingStatus',
      label: '处理状态',
      options: processingStatusOptions
    },
    {
      key: 'discoveredTimeRange',
      type: 'daterange' as const,
      prop: 'discoveredTimeRange',
      label: '发现时间'
    }
  ]
])
```

#### Day 4: 表格和数据展示
**任务清单**:
- [ ] 实现人员列表表格
- [ ] 添加异常信息列
- [ ] 实现分页功能
- [ ] 复用PersonnelAvatar组件

**表格列设计**:
- 选择框
- 头像
- 姓名
- 身份证号（脱敏）
- 联系方式
- 所属机构
- 异常类型
- 异常等级
- 发现时间
- 处理状态
- 处理人
- 操作列

#### Day 5: 搜索和批量操作
**任务清单**:
- [ ] 实现搜索筛选功能
- [ ] 实现跨页面选择状态保持
- [ ] 添加批量操作按钮
- [ ] 创建批量下发处理任务功能

**复用逻辑**:
```typescript
// 复用背审模块的选择状态管理
const selectedPersonnelIdCards = ref<Set<string>>(new Set())

const handleSelectionChange = (selection: AbnormalPersonnel[]) => {
  // 复用相同的选择状态管理逻辑
}
```

### 第三阶段：处理任务管理页面 (3天)

#### Day 6: 任务页面基础
**任务清单**:
- [ ] 创建 `ProcessingTaskPage.vue`
- [ ] 复用任务列表布局
- [ ] 配置搜索表单
- [ ] 实现基础数据加载

#### Day 7: 任务列表和操作
**任务清单**:
- [ ] 实现任务列表表格
- [ ] 添加任务状态管理
- [ ] 实现任务撤销功能
- [ ] 添加催办功能

#### Day 8: 任务详情抽屉
**任务清单**:
- [ ] 改造TaskDetailDrawer组件
- [ ] 实现三个Tab页面内容
- [ ] 添加处理历史展示
- [ ] 实现关联人员管理

### 第四阶段：业务组件开发 (3天)

#### Day 9: 发起处理任务弹窗
**任务清单**:
- [ ] 改造StartCheckTaskDialog
- [ ] 调整表单字段和验证规则
- [ ] 实现处理任务创建逻辑
- [ ] 测试弹窗功能

**组件改造要点**:
```vue
<!-- 处理任务特有字段 -->
<el-form-item label="处理要求" prop="processingRequirement">
  <el-input
    v-model="formData.processingRequirement"
    type="textarea"
    placeholder="请输入具体的处理要求"
  />
</el-form-item>

<el-form-item label="处理期限" prop="processingDeadline">
  <el-date-picker
    v-model="formData.processingDeadline"
    type="datetime"
    placeholder="请选择处理期限"
  />
</el-form-item>
```

#### Day 10: 异常人员详情
**任务清单**:
- [ ] 创建异常人员详情弹窗
- [ ] 展示异常详细信息
- [ ] 显示处理历史
- [ ] 添加处理结果录入

#### Day 11: 处理历史组件
**任务清单**:
- [ ] 创建处理历史表格组件
- [ ] 实现时间线展示
- [ ] 添加处理结果查看
- [ ] 实现状态变更追踪

### 第五阶段：功能完善和测试 (2天)

#### Day 12: 功能集成测试
**任务清单**:
- [ ] 端到端功能测试
- [ ] 修复发现的问题
- [ ] 优化用户体验
- [ ] 性能优化

#### Day 13: 最终优化
**任务清单**:
- [ ] 代码审查和重构
- [ ] 文档完善
- [ ] 部署测试
- [ ] 用户验收准备

## 技术实现细节

### 1. 数据模型设计

#### 异常类型配置
```typescript
export const abnormalTypeConfig = {
  'criminal_record': { label: '犯罪记录', level: 'critical', color: 'danger' },
  'credit_issue': { label: '信用问题', level: 'high', color: 'warning' },
  'education_fraud': { label: '学历造假', level: 'medium', color: 'warning' },
  'work_experience': { label: '工作经历问题', level: 'low', color: 'info' }
}
```

#### 处理状态配置
```typescript
export const processingStatusConfig = {
  'pending': { label: '待处理', color: 'info' },
  'assigned': { label: '已分配', color: 'primary' },
  'processing': { label: '处理中', color: 'warning' },
  'completed': { label: '已完成', color: 'success' }
}
```

### 2. 组件复用映射

| 背审模块组件 | 结果处理模块组件 | 复用程度 | 改造内容 |
|-------------|-----------------|----------|----------|
| SearchForm | SearchForm | 100% | 仅调整配置项 |
| PersonnelAvatar | PersonnelAvatar | 100% | 无需改造 |
| StartCheckTaskDialog | StartProcessingTaskDialog | 80% | 调整表单字段 |
| TaskDetailDrawer | ProcessingTaskDetailDrawer | 70% | 调整Tab内容 |
| BackgroundCheckPersonnelPage | AbnormalPersonnelPage | 85% | 调整业务逻辑 |
| BackgroundCheckTaskPage | ProcessingTaskPage | 85% | 调整业务逻辑 |

### 3. 路由配置
```typescript
// router/index.ts
{
  path: '/result-processing',
  name: 'ResultProcessing',
  meta: { title: '背审结果处理' },
  children: [
    {
      path: 'personnel',
      name: 'AbnormalPersonnel',
      component: () => import('@/views/result-processing/AbnormalPersonnelPage.vue'),
      meta: { title: '异常人员管理' }
    },
    {
      path: 'tasks',
      name: 'ProcessingTasks',
      component: () => import('@/views/result-processing/ProcessingTaskPage.vue'),
      meta: { title: '处理任务管理' }
    }
  ]
}
```

## 质量保证

### 代码质量标准
- 遵循背审模块的编码规范
- TypeScript类型覆盖率 > 95%
- 组件复用率 > 80%
- 代码注释覆盖率 > 60%

### 测试策略
- 单元测试：核心业务逻辑
- 集成测试：组件交互
- 端到端测试：完整业务流程
- 性能测试：大数据量场景

### 用户体验标准
- 页面加载时间 < 2秒
- 操作响应时间 < 1秒
- 界面风格与背审模块保持一致
- 操作流程直观易懂

## 风险管控

### 技术风险
1. **组件兼容性风险**
   - 风险：复用组件可能不完全适配新业务
   - 缓解：充分测试，必要时进行适配改造

2. **数据模型复杂性风险**
   - 风险：异常处理的数据关系较复杂
   - 缓解：详细设计数据模型，充分验证

### 进度风险
1. **需求变更风险**
   - 风险：开发过程中业务需求可能变化
   - 缓解：保持与业务方密切沟通，采用迭代开发

2. **技术难点风险**
   - 风险：某些功能实现可能比预期复杂
   - 缓解：预留缓冲时间，及时调整计划

## 成功标准

### 功能完整性
- [ ] 异常人员管理功能完整
- [ ] 处理任务管理功能完整
- [ ] 批量操作功能正常
- [ ] 搜索筛选功能准确

### 性能指标
- [ ] 页面加载性能达标
- [ ] 大数据量处理流畅
- [ ] 内存使用合理

### 用户体验
- [ ] 界面美观一致
- [ ] 操作流程顺畅
- [ ] 错误处理友好
- [ ] 响应式设计良好

### 代码质量
- [ ] 代码结构清晰
- [ ] 组件复用充分
- [ ] 类型安全完整
- [ ] 文档完善

## 后续规划

### 短期优化 (1-2周)
- 根据用户反馈优化交互体验
- 修复发现的问题和缺陷
- 完善错误处理机制

### 中期扩展 (1-2月)
- 添加更多异常类型支持
- 增强报表和统计功能
- 集成消息通知系统

### 长期发展 (3-6月)
- 与其他系统集成
- 添加移动端支持
- 引入AI辅助处理功能

## 详细开发计划

### 第一阶段：基础设施搭建 (2天)

#### Day 1: 项目结构和配置
**任务清单**:
- [ ] 创建目录结构
- [ ] 配置路由 (`/result-processing/personnel`, `/result-processing/tasks`)
- [ ] 设计数据模型接口
- [ ] 创建基础配置文件

**输出物**:
- 完整的目录结构
- 路由配置文件
- TypeScript接口定义
- 基础配置文件

#### Day 2: Mock数据设计
**任务清单**:
- [ ] 设计异常人员Mock数据
- [ ] 设计处理任务Mock数据
- [ ] 创建数据生成函数
- [ ] 测试数据完整性

**输出物**:
- `resultProcessingMockData.ts`
- 数据模型验证
- 基础数据测试

### 第二阶段：异常人员管理页面 (3天)

#### Day 3: 页面框架搭建
**任务清单**:
- [ ] 创建 `AbnormalPersonnelPage.vue`
- [ ] 复用SearchForm组件配置
- [ ] 实现基础布局和样式
- [ ] 配置状态标签页（待处理、已处理）

**技术要点**:
```vue
<!-- 搜索表单配置示例 -->
const searchFormConfig = computed(() => [
  [
    {
      key: 'name',
      type: 'input' as const,
      prop: 'name',
      label: '姓名',
      placeholder: '请输入姓名'
    },
    {
      key: 'abnormalType',
      type: 'select' as const,
      prop: 'abnormalType',
      label: '异常类型',
      options: abnormalTypeOptions
    },
    {
      key: 'processingStatus',
      type: 'select' as const,
      prop: 'processingStatus',
      label: '处理状态',
      options: processingStatusOptions
    },
    {
      key: 'discoveredTimeRange',
      type: 'daterange' as const,
      prop: 'discoveredTimeRange',
      label: '发现时间'
    }
  ]
])
```

#### Day 4: 表格和数据展示
**任务清单**:
- [ ] 实现人员列表表格
- [ ] 添加异常信息列
- [ ] 实现分页功能
- [ ] 复用PersonnelAvatar组件

**表格列设计**:
- 选择框
- 头像
- 姓名
- 身份证号（脱敏）
- 联系方式
- 所属机构
- 异常类型
- 异常等级
- 发现时间
- 处理状态
- 处理人
- 操作列

#### Day 5: 搜索和批量操作
**任务清单**:
- [ ] 实现搜索筛选功能
- [ ] 实现跨页面选择状态保持
- [ ] 添加批量操作按钮
- [ ] 创建批量下发处理任务功能

**复用逻辑**:
```typescript
// 复用背审模块的选择状态管理
const selectedPersonnelIdCards = ref<Set<string>>(new Set())

const handleSelectionChange = (selection: AbnormalPersonnel[]) => {
  // 复用相同的选择状态管理逻辑
}
```

### 第三阶段：处理任务管理页面 (3天)

#### Day 6: 任务页面基础
**任务清单**:
- [ ] 创建 `ProcessingTaskPage.vue`
- [ ] 复用任务列表布局
- [ ] 配置搜索表单
- [ ] 实现基础数据加载

#### Day 7: 任务列表和操作
**任务清单**:
- [ ] 实现任务列表表格
- [ ] 添加任务状态管理
- [ ] 实现任务撤销功能
- [ ] 添加催办功能

#### Day 8: 任务详情抽屉
**任务清单**:
- [ ] 改造TaskDetailDrawer组件
- [ ] 实现三个Tab页面内容
- [ ] 添加处理历史展示
- [ ] 实现关联人员管理

### 第四阶段：业务组件开发 (3天)

#### Day 9: 发起处理任务弹窗
**任务清单**:
- [ ] 改造StartCheckTaskDialog
- [ ] 调整表单字段和验证规则
- [ ] 实现处理任务创建逻辑
- [ ] 测试弹窗功能

**组件改造要点**:
```vue
<!-- 处理任务特有字段 -->
<el-form-item label="处理要求" prop="processingRequirement">
  <el-input
    v-model="formData.processingRequirement"
    type="textarea"
    placeholder="请输入具体的处理要求"
  />
</el-form-item>

<el-form-item label="处理期限" prop="processingDeadline">
  <el-date-picker
    v-model="formData.processingDeadline"
    type="datetime"
    placeholder="请选择处理期限"
  />
</el-form-item>
```

#### Day 10: 异常人员详情
**任务清单**:
- [ ] 创建异常人员详情弹窗
- [ ] 展示异常详细信息
- [ ] 显示处理历史
- [ ] 添加处理结果录入

#### Day 11: 处理历史组件
**任务清单**:
- [ ] 创建处理历史表格组件
- [ ] 实现时间线展示
- [ ] 添加处理结果查看
- [ ] 实现状态变更追踪

### 第五阶段：功能完善和测试 (2天)

#### Day 12: 功能集成测试
**任务清单**:
- [ ] 端到端功能测试
- [ ] 修复发现的问题
- [ ] 优化用户体验
- [ ] 性能优化

#### Day 13: 最终优化
**任务清单**:
- [ ] 代码审查和重构
- [ ] 文档完善
- [ ] 部署测试
- [ ] 用户验收准备

## 技术实现细节

### 1. 数据模型设计

#### 异常类型配置
```typescript
export const abnormalTypeConfig = {
  'criminal_record': { label: '犯罪记录', level: 'critical', color: 'danger' },
  'credit_issue': { label: '信用问题', level: 'high', color: 'warning' },
  'education_fraud': { label: '学历造假', level: 'medium', color: 'warning' },
  'work_experience': { label: '工作经历问题', level: 'low', color: 'info' }
}
```

#### 处理状态配置
```typescript
export const processingStatusConfig = {
  'pending': { label: '待处理', color: 'info' },
  'assigned': { label: '已分配', color: 'primary' },
  'processing': { label: '处理中', color: 'warning' },
  'completed': { label: '已完成', color: 'success' }
}
```

### 2. 组件复用映射

| 背审模块组件 | 结果处理模块组件 | 复用程度 | 改造内容 |
|-------------|-----------------|----------|----------|
| SearchForm | SearchForm | 100% | 仅调整配置项 |
| PersonnelAvatar | PersonnelAvatar | 100% | 无需改造 |
| StartCheckTaskDialog | StartProcessingTaskDialog | 80% | 调整表单字段 |
| TaskDetailDrawer | ProcessingTaskDetailDrawer | 70% | 调整Tab内容 |
| BackgroundCheckPersonnelPage | AbnormalPersonnelPage | 85% | 调整业务逻辑 |
| BackgroundCheckTaskPage | ProcessingTaskPage | 85% | 调整业务逻辑 |

### 3. 路由配置
```typescript
// router/index.ts
{
  path: '/result-processing',
  name: 'ResultProcessing',
  meta: { title: '背审结果处理' },
  children: [
    {
      path: 'personnel',
      name: 'AbnormalPersonnel',
      component: () => import('@/views/result-processing/AbnormalPersonnelPage.vue'),
      meta: { title: '异常人员管理' }
    },
    {
      path: 'tasks',
      name: 'ProcessingTasks',
      component: () => import('@/views/result-processing/ProcessingTaskPage.vue'),
      meta: { title: '处理任务管理' }
    }
  ]
}
```

## 质量保证

### 代码质量标准
- 遵循背审模块的编码规范
- TypeScript类型覆盖率 > 95%
- 组件复用率 > 80%
- 代码注释覆盖率 > 60%

### 测试策略
- 单元测试：核心业务逻辑
- 集成测试：组件交互
- 端到端测试：完整业务流程
- 性能测试：大数据量场景

### 用户体验标准
- 页面加载时间 < 2秒
- 操作响应时间 < 1秒
- 界面风格与背审模块保持一致
- 操作流程直观易懂

## 风险管控

### 技术风险
1. **组件兼容性风险**
   - 风险：复用组件可能不完全适配新业务
   - 缓解：充分测试，必要时进行适配改造

2. **数据模型复杂性风险**
   - 风险：异常处理的数据关系较复杂
   - 缓解：详细设计数据模型，充分验证

### 进度风险
1. **需求变更风险**
   - 风险：开发过程中业务需求可能变化
   - 缓解：保持与业务方密切沟通，采用迭代开发

2. **技术难点风险**
   - 风险：某些功能实现可能比预期复杂
   - 缓解：预留缓冲时间，及时调整计划

## 成功标准

### 功能完整性
- [ ] 异常人员管理功能完整
- [ ] 处理任务管理功能完整
- [ ] 批量操作功能正常
- [ ] 搜索筛选功能准确

### 性能指标
- [ ] 页面加载性能达标
- [ ] 大数据量处理流畅
- [ ] 内存使用合理

### 用户体验
- [ ] 界面美观一致
- [ ] 操作流程顺畅
- [ ] 错误处理友好
- [ ] 响应式设计良好

### 代码质量
- [ ] 代码结构清晰
- [ ] 组件复用充分
- [ ] 类型安全完整
- [ ] 文档完善

## 后续规划

### 短期优化 (1-2周)
- 根据用户反馈优化交互体验
- 修复发现的问题和缺陷
- 完善错误处理机制

### 中期扩展 (1-2月)
- 添加更多异常类型支持
- 增强报表和统计功能
- 集成消息通知系统

### 长期发展 (3-6月)
- 与其他系统集成
- 添加移动端支持
- 引入AI辅助处理功能
