# 统一人员详情抽屉组件设计文档

## 🎯 设计目标

创建一个统一的人员详情抽屉组件，支持背审人员和异常人员两种模式，提供一致的用户体验和信息展示。

## 📋 功能需求

### 1. 双模式支持
- **背审模式** (`background-check`): 展示背审人员的详细信息
- **结果处理模式** (`result-processing`): 展示异常人员的详细信息

### 2. 四Tab设计
- **基本信息**: 人员基础信息展示
- **当前任务**: 根据模式展示当前背审或处理任务
- **背审历史**: 背景调查的历史记录
- **处理历史**: 异常处理的历史记录

### 3. 醒目状态显示
- 在人员头部区域显示醒目的当前状态标签
- 支持多种状态：背审中、背审完成、待处理、已处理

## 🏗️ 组件架构

### 组件文件位置
```
src/components/common/UnifiedPersonnelDetailDrawer.vue
```

### Props接口
```typescript
interface Props {
  modelValue: boolean                           // 抽屉显示状态
  personnelId?: string                         // 人员ID
  mode: 'background-check' | 'result-processing'  // 模式
}
```

### Emits事件
```typescript
const emit = defineEmits<{
  'update:modelValue': [value: boolean]        // 更新显示状态
  'view-task': [taskId: string]               // 查看任务详情
}>()
```

## 🎨 UI设计

### 1. 抽屉头部
```vue
<div class="personnel-header">
  <div class="avatar-section">
    <PersonnelAvatar :src="personnelData.avatar" :name="personnelData.name" :size="60" />
  </div>
  <div class="basic-info">
    <h3 class="personnel-name">{{ personnelData.name }}</h3>
    <div class="personnel-meta">
      <!-- 人员类型/异常等级标签 -->
      <el-tag>{{ getTypeText() }}</el-tag>
      
      <!-- 醒目的当前状态标签 -->
      <el-tag 
        :type="getCurrentStatusColor()" 
        size="large"
        class="status-tag-prominent"
        effect="dark"
      >
        {{ getCurrentStatusText() }}
      </el-tag>
    </div>
  </div>
</div>
```

### 2. 醒目状态标签样式
```css
.status-tag-prominent {
  font-size: 14px !important;
  font-weight: 600 !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-left: 8px;
}
```

### 3. Tab内容设计

#### Tab 1: 基本信息
- **背审模式**: 姓名、身份证号、人员类型、所属部门、职位、入职时间、联系电话、风险等级、背审次数
- **结果处理模式**: 姓名、身份证号、联系方式、所属机构、异常类型、异常等级、发现时间、背审任务、异常描述

#### Tab 2: 当前任务
- **背审模式**: 
  - 背审中：任务编号、任务状态、处理部门、发起时间、截止时间
  - 其他状态：当前状态、最后背审时间、下次背审时间
- **结果处理模式**:
  - 处理中：任务编号、处理状态、处理人、分配时间
  - 已完成：处理状态、处理人、完成时间、处理结果、处理说明
  - 待处理：当前状态、发现时间、背审结果

#### Tab 3: 背审历史
- 时间线展示背景调查的历史记录
- 包含发起、进行中、完成等状态节点
- 显示操作人、时间、结果等信息

#### Tab 4: 处理历史
- 时间线展示异常处理的历史记录
- 包含发现异常、分配任务、处理完成等节点
- 显示操作人、时间、处理说明等信息

## 🔧 技术实现

### 1. 状态判断逻辑
```typescript
const getCurrentStatusText = () => {
  if (props.mode === 'background-check') {
    const data = personnelData.value as PersonnelData
    switch (data.backgroundCheckStatus) {
      case 'in_progress': return '背审中'
      case 'completed': return '背审完成'
      case 'pending': return '待背审'
      default: return data.backgroundCheckStatus
    }
  } else {
    const data = personnelData.value as AbnormalPersonnel
    switch (data.processingStatus) {
      case 'pending': return '待处理'
      case 'processing': return '处理中'
      case 'completed': return '已处理'
      default: return data.processingStatus
    }
  }
}
```

### 2. 动态数据加载
```typescript
const loadPersonnelData = () => {
  if (!props.personnelId) return
  
  if (props.mode === 'background-check') {
    const personnel = personnelMockData.find(p => p.id === parseInt(props.personnelId!))
    if (personnel) {
      personnelData.value = personnel
    }
  } else {
    const personnel = abnormalPersonnelMockData.find(p => p.id === props.personnelId)
    if (personnel) {
      personnelData.value = personnel
    }
  }
}
```

### 3. 历史记录生成
```typescript
const backgroundHistory = computed(() => {
  if (!personnelData.value) return []
  
  const history = []
  
  if (props.mode === 'background-check') {
    // 背审人员的背审历史
    history.push({
      time: '2024-01-15 09:00:00',
      type: 'start',
      content: '发起背景调查',
      operator: '张管理员',
      result: '进行中',
      resultType: 'primary'
    })
  } else {
    // 异常人员的背审历史
    const abnormalData = personnelData.value as AbnormalPersonnel
    history.push({
      time: abnormalData.discoveredAt,
      type: 'discover',
      content: `发现异常：${abnormalData.abnormalDescription}`,
      operator: '系统检测',
      result: abnormalData.backgroundCheckResult,
      resultType: 'danger'
    })
  }
  
  return history.reverse()
})
```

## 📱 响应式设计

### 移动端适配
```css
@media (max-width: 768px) {
  .personnel-header {
    flex-direction: column;
    text-align: center;
  }
  
  .avatar-section {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .personnel-meta {
    justify-content: center;
  }
  
  .info-grid,
  .task-info {
    grid-template-columns: 1fr;
  }
}
```

## 🔄 组件使用

### 背审人员页面使用
```vue
<UnifiedPersonnelDetailDrawer 
  v-model="detailDrawerVisible" 
  :personnel-id="selectedPersonnelId?.toString()"
  mode="background-check"
  @view-task="handleViewTask" 
/>
```

### 异常人员页面使用
```vue
<UnifiedPersonnelDetailDrawer 
  v-model="detailDrawerVisible" 
  :personnel-id="selectedPersonnelId"
  mode="result-processing"
  @view-task="handleViewTask" 
/>
```

## 🎯 状态标签设计

### 状态类型和颜色
| 状态 | 背审模式 | 结果处理模式 | 颜色 | 效果 |
|------|----------|--------------|------|------|
| 进行中 | 背审中 | 处理中 | primary | 蓝色醒目 |
| 完成 | 背审完成 | 已处理 | success | 绿色醒目 |
| 待处理 | 待背审 | 待处理 | warning | 橙色醒目 |

### 醒目效果实现
- **大尺寸**: `size="large"`
- **深色效果**: `effect="dark"`
- **自定义样式**: 增加阴影、加粗字体、增大内边距
- **突出位置**: 放在人员头部区域，与姓名并列显示

## 🚀 优势特点

### 1. 统一体验
- 两个模块使用相同的详情展示组件
- 一致的布局、样式和交互逻辑
- 降低用户学习成本

### 2. 灵活配置
- 通过mode属性切换不同模式
- 根据模式动态显示不同的信息字段
- 支持扩展新的模式

### 3. 信息层次清晰
- 四Tab设计，信息分类明确
- 醒目的状态标签，关键信息突出
- 时间线展示历史记录，逻辑清晰

### 4. 响应式友好
- 移动端适配完善
- 网格布局自动调整
- 触摸友好的交互设计

## 📊 实现效果

### 用户体验提升
- ✅ **一致性**: 两个模块的详情展示完全一致
- ✅ **易用性**: 醒目的状态标签，关键信息一目了然
- ✅ **完整性**: 四Tab设计，信息展示全面
- ✅ **美观性**: 现代化的UI设计，视觉效果优秀

### 开发效率提升
- ✅ **复用性**: 一个组件支持两种模式，减少重复开发
- ✅ **维护性**: 统一的代码结构，便于维护和扩展
- ✅ **扩展性**: 易于添加新的模式和功能

**统一人员详情抽屉组件已完成，为背审管理系统提供了一致且优秀的详情展示体验！** 🎉
