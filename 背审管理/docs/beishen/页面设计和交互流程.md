# 待背审功能页面设计和交互流程

## 🎨 页面设计详细说明

### 1. 背审人员管理页面设计

#### 1.1 页面整体布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 面包屑导航: 首页 > 背审管理 > 背审人员                              │
├─────────────────────────────────────────────────────────────────┤
│ 状态切换Tab                                                      │
│ [待背审 (123)] [背审中 (45)] [已完成背审 (678)]                   │
├─────────────────────────────────────────────────────────────────┤
│ 筛选条件区域                                                     │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐    │
│ │人员类型 │ 所属组织 │ 风险等级 │ 时间范围 │ 关键词  │ 操作    │    │
│ │[下拉选择]│[下拉选择]│[下拉选择]│[日期选择]│[输入框] │[搜索重置]│    │
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│ 操作工具栏 (按状态动态显示)                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 批量操作模式: ○选中人员(0) ●当前筛选结果(123)                │ │
│ │ 待背审状态: [发起背审] [导出Excel] [批量操作▼]               │ │
│ │ 背审中状态: [撤销任务] [导出Excel] [批量操作▼]               │ │
│ │ 已完成状态: [重新发起] [导出Excel] [批量操作▼]               │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 统计卡片区域 (紧凑设计，可点击查看详情)                          │
│ ┌─────────┬─────────┬─────────┬─────────┐ [📊统计详情]          │
│ │总人数   │待背审   │背审中   │已完成   │                        │
│ │  1,234  │   123   │   45    │   678   │                        │
│ │  [蓝色] │ [橙色]  │ [蓝色]  │ [绿色]  │                        │
│ └─────────┴─────────┴─────────┴─────────┘                        │
├─────────────────────────────────────────────────────────────────┤
│ 人员列表表格                                                     │
│ ┌─┬────────┬──────────┬────────┬────────┬──────────┬────────┐ │
│ │□│ 姓名   │ 身份证号  │人员类型│背审状态│最后背审  │ 操作   │ │
│ ├─┼────────┼──────────┼────────┼────────┼──────────┼────────┤ │
│ │□│ 张三   │ 130***   │专职保卫│待背审  │2024-01-01│ 详情   │ │
│ │□│ 李四   │ 130***   │保安人员│背审中  │2024-01-15│ 详情   │ │
│ │□│ 王五   │ 130***   │物流人员│已完成  │2024-01-20│ 详情   │ │
│ └─┴────────┴──────────┴────────┴────────┴──────────┴────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 分页组件                                                         │
│ 共123条记录 [10条/页▼] [<] [1] [2] [3] [>] [跳转到第_页]         │
└─────────────────────────────────────────────────────────────────┘
```

#### 1.2 状态标签设计
```typescript
const statusConfig = {
  pending: { color: 'warning', text: '待背审' },
  in_progress: { color: 'primary', text: '背审中' },
  completed: { color: 'success', text: '已完成' }
}

const riskLevelConfig = {
  low: { color: 'info', text: '低风险' },
  medium: { color: 'warning', text: '中风险' },
  high: { color: 'danger', text: '高风险' }
}
```

### 2. 背审任务管理页面设计

#### 2.1 页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 面包屑导航: 首页 > 背审管理 > 背审任务                              │
├─────────────────────────────────────────────────────────────────┤
│ 任务统计卡片                                                     │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐    │
│ │总任务   │待分配   │进行中   │已完成   │已取消   │逾期任务 │    │
│ │   89    │   12    │   23    │   45    │    6    │    3    │    │
│ │ [蓝色]  │ [灰色]  │ [蓝色]  │ [绿色]  │ [灰色]  │ [红色]  │    │
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│ 筛选条件区域                                                     │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐    │
│ │任务状态 │处理部门 │创建时间 │截止时间 │优先级   │ 操作    │    │
│ │[下拉选择]│[下拉选择]│[日期选择]│[日期选择]│[下拉选择]│[搜索重置]│    │
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│ 任务列表表格                                                     │
│ ┌──────────┬────────┬────────┬────────┬────────┬────────┬──────┐ │
│ │ 任务编号 │任务标题│人员数量│处理部门│  进度  │ 状态   │ 操作 │ │
│ ├──────────┼────────┼────────┼────────┼────────┼────────┼──────┤ │
│ │ BG001    │新员工  │ 15人   │莲池分局│ 60%    │进行中  │详情  │ │
│ │          │背审    │        │        │████░░░ │        │催办  │ │
│ ├──────────┼────────┼────────┼────────┼────────┼────────┼──────┤ │
│ │ BG002    │定期    │ 8人    │竞秀分局│ 100%   │已完成  │详情  │ │
│ │          │背审    │        │        │██████ │        │      │ │
│ └──────────┴────────┴────────┴────────┴────────┴────────┴──────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 3. 弹窗设计

#### 3.1 发起背审任务弹窗
```
┌─────────────────────────────────────────────────────────────────┐
│ 发起背审任务                                              [×]   │
├─────────────────────────────────────────────────────────────────┤
│ 基本信息                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 任务标题: [___________________________] (必填)             │ │
│ │ 任务描述: [___________________________]                     │ │
│ │          [___________________________]                     │ │
│ │ 优先级:   ○普通 ●中等 ○高 ○紧急                           │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 分配信息                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 处理方式: ●分配给部门 ○分配给个人                           │ │
│ │ 处理部门: [下拉选择部门] (部门模式必填)                      │ │
│ │ 处理人员: [下拉选择人员] (个人模式必填)                      │ │
│ │ 截止时间: [2024-02-01 18:00] (必填)                         │ │
│ │ 说明: 部门模式下该部门所有人员都可处理，个人模式仅指定人员   │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 影响范围                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ●选中人员 (15人)                                            │ │
│ │ ○当前筛选结果 (123人)                                       │ │
│ │                                                             │ │
│ │ 预览: 张三、李四、王五... (显示前5个，超出显示省略号)        │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                                          [取消] [确认发起]      │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.2 人员背审详情抽屉
```
                                                    ┌─────────────────┐
                                                    │ 人员背审详情    │[×]
                                                    ├─────────────────┤
                                                    │ 张三 - 专职保卫  │
                                                    ├─────────────────┤
                                                    │ [基本信息]      │
                                                    │ [当前状态]      │
                                                    │ [背审历史]      │
                                                    │ [操作记录]      │
                                                    ├─────────────────┤
                                                    │ 基本信息:       │
                                                    │ 姓名: 张三      │
                                                    │ 身份证: 130***  │
                                                    │ 人员类型: 专职保卫│
                                                    │ 部门: 保安部    │
                                                    │ 入职时间: 2024-01-01│
                                                    │ 联系电话: 138****│
                                                    │ 风险等级: 低风险 │
                                                    │ 背审次数: 3次   │
                                                    ├─────────────────┤
                                                    │ 当前状态:       │
                                                    │ 状态: 背审中    │
                                                    │ 任务: BG001     │
                                                    │ 处理部门: 莲池分局│
                                                    │ 发起时间: 01-15 │
                                                    │ 截止时间: 02-01 │
                                                    │ 剩余: 15天      │
                                                    ├─────────────────┤
                                                    │ 背审历史:       │
                                                    │ ● 2024-01-15   │
                                                    │   发起背审任务   │
                                                    │   (BG001)       │
                                                    │ ● 2023-06-01   │
                                                    │   完成背审-正常  │
                                                    │ ● 2023-01-01   │
                                                    │   入职背审-正常  │
                                                    ├─────────────────┤
                                                    │ [快速操作]      │
                                                    │ [查看任务详情]  │
                                                    │ [导出记录]      │
                                                    └─────────────────┘
```

#### 3.3 统计详情弹窗
```
┌─────────────────────────────────────────────────────────────────┐
│ 背审统计详情                                              [×]   │
├─────────────────────────────────────────────────────────────────┤
│ 总体统计                                                         │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐    │
│ │总人数   │待背审   │背审中   │已完成   │风险人员 │逾期任务 │    │
│ │  1,234  │   123   │   45    │   678   │    8    │    3    │    │
│ │  100%   │  10%    │  3.6%   │  55%    │  0.6%   │  0.2%   │    │
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│ 按人员类型统计                                                   │
│ ┌─────────────┬─────────┬─────────┬─────────┬─────────┐          │
│ │ 人员类型    │ 总数    │ 待背审  │ 背审中  │ 已完成  │          │
│ ├─────────────┼─────────┼─────────┼─────────┼─────────┤          │
│ │ 专职保卫    │   456   │   45    │   12    │   399   │          │
│ │ 保安人员    │   678   │   67    │   28    │   583   │          │
│ │ 物流人员    │   100   │   11    │    5    │    84   │          │
│ └─────────────┴─────────┴─────────┴─────────┴─────────┘          │
├─────────────────────────────────────────────────────────────────┤
│ 按组织统计                                                       │
│ ┌─────────────┬─────────┬─────────┬─────────┬─────────┐          │
│ │ 组织名称    │ 总数    │ 待背审  │ 背审中  │ 已完成  │          │
│ ├─────────────┼─────────┼─────────┼─────────┼─────────┤          │
│ │ 莲池分局    │   234   │   23    │    8    │   203   │          │
│ │ 竞秀分局    │   345   │   34    │   15    │   296   │          │
│ │ 满城分局    │   123   │   12    │    5    │   106   │          │
│ └─────────────┴─────────┴─────────┴─────────┴─────────┘          │
├─────────────────────────────────────────────────────────────────┤
│ 风险等级分布                                                     │
│ ┌─────────────┬─────────┬─────────┬─────────┐                    │
│ │ 风险等级    │ 人数    │ 占比    │ 趋势    │                    │
│ ├─────────────┼─────────┼─────────┼─────────┤                    │
│ │ 低风险      │  1,156  │  93.7%  │ ↓ -2%   │                    │
│ │ 中风险      │    70   │   5.7%  │ ↑ +1%   │                    │
│ │ 高风险      │     8   │   0.6%  │ ↑ +1%   │                    │
│ └─────────────┴─────────┴─────────┴─────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│                                          [导出报表] [关闭]      │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 交互流程设计

### 1. 操作按钮状态控制

#### 1.1 按背审状态显示操作按钮
```typescript
const getAvailableActions = (currentStatus: string) => {
  switch (currentStatus) {
    case 'pending':
      return ['发起背审', '导出Excel', '批量操作']
    case 'in_progress':
      return ['撤销任务', '导出Excel', '批量操作']
    case 'completed':
      return ['重新发起', '导出Excel', '批量操作']
    default:
      return ['导出Excel']
  }
}
```

#### 1.2 操作权限控制
- **发起背审**: 仅管理员可操作，人员状态必须为"待背审"
- **撤销任务**: 仅管理员可操作，人员状态必须为"背审中"
- **重新发起**: 仅管理员可操作，人员状态必须为"已完成背审"
- **导出功能**: 所有角色都可使用

### 2. 发起背审任务流程

#### 2.1 选择模式流程
```mermaid
graph TD
    A[进入背审人员页面] --> B[筛选待背审人员]
    B --> C[勾选目标人员]
    C --> D[点击发起背审按钮]
    D --> E[弹出发起任务弹窗]
    E --> F[填写任务信息]
    F --> G[选择处理部门和截止时间]
    G --> H[确认影响范围显示选中人员]
    H --> I[点击确认发起]
    I --> J[显示确认对话框]
    J --> K[确认后提交请求]
    K --> L[显示成功提示]
    L --> M[刷新页面数据]
    M --> N[人员状态变为背审中]
```

#### 2.2 筛选模式流程
```mermaid
graph TD
    A[进入背审人员页面] --> B[设置筛选条件]
    B --> C[选择筛选模式]
    C --> D[点击发起背审按钮]
    D --> E[弹出发起任务弹窗]
    E --> F[填写任务信息]
    F --> G[确认影响范围显示筛选结果数量]
    G --> H[点击确认发起]
    H --> I[显示确认对话框并列出影响人员]
    I --> J[确认后批量提交]
    J --> K[显示进度条]
    K --> L[完成后显示结果统计]
```

### 3. 统计详情交互流程

#### 3.1 统计卡片点击流程
```mermaid
graph TD
    A[点击统计详情按钮] --> B[弹出统计详情弹窗]
    B --> C[显示总体统计]
    C --> D[显示按类型统计]
    D --> E[显示按组织统计]
    E --> F[显示风险等级分布]
    F --> G[可导出统计报表]
```

#### 3.2 抽屉详情交互流程
```mermaid
graph TD
    A[点击人员详情] --> B[从右侧滑出抽屉]
    B --> C[显示基本信息Tab]
    C --> D[可切换到当前状态Tab]
    D --> E[可切换到背审历史Tab]
    E --> F[可切换到操作记录Tab]
    F --> G[提供快速操作按钮]
    G --> H[可直接跳转到任务详情]
```

### 4. 任务管理交互流程

#### 4.1 查看任务详情流程
```mermaid
graph TD
    A[任务列表页面] --> B[点击任务详情]
    B --> C[弹出任务详情弹窗]
    C --> D[显示任务基本信息]
    D --> E[显示关联人员列表]
    E --> F[显示处理进度]
    F --> G[可进行催办操作]
    G --> H[可查看处理历史]
```

#### 4.2 催办流程
```mermaid
graph TD
    A[发现逾期任务] --> B[点击催办按钮]
    B --> C[弹出催办弹窗]
    C --> D[填写催办内容]
    D --> E[选择催办对象]
    E --> F[发送催办通知]
    F --> G[记录催办历史]
    G --> H[更新任务状态]
```

### 5. 分配方式交互流程

#### 5.1 部门分配模式
```mermaid
graph TD
    A[选择部门分配模式] --> B[显示部门选择下拉框]
    B --> C[隐藏个人选择下拉框]
    C --> D[选择目标部门]
    D --> E[该部门所有成员都可处理任务]
    E --> F[任务分配给部门负责人]
```

#### 5.2 个人分配模式
```mermaid
graph TD
    A[选择个人分配模式] --> B[显示人员选择下拉框]
    B --> C[隐藏部门选择下拉框]
    C --> D[选择具体处理人员]
    D --> E[任务仅分配给指定人员]
    E --> F[其他人员无法查看和处理]
```

### 6. 状态变更交互

#### 6.1 状态变更反馈
- **即时反馈**: 操作后立即更新UI状态
- **进度提示**: 批量操作显示进度条
- **结果通知**: 操作完成后显示详细结果
- **错误处理**: 失败时显示具体错误信息

#### 6.2 数据同步策略
- **乐观更新**: 操作后立即更新本地状态
- **后台同步**: 定期同步服务器状态
- **冲突处理**: 检测到冲突时提示用户
- **自动刷新**: 关键操作后自动刷新相关数据

## 📝 设计改进要点总结

### 1. 操作按钮状态化显示
- ✅ **待背审状态**: 仅显示"发起背审"按钮
- ✅ **背审中状态**: 仅显示"撤销任务"按钮
- ✅ **已完成状态**: 仅显示"重新发起"按钮
- ✅ **权限控制**: 根据用户角色动态显示可用操作

### 2. 统计卡片优化
- ✅ **紧凑布局**: 减少统计卡片数量，节省页面空间
- ✅ **详情弹窗**: 点击"统计详情"按钮查看完整统计信息
- ✅ **多维度统计**: 按人员类型、组织、风险等级等维度展示
- ✅ **趋势分析**: 显示数据变化趋势和占比信息

### 3. 分配方式优化
- ✅ **二选一模式**: 部门分配和个人分配互斥选择
- ✅ **动态表单**: 根据选择模式显示对应的必填字段
- ✅ **清晰说明**: 提供分配方式的说明文字
- ✅ **灵活处理**: 部门模式支持部门内任意人员处理

### 4. 人员详情抽屉化
- ✅ **更大空间**: 抽屉提供更多展示空间
- ✅ **多Tab设计**: 基本信息、当前状态、背审历史、操作记录
- ✅ **快速操作**: 提供常用操作的快捷按钮
- ✅ **关联跳转**: 可直接跳转到相关任务详情

### 5. 交互流程完善
- ✅ **状态驱动**: 基于人员状态控制可用操作
- ✅ **权限控制**: 细粒度的操作权限管理
- ✅ **反馈机制**: 完整的操作反馈和确认流程
- ✅ **数据同步**: 实时的状态更新和数据同步

### 6. 用户体验提升
- ✅ **操作直观**: 根据状态显示相应操作，避免混淆
- ✅ **信息丰富**: 统计详情提供全面的数据分析
- ✅ **空间利用**: 抽屉和弹窗合理分配，最大化信息展示
- ✅ **流程清晰**: 明确的操作流程和状态变更逻辑

这个优化后的设计方案更加符合实际使用需求，提供了更好的用户体验和操作效率。
