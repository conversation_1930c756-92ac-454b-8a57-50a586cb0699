# 待背审功能页面设计和交互流程

## 🎨 页面设计详细说明

### 1. 背审人员管理页面设计

#### 1.1 页面整体布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 面包屑导航: 首页 > 背审管理 > 背审人员                              │
├─────────────────────────────────────────────────────────────────┤
│ 状态切换Tab                                                      │
│ [待背审 (123)] [背审中 (45)] [已完成背审 (678)]                   │
├─────────────────────────────────────────────────────────────────┤
│ 筛选条件区域                                                     │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐    │
│ │人员类型 │ 所属组织 │ 风险等级 │ 时间范围 │ 关键词  │ 操作    │    │
│ │[下拉选择]│[下拉选择]│[下拉选择]│[日期选择]│[输入框] │[搜索重置]│    │
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│ 操作工具栏                                                       │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 批量操作模式: ○选中人员(0) ●当前筛选结果(123)                │ │
│ │ [发起背审] [撤销任务] [重新发起] [导出Excel] [批量操作▼]     │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 统计卡片区域                                                     │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐    │
│ │总人数   │待背审   │背审中   │已完成   │风险人员 │逾期任务 │    │
│ │  1,234  │   123   │   45    │   678   │    8    │    3    │    │
│ │  [蓝色] │ [橙色]  │ [蓝色]  │ [绿色]  │ [红色]  │ [红色]  │    │
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│ 人员列表表格                                                     │
│ ┌─┬────────┬──────────┬────────┬────────┬──────────┬────────┐ │
│ │□│ 姓名   │ 身份证号  │人员类型│背审状态│最后背审  │ 操作   │ │
│ ├─┼────────┼──────────┼────────┼────────┼──────────┼────────┤ │
│ │□│ 张三   │ 130***   │专职保卫│待背审  │2024-01-01│ 详情   │ │
│ │□│ 李四   │ 130***   │保安人员│背审中  │2024-01-15│ 详情   │ │
│ │□│ 王五   │ 130***   │物流人员│已完成  │2024-01-20│ 详情   │ │
│ └─┴────────┴──────────┴────────┴────────┴──────────┴────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 分页组件                                                         │
│ 共123条记录 [10条/页▼] [<] [1] [2] [3] [>] [跳转到第_页]         │
└─────────────────────────────────────────────────────────────────┘
```

#### 1.2 状态标签设计
```typescript
const statusConfig = {
  pending: { color: 'warning', text: '待背审' },
  in_progress: { color: 'primary', text: '背审中' },
  completed: { color: 'success', text: '已完成' }
}

const riskLevelConfig = {
  low: { color: 'info', text: '低风险' },
  medium: { color: 'warning', text: '中风险' },
  high: { color: 'danger', text: '高风险' }
}
```

### 2. 背审任务管理页面设计

#### 2.1 页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 面包屑导航: 首页 > 背审管理 > 背审任务                              │
├─────────────────────────────────────────────────────────────────┤
│ 任务统计卡片                                                     │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐    │
│ │总任务   │待分配   │进行中   │已完成   │已取消   │逾期任务 │    │
│ │   89    │   12    │   23    │   45    │    6    │    3    │    │
│ │ [蓝色]  │ [灰色]  │ [蓝色]  │ [绿色]  │ [灰色]  │ [红色]  │    │
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│ 筛选条件区域                                                     │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐    │
│ │任务状态 │处理部门 │创建时间 │截止时间 │优先级   │ 操作    │    │
│ │[下拉选择]│[下拉选择]│[日期选择]│[日期选择]│[下拉选择]│[搜索重置]│    │
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│ 任务列表表格                                                     │
│ ┌──────────┬────────┬────────┬────────┬────────┬────────┬──────┐ │
│ │ 任务编号 │任务标题│人员数量│处理部门│  进度  │ 状态   │ 操作 │ │
│ ├──────────┼────────┼────────┼────────┼────────┼────────┼──────┤ │
│ │ BG001    │新员工  │ 15人   │莲池分局│ 60%    │进行中  │详情  │ │
│ │          │背审    │        │        │████░░░ │        │催办  │ │
│ ├──────────┼────────┼────────┼────────┼────────┼────────┼──────┤ │
│ │ BG002    │定期    │ 8人    │竞秀分局│ 100%   │已完成  │详情  │ │
│ │          │背审    │        │        │██████ │        │      │ │
│ └──────────┴────────┴────────┴────────┴────────┴────────┴──────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 3. 弹窗设计

#### 3.1 发起背审任务弹窗
```
┌─────────────────────────────────────────────────────────────────┐
│ 发起背审任务                                              [×]   │
├─────────────────────────────────────────────────────────────────┤
│ 基本信息                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 任务标题: [___________________________] (必填)             │ │
│ │ 任务描述: [___________________________]                     │ │
│ │          [___________________________]                     │ │
│ │ 优先级:   ○普通 ●中等 ○高 ○紧急                           │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 分配信息                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 处理部门: [下拉选择部门] (必填)                              │ │
│ │ 处理人员: [下拉选择人员] (可选)                              │ │
│ │ 截止时间: [2024-02-01 18:00] (必填)                         │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 影响范围                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ●选中人员 (15人)                                            │ │
│ │ ○当前筛选结果 (123人)                                       │ │
│ │                                                             │ │
│ │ 预览: 张三、李四、王五... (显示前5个，超出显示省略号)        │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                                          [取消] [确认发起]      │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.2 人员背审详情弹窗
```
┌─────────────────────────────────────────────────────────────────┐
│ 人员背审详情 - 张三                                        [×]   │
├─────────────────────────────────────────────────────────────────┤
│ Tab切换: [基本信息] [当前状态] [背审历史]                        │
├─────────────────────────────────────────────────────────────────┤
│ 基本信息Tab内容:                                                │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 姓名: 张三          身份证: 130***                          │ │
│ │ 人员类型: 专职保卫   部门: 保安部                           │ │
│ │ 入职时间: 2024-01-01 联系电话: 138****                     │ │
│ │ 风险等级: 低风险     背审次数: 3次                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 当前状态Tab内容:                                                │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 当前状态: 背审中                                            │ │
│ │ 关联任务: BG20240115001 - 新员工背审                       │ │
│ │ 处理部门: 莲池分局                                          │ │
│ │ 发起时间: 2024-01-15 09:00                                 │ │
│ │ 截止时间: 2024-02-01 18:00                                 │ │
│ │ 剩余时间: 15天                                              │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 背审历史Tab内容:                                                │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 时间线展示:                                                 │ │
│ │ ● 2024-01-15 发起背审任务 (BG001) - 进行中                 │ │
│ │ ● 2023-06-01 完成背审 - 正常                               │ │
│ │ ● 2023-01-01 入职背审 - 正常                               │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                                                    [关闭]       │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 交互流程设计

### 1. 发起背审任务流程

#### 1.1 选择模式流程
```mermaid
graph TD
    A[进入背审人员页面] --> B[筛选待背审人员]
    B --> C[勾选目标人员]
    C --> D[点击发起背审按钮]
    D --> E[弹出发起任务弹窗]
    E --> F[填写任务信息]
    F --> G[选择处理部门和截止时间]
    G --> H[确认影响范围显示选中人员]
    H --> I[点击确认发起]
    I --> J[显示确认对话框]
    J --> K[确认后提交请求]
    K --> L[显示成功提示]
    L --> M[刷新页面数据]
    M --> N[人员状态变为背审中]
```

#### 1.2 筛选模式流程
```mermaid
graph TD
    A[进入背审人员页面] --> B[设置筛选条件]
    B --> C[选择筛选模式]
    C --> D[点击发起背审按钮]
    D --> E[弹出发起任务弹窗]
    E --> F[填写任务信息]
    F --> G[确认影响范围显示筛选结果数量]
    G --> H[点击确认发起]
    H --> I[显示确认对话框并列出影响人员]
    I --> J[确认后批量提交]
    J --> K[显示进度条]
    K --> L[完成后显示结果统计]
```

### 2. 任务管理交互流程

#### 2.1 查看任务详情流程
```mermaid
graph TD
    A[任务列表页面] --> B[点击任务详情]
    B --> C[弹出任务详情弹窗]
    C --> D[显示任务基本信息]
    D --> E[显示关联人员列表]
    E --> F[显示处理进度]
    F --> G[可进行催办操作]
    G --> H[可查看处理历史]
```

#### 2.2 催办流程
```mermaid
graph TD
    A[发现逾期任务] --> B[点击催办按钮]
    B --> C[弹出催办弹窗]
    C --> D[填写催办内容]
    D --> E[选择催办对象]
    E --> F[发送催办通知]
    F --> G[记录催办历史]
    G --> H[更新任务状态]
```

### 3. 状态变更交互

#### 3.1 状态变更反馈
- **即时反馈**: 操作后立即更新UI状态
- **进度提示**: 批量操作显示进度条
- **结果通知**: 操作完成后显示详细结果
- **错误处理**: 失败时显示具体错误信息

#### 3.2 数据同步策略
- **乐观更新**: 操作后立即更新本地状态
- **后台同步**: 定期同步服务器状态
- **冲突处理**: 检测到冲突时提示用户
- **自动刷新**: 关键操作后自动刷新相关数据

这个设计方案提供了完整的页面布局、交互流程和用户体验设计，确保功能的易用性和操作的直观性。
