# 待背审功能开发方案

## 📋 开发概述

基于需求方案，制定详细的技术开发方案，包括数据库设计、API接口设计、前端组件设计和开发计划。

## 🗄️ 数据库设计

### 1. 核心表结构

#### 1.1 人员背审状态表 (personnel_background_check_status)
```sql
CREATE TABLE personnel_background_check_status (
    id VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    personnel_id VARCHAR(36) NOT NULL COMMENT '人员ID',
    current_status ENUM('pending', 'in_progress', 'completed') NOT NULL DEFAULT 'pending' COMMENT '当前背审状态',
    current_task_id VARCHAR(36) COMMENT '当前关联的任务ID',
    last_check_date DATE COMMENT '最后背审日期',
    next_check_date DATE COMMENT '下次背审日期',
    check_count INT DEFAULT 0 COMMENT '背审次数',
    risk_level ENUM('low', 'medium', 'high') DEFAULT 'low' COMMENT '风险等级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_personnel_id (personnel_id),
    INDEX idx_current_status (current_status),
    INDEX idx_current_task_id (current_task_id),
    INDEX idx_next_check_date (next_check_date)
) COMMENT '人员背审状态表';
```

#### 1.2 背审任务表 (background_check_tasks)
```sql
CREATE TABLE background_check_tasks (
    id VARCHAR(36) PRIMARY KEY COMMENT '任务ID',
    task_no VARCHAR(50) UNIQUE NOT NULL COMMENT '任务编号',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    status ENUM('pending', 'assigned', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    
    -- 人员统计
    personnel_count INT DEFAULT 0 COMMENT '涉及人员总数',
    completed_count INT DEFAULT 0 COMMENT '已完成人员数',
    
    -- 创建信息
    created_by VARCHAR(36) NOT NULL COMMENT '创建人ID',
    created_by_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    created_by_org VARCHAR(36) NOT NULL COMMENT '创建人组织ID',
    
    -- 分配信息
    assigned_to_org VARCHAR(36) COMMENT '分配给组织ID',
    assigned_to_org_name VARCHAR(100) COMMENT '分配给组织名称',
    assigned_to_user VARCHAR(36) COMMENT '分配给用户ID',
    assigned_to_user_name VARCHAR(50) COMMENT '分配给用户姓名',
    
    -- 时间管理
    assigned_at TIMESTAMP NULL COMMENT '分配时间',
    due_date TIMESTAMP NOT NULL COMMENT '截止时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_no (task_no),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_assigned_to_org (assigned_to_org),
    INDEX idx_due_date (due_date),
    INDEX idx_created_at (created_at)
) COMMENT '背审任务表';
```

#### 1.3 任务人员关联表 (task_personnel_relations)
```sql
CREATE TABLE task_personnel_relations (
    id VARCHAR(36) PRIMARY KEY COMMENT '关联ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    personnel_id VARCHAR(36) NOT NULL COMMENT '人员ID',
    status ENUM('pending', 'processing', 'completed') DEFAULT 'pending' COMMENT '处理状态',
    result ENUM('normal', 'abnormal', 'risk') COMMENT '背审结果',
    result_details TEXT COMMENT '结果详情',
    processed_by VARCHAR(36) COMMENT '处理人ID',
    processed_by_name VARCHAR(50) COMMENT '处理人姓名',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_task_personnel (task_id, personnel_id),
    INDEX idx_task_id (task_id),
    INDEX idx_personnel_id (personnel_id),
    INDEX idx_status (status)
) COMMENT '任务人员关联表';
```

#### 1.4 背审记录表 (background_check_records)
```sql
CREATE TABLE background_check_records (
    id VARCHAR(36) PRIMARY KEY COMMENT '记录ID',
    personnel_id VARCHAR(36) NOT NULL COMMENT '人员ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    check_type ENUM('initial', 'periodic', 'special') NOT NULL COMMENT '背审类型',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') NOT NULL COMMENT '状态',
    result ENUM('normal', 'abnormal', 'risk') COMMENT '背审结果',
    result_details TEXT COMMENT '结果详情',
    
    -- 处理信息
    processed_by VARCHAR(36) COMMENT '处理人ID',
    processed_by_name VARCHAR(50) COMMENT '处理人姓名',
    processed_by_org VARCHAR(36) COMMENT '处理人组织ID',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    -- 审核信息
    reviewed_by VARCHAR(36) COMMENT '审核人ID',
    reviewed_by_name VARCHAR(50) COMMENT '审核人姓名',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_comments TEXT COMMENT '审核意见',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_personnel_id (personnel_id),
    INDEX idx_task_id (task_id),
    INDEX idx_check_type (check_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '背审记录表';
```

#### 1.5 催办记录表 (reminder_records)
```sql
CREATE TABLE reminder_records (
    id VARCHAR(36) PRIMARY KEY COMMENT '催办ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    reminder_type ENUM('overdue', 'deadline_approaching', 'manual') NOT NULL COMMENT '催办类型',
    content TEXT NOT NULL COMMENT '催办内容',
    sent_to_org VARCHAR(36) NOT NULL COMMENT '发送给组织ID',
    sent_to_user VARCHAR(36) COMMENT '发送给用户ID',
    sent_by VARCHAR(36) NOT NULL COMMENT '发送人ID',
    sent_by_name VARCHAR(50) NOT NULL COMMENT '发送人姓名',
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    read_at TIMESTAMP NULL COMMENT '阅读时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_sent_to_org (sent_to_org),
    INDEX idx_sent_at (sent_at)
) COMMENT '催办记录表';
```

### 2. 数据初始化

#### 2.1 人员状态初始化
```sql
-- 为现有人员初始化背审状态
INSERT INTO personnel_background_check_status (personnel_id, current_status, check_count)
SELECT id, 'pending', 0 FROM personnel_info 
WHERE id NOT IN (SELECT personnel_id FROM personnel_background_check_status);
```

#### 2.2 任务编号生成规则
```typescript
// 任务编号格式: BG + YYYYMMDD + 4位序号
// 例: BG202401150001
function generateTaskNo(): string {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  const sequence = await getNextSequence(date)
  return `BG${date}${sequence.toString().padStart(4, '0')}`
}
```

## 🔌 API接口设计

### 1. 背审人员管理接口

#### 1.1 获取背审人员列表
```typescript
GET /api/background-check/personnel
Query Parameters:
- status: 'pending' | 'in_progress' | 'completed'
- personnelType: number
- organizationId: string
- keyword: string
- page: number
- size: number

Response:
{
  code: 200,
  data: {
    records: PersonnelWithCheckStatus[],
    total: number,
    page: number,
    size: number
  }
}
```

#### 1.2 批量发起背审任务
```typescript
POST /api/background-check/tasks/create
Body:
{
  title: string,
  description?: string,
  priority: 'low' | 'medium' | 'high' | 'urgent',
  dueDate: string,
  assignedToOrg: string,
  assignedToUser?: string,
  personnelIds?: string[], // 指定人员ID列表
  filterConditions?: {     // 或使用筛选条件
    status: string,
    personnelType: number,
    organizationId: string
  }
}

Response:
{
  code: 200,
  data: {
    taskId: string,
    taskNo: string,
    affectedPersonnelCount: number
  }
}
```

#### 1.3 批量撤销背审任务
```typescript
POST /api/background-check/tasks/cancel
Body:
{
  personnelIds?: string[],
  filterConditions?: object
}

Response:
{
  code: 200,
  data: {
    cancelledTaskIds: string[],
    affectedPersonnelCount: number
  }
}
```

### 2. 背审任务管理接口

#### 2.1 获取任务列表
```typescript
GET /api/background-check/tasks
Query Parameters:
- status: string
- assignedToOrg: string
- createdBy: string
- isOverdue: boolean
- page: number
- size: number

Response:
{
  code: 200,
  data: {
    records: BackgroundCheckTask[],
    total: number,
    statistics: {
      total: number,
      inProgress: number,
      completed: number,
      overdue: number
    }
  }
}
```

#### 2.2 获取任务详情
```typescript
GET /api/background-check/tasks/{taskId}

Response:
{
  code: 200,
  data: {
    task: BackgroundCheckTask,
    personnel: TaskPersonnelRelation[],
    progress: {
      total: number,
      completed: number,
      pending: number,
      percentage: number
    }
  }
}
```

#### 2.3 发送催办通知
```typescript
POST /api/background-check/tasks/{taskId}/remind
Body:
{
  content: string,
  reminderType: 'overdue' | 'deadline_approaching' | 'manual'
}

Response:
{
  code: 200,
  data: {
    reminderId: string,
    sentAt: string
  }
}
```

### 3. 背审记录接口

#### 3.1 获取人员背审历史
```typescript
GET /api/background-check/personnel/{personnelId}/records

Response:
{
  code: 200,
  data: BackgroundCheckRecord[]
}
```

#### 3.2 提交背审结果
```typescript
POST /api/background-check/records
Body:
{
  taskId: string,
  personnelId: string,
  result: 'normal' | 'abnormal' | 'risk',
  resultDetails: string
}

Response:
{
  code: 200,
  data: {
    recordId: string,
    taskProgress: {
      completed: number,
      total: number,
      percentage: number
    }
  }
}
```

## 🎨 前端组件设计

### 1. 页面组件结构

#### 1.1 背审人员管理页面
```
BackgroundCheckPersonnelPage
├── PersonnelStatusTabs (状态切换)
├── PersonnelFilterForm (筛选表单)
├── PersonnelActionToolbar (操作工具栏)
├── PersonnelStatusCards (状态统计卡片)
├── PersonnelTable (人员列表表格)
├── BatchOperationDialog (批量操作弹窗)
└── PersonnelDetailDialog (人员详情弹窗)
```

#### 1.2 背审任务管理页面
```
BackgroundCheckTaskPage
├── TaskStatisticsCards (任务统计卡片)
├── TaskFilterForm (任务筛选表单)
├── TaskTable (任务列表表格)
├── TaskDetailDialog (任务详情弹窗)
└── ReminderDialog (催办弹窗)
```

### 2. 核心组件设计

#### 2.1 批量操作工具栏
```vue
<template>
  <div class="batch-operation-toolbar">
    <div class="operation-mode">
      <el-radio-group v-model="operationMode">
        <el-radio label="selected">选中人员 ({{ selectedCount }})</el-radio>
        <el-radio label="filtered">当前筛选结果 ({{ filteredCount }})</el-radio>
      </el-radio-group>
    </div>
    <div class="operation-buttons">
      <el-button 
        v-if="canStartCheck" 
        type="primary" 
        @click="handleStartCheck"
      >
        发起背审
      </el-button>
      <el-button 
        v-if="canCancelCheck" 
        type="warning" 
        @click="handleCancelCheck"
      >
        撤销任务
      </el-button>
      <el-button 
        v-if="canRestartCheck" 
        type="success" 
        @click="handleRestartCheck"
      >
        重新发起
      </el-button>
    </div>
  </div>
</template>
```

#### 2.2 任务进度组件
```vue
<template>
  <div class="task-progress">
    <el-progress 
      :percentage="progress.percentage" 
      :status="progressStatus"
      :stroke-width="8"
    />
    <div class="progress-text">
      {{ progress.completed }}/{{ progress.total }} 
      ({{ progress.percentage }}%)
    </div>
    <div v-if="isOverdue" class="overdue-warning">
      <el-tag type="danger" size="small">
        逾期 {{ overdueBy }} 天
      </el-tag>
    </div>
  </div>
</template>
```

## 📅 开发计划

### 第一阶段：数据库和基础API (3天)
- Day 1: 数据库表结构设计和创建
- Day 2: 基础API接口开发（CRUD操作）
- Day 3: 数据初始化和测试

### 第二阶段：背审人员管理页面 (4天)
- Day 4: 人员列表页面和筛选功能
- Day 5: 批量操作功能实现
- Day 6: 人员详情和历史记录
- Day 7: 状态管理和交互优化

### 第三阶段：背审任务管理页面 (3天)
- Day 8: 任务列表和统计功能
- Day 9: 任务详情和进度跟踪
- Day 10: 催办功能和通知机制

### 第四阶段：测试和优化 (2天)
- Day 11: 功能测试和Bug修复
- Day 12: 性能优化和用户体验改进

## 🔧 技术要点

### 1. 状态管理
- 使用Pinia管理背审状态
- 实现状态变更的实时更新
- 支持乐观更新和错误回滚

### 2. 批量操作优化
- 前端分页批量处理
- 后端异步任务处理
- 进度反馈和错误处理

### 3. 实时通知
- WebSocket连接管理
- 任务状态变更通知
- 催办消息推送

### 4. 性能优化
- 列表数据虚拟滚动
- 接口请求防抖和缓存
- 组件懒加载

## 🎯 关键技术决策

1. **批量操作策略**: 支持选择模式和筛选模式两种批量操作方式
2. **状态同步**: 使用事件驱动模式确保状态一致性
3. **权限控制**: 基于角色和组织的细粒度权限控制
4. **数据一致性**: 使用数据库事务确保操作原子性
5. **用户体验**: 提供丰富的交互反馈和操作确认

这个开发方案提供了完整的技术实现路径，涵盖了从数据库设计到前端交互的所有关键环节。
