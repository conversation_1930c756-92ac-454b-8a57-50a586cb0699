# 背审结果处理模块交互设计

## 设计理念

### 核心原则
1. **一致性**: 与背审管理模块保持高度一致的交互模式
2. **复用性**: 最大化复用已验证的交互组件和模式
3. **易用性**: 简化操作流程，降低学习成本
4. **效率性**: 支持批量操作，提高工作效率

### 设计目标
- 用户无需重新学习，可以直接上手使用
- 操作流程清晰，减少用户犯错的可能性
- 信息展示层次分明，重要信息突出显示
- 响应式设计，适配不同设备和屏幕尺寸

## 页面结构设计

### 1. 异常人员管理页面 (AbnormalPersonnelPage)

#### 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 搜索表单区域                                                  │
│ [姓名] [异常类型] [处理状态] [发现时间] [搜索] [重置] [导出]    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 状态标签页                                                   │
│ [待处理(123)] [已处理(456)]                                  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 表格标题行                                                   │
│ 异常人员：579个  已选择：3个 [×]     [批量下发处理任务]      │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 人员列表表格                                                 │
│ [□] [头像] 姓名 身份证号 联系方式 机构 异常类型 等级 发现时间 │
│ [□] [头像] 张三 110***1234 138*** 技术部 犯罪记录 高 01-15  │
│ [□] [头像] 李四 120***5678 139*** 市场部 信用问题 中 01-16  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 分页组件                                                     │
│        共 579 条  [10条/页] [< 1 2 3 4 5 >]                │
└─────────────────────────────────────────────────────────────┘
```

#### 交互要点
1. **搜索表单**: 一行4个字段，与背审模块保持一致
2. **状态标签**: 显示数量统计，支持快速切换
3. **选择机制**: 支持跨页面选择，基于身份证号缓存
4. **批量操作**: 选中人员后显示批量操作按钮

### 2. 处理任务管理页面 (ProcessingTaskPage)

#### 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 搜索表单区域                                                  │
│ [任务状态] [任务标题] [处理人] [截止时间] [搜索] [重置] [导出] │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 表格标题行                                                   │
│ 处理任务：89个  已选择：2个 [×]     [批量撤销] [批量催办]    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 任务列表表格                                                 │
│ [□] 任务编号 任务标题 人员数量 处理人 创建时间 进度 状态 催办 │
│ [□] PT001   新员工异常处理 15人 张处理员 01-15 60% 进行中 2次│
│ [□] PT002   定期异常复查   8人  李处理员 01-10 100% 已完成 0次│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 分页组件                                                     │
│        共 89 条  [20条/页] [< 1 2 3 4 5 >]                 │
└─────────────────────────────────────────────────────────────┘
```

## 组件交互设计

### 1. 搜索表单组件 (SearchForm)

#### 交互行为
- **实时搜索**: 输入框支持回车键触发搜索
- **条件清除**: 每个字段都有清除按钮
- **重置功能**: 一键清空所有搜索条件
- **自定义按钮**: 导出等功能按钮集成在搜索表单中

#### 字段配置
```typescript
// 异常人员页面搜索配置
const searchFormConfig = [
  [
    { key: 'name', type: 'input', label: '姓名', placeholder: '请输入姓名' },
    { key: 'abnormalType', type: 'select', label: '异常类型', options: [...] },
    { key: 'processingStatus', type: 'select', label: '处理状态', options: [...] },
    { key: 'discoveredTimeRange', type: 'daterange', label: '发现时间' }
  ]
]

// 处理任务页面搜索配置
const searchFormConfig = [
  [
    { key: 'status', type: 'select', label: '任务状态', options: [...] },
    { key: 'title', type: 'input', label: '任务标题', placeholder: '请输入任务标题' },
    { key: 'assignedToUser', type: 'input', label: '处理人', placeholder: '请输入处理人' },
    { key: 'dueDate', type: 'date', label: '截止时间' }
  ]
]
```

### 2. 批量操作弹窗

#### 发起处理任务弹窗 (StartProcessingTaskDialog)
```
┌─────────────────────────────────────────────────────────────┐
│ 发起处理任务                                           [×]   │
├─────────────────────────────────────────────────────────────┤
│ 基本信息                                                     │
│ 任务标题: [                                              ] │
│ 任务描述: [                                              ] │
│          [                                              ] │
│ 优先级:   [中等 ▼]                                          │
│                                                             │
│ 处理要求                                                     │
│ 处理要求: [                                              ] │
│          [                                              ] │
│ 处理期限: [2024-01-25 18:00:00]                            │
│                                                             │
│ 任务分配                                                     │
│ 分配方式: ○ 部门分配  ● 个人分配                            │
│ 分配给:   [张处理员 ▼]                                      │
│                                                             │
│ 影响范围                                                     │
│ 操作范围: ○ 选中的人员 (3人)  ● 全部筛选结果 (123人)        │
│ 提示: 仅对当前选中的人员执行操作                             │
│                                                             │
│ 将对以下人员发起处理任务:                                    │
│ [张三] [李四] [王五] 等3人                                   │
│                                                             │
│                                    [取消]  [确定发起]       │
└─────────────────────────────────────────────────────────────┘
```

#### 交互要点
1. **表单验证**: 实时验证必填字段
2. **操作范围**: 支持选中人员和全部筛选结果两种模式
3. **人员预览**: 显示将要处理的人员列表
4. **确认机制**: 提交前弹出确认对话框

### 3. 任务详情抽屉 (ProcessingTaskDetailDrawer)

#### 抽屉结构
```
┌─────────────────────────────────────────────────────────────┐
│ 任务详情                                             [×]   │
├─────────────────────────────────────────────────────────────┤
│ [任务信息] [处理历史] [关联人员]                             │
├─────────────────────────────────────────────────────────────┤
│ Tab 1: 任务信息                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 任务编号: PT20240115001                                │ │
│ │ 任务标题: 新员工异常处理                                │ │
│ │ 任务状态: [进行中]  优先级: [高]                        │ │
│ │ 人员数量: 15人      处理人: 张处理员                    │ │
│ │ 创建时间: 2024-01-15 09:00:00                          │ │
│ │ 截止时间: 2024-01-25 18:00:00                          │ │
│ │ 任务描述: 对新入职员工的异常情况进行处理...              │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 任务进度                                                     │
│ ████████████░░░░░░░░ 60%                                    │
│ 已完成: 9人 / 总计: 15人                                    │
└─────────────────────────────────────────────────────────────┘
```

#### Tab 2: 处理历史
```
┌─────────────────────────────────────────────────────────────┐
│ 处理时间          处理人    处理内容                         │
│ 2024-01-20 14:30 张处理员  完成3名人员的异常处理             │
│ 2024-01-18 10:15 张处理员  开始处理任务，已联系相关人员       │
│ 2024-01-15 09:30 系统      任务已分配给张处理员              │
│ 2024-01-15 09:00 李管理员  创建处理任务                      │
└─────────────────────────────────────────────────────────────┘
```

#### Tab 3: 关联人员
```
┌─────────────────────────────────────────────────────────────┐
│ 人员搜索                                                     │
│ 姓名: [        ] 身份证号: [        ] [搜索] [重置]         │
├─────────────────────────────────────────────────────────────┤
│ 姓名   身份证号      联系方式   所属机构  异常类型   处理状态  │
│ 张三   110***1234   138***     技术部    犯罪记录   已处理   │
│ 李四   120***5678   139***     市场部    信用问题   处理中   │
│ 王五   130***9012   137***     财务部    学历造假   待处理   │
├─────────────────────────────────────────────────────────────┤
│        共 15 条  [10条/页] [< 1 2 >]                       │
└─────────────────────────────────────────────────────────────┘
```

## 状态和反馈设计

### 1. 状态标识

#### 异常等级标识
- **严重 (Critical)**: 红色标签，图标警告
- **高 (High)**: 橙色标签
- **中 (Medium)**: 黄色标签  
- **低 (Low)**: 蓝色标签

#### 处理状态标识
- **待处理**: 灰色标签
- **已分配**: 蓝色标签
- **处理中**: 橙色标签，带进度指示
- **已完成**: 绿色标签，带完成图标

### 2. 交互反馈

#### 操作反馈
- **成功操作**: 绿色Toast提示，自动消失
- **错误操作**: 红色Toast提示，需要用户确认
- **警告信息**: 黄色Toast提示，自动消失
- **加载状态**: 按钮loading状态，表格skeleton

#### 确认对话框
```
┌─────────────────────────────────────────┐
│ 确认发起处理任务                         │
├─────────────────────────────────────────┤
│ 确定要对选中的 3 人发起处理任务吗？      │
│                                         │
│ 任务标题: 新员工异常处理                 │
│ 处理人: 张处理员                        │
│ 截止时间: 2024-01-25 18:00:00           │
│                                         │
│              [取消]  [确定]             │
└─────────────────────────────────────────┘
```

## 响应式设计

### 桌面端 (>1200px)
- 搜索表单一行显示4个字段
- 表格显示所有列
- 抽屉宽度60%

### 平板端 (768px-1200px)
- 搜索表单一行显示2个字段
- 表格隐藏次要列
- 抽屉宽度80%

### 移动端 (<768px)
- 搜索表单一行显示1个字段
- 表格采用卡片式布局
- 抽屉全屏显示

## 无障碍设计

### 键盘导航
- 支持Tab键在表单字段间切换
- 支持回车键提交搜索
- 支持ESC键关闭弹窗

### 屏幕阅读器
- 为重要元素添加aria-label
- 表格添加适当的表头关联
- 状态变化提供语音反馈

### 色彩对比
- 确保文字与背景对比度 > 4.5:1
- 状态标识不仅依赖颜色，还有图标辅助
- 支持高对比度模式

## 性能优化

### 渲染优化
- 表格使用虚拟滚动处理大数据量
- 图片懒加载
- 组件按需加载

### 交互优化
- 搜索防抖处理
- 批量操作进度提示
- 操作结果缓存

### 网络优化
- 分页加载数据
- 搜索结果缓存
- 图片压缩和CDN加速

## 错误处理

### 网络错误
- 显示友好的错误提示
- 提供重试机制
- 离线状态提示

### 数据错误
- 表单验证错误高亮显示
- 数据格式错误提示
- 空状态友好展示

### 权限错误
- 无权限操作灰化处理
- 权限不足提示
- 登录状态检查
