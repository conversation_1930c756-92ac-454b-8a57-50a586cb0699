# 通用任务详情抽屉组件设计

## 🎯 设计目标

基于TaskDetailDrawer和ProcessingTaskDetailDrawer的共同特点，创建一个通用的任务详情组件，支持不同类型的任务，减少代码重复，提高维护效率。

## 🏗️ 组件架构

### 1. 核心组件
- **TaskDetailDrawer.vue** - 通用任务详情抽屉组件
- **taskDetailConfig.ts** - 统一配置文件
- **TaskDetailDrawerWrapper.vue** - 具体业务包装组件

### 2. 组件层次结构
```
TaskDetailDrawer (通用组件)
├── TaskDetailDrawerWrapper (背审任务包装)
└── ProcessingTaskDetailDrawerWrapper (处理任务包装)
```

## 📋 功能特性

### 1. **任务类型支持**
- ✅ **背审任务** (`background-check`)
- ✅ **处理任务** (`result-processing`)
- ✅ **可扩展** 支持更多任务类型

### 2. **差异化展示**
- ✅ **背审任务**: 展示背审状态（未完成、正常、异常+异常类型）
- ✅ **处理任务**: 展示处理状态（未处理、已处理+处理结果）
- ✅ **历史记录**: 背审任务显示催办历史表格，处理任务显示处理历史时间线

### 3. **统一搜索**
- ✅ **基础搜索**: 姓名、身份证号
- ✅ **状态筛选**: 未完成、已完成
- ✅ **分页功能**: 统一的分页组件

## 🎨 组件设计

### 1. 通用组件接口
```typescript
interface Props {
  modelValue: boolean
  taskId?: string
  taskType: 'background-check' | 'result-processing'
  taskDetail?: any
  historyList?: any[]
  personnelList?: any[]
  onFetchTaskDetail?: (taskId: string) => Promise<any>
  onFetchHistory?: (taskId: string) => Promise<any[]>
  onFetchPersonnel?: (taskId: string, searchForm: any, pagination: any) => Promise<{ list: any[], total: number }>
}
```

### 2. 任务信息展示
```vue
<el-descriptions :column="2" border>
  <el-descriptions-item label="任务编号">
    {{ taskDetail.taskNo }}
  </el-descriptions-item>
  <el-descriptions-item label="任务状态">
    <el-tag :type="getStatusColor(taskDetail.status)" size="small">
      {{ getStatusText(taskDetail.status) }}
    </el-tag>
  </el-descriptions-item>
  <!-- 更多字段... -->
</el-descriptions>
```

### 3. 差异化人员列表
```vue
<!-- 背审任务：显示背审状态 -->
<el-table-column v-if="taskType === 'background-check'" prop="status" label="背审状态" width="120">
  <template #default="scope">
    <el-tag :type="getPersonnelStatusColor(scope.row.status)" size="small">
      {{ getPersonnelStatusText(scope.row.status) }}
    </el-tag>
    <!-- 异常时显示异常类型 -->
    <div v-if="scope.row.status === 'abnormal' && scope.row.abnormalType" class="abnormal-type">
      <el-tag :type="getAbnormalTypeColor(scope.row.abnormalType)" size="small" class="mt-1">
        {{ getAbnormalTypeText(scope.row.abnormalType) }}
      </el-tag>
    </div>
  </template>
</el-table-column>

<!-- 处理任务：显示异常类型和处理状态 -->
<template v-else-if="taskType === 'result-processing'">
  <el-table-column prop="abnormalType" label="异常类型" width="120">
    <template #default="scope">
      <el-tag :type="getAbnormalTypeColor(scope.row.abnormalType)" size="small">
        {{ getAbnormalTypeText(scope.row.abnormalType) }}
      </el-tag>
    </template>
  </el-table-column>
  <el-table-column prop="processingStatus" label="处理状态" width="120">
    <template #default="scope">
      <el-tag :type="getProcessingStatusColor(scope.row.processingStatus)" size="small">
        {{ getProcessingStatusText(scope.row.processingStatus) }}
      </el-tag>
      <!-- 已处理时显示处理结果 -->
      <div v-if="scope.row.processingStatus === 'completed' && scope.row.processingResult" class="processing-result">
        <el-tag :type="getProcessingResultColor(scope.row.processingResult)" size="small" class="mt-1">
          {{ getProcessingResultText(scope.row.processingResult) }}
        </el-tag>
      </div>
    </template>
  </el-table-column>
</template>
```

### 4. 差异化历史记录
```vue
<!-- 背审任务：催办历史表格 -->
<el-table v-if="taskType === 'background-check'" :data="historyList">
  <el-table-column prop="reminderTime" label="催办时间" width="180" />
  <el-table-column prop="reminderUser" label="催办人" width="120" />
  <el-table-column label="是否已读" width="100">
    <template #default="scope">
      <el-tag :type="scope.row.isRead ? 'success' : 'warning'" size="small">
        {{ scope.row.isRead ? '已读' : '未读' }}
      </el-tag>
    </template>
  </el-table-column>
  <el-table-column prop="feedback" label="反馈内容" min-width="200" />
</el-table>

<!-- 处理任务：处理历史时间线 -->
<el-timeline v-else-if="taskType === 'result-processing'">
  <el-timeline-item
    v-for="item in historyList"
    :timestamp="item.time"
    placement="top"
    :type="getHistoryType(item.type)"
  >
    <div class="history-content">
      <p><strong>{{ item.title }}</strong></p>
      <p>{{ item.content }}</p>
      <p class="operator">操作人：{{ item.operator }}</p>
    </div>
  </el-timeline-item>
</el-timeline>
```

## 🔧 配置文件设计

### 1. 状态配置
```typescript
// 任务状态配置
export const taskStatusConfig = {
  'pending': { label: '待分配', color: 'info' },
  'assigned': { label: '已分配', color: 'primary' },
  'in_progress': { label: '进行中', color: 'warning' },
  'completed': { label: '已完成', color: 'success' },
  'cancelled': { label: '已取消', color: 'info' }
} as const

// 背审状态配置
export const backgroundCheckStatusConfig = {
  'pending': { label: '未完成', color: 'info' },
  'normal': { label: '正常', color: 'success' },
  'abnormal': { label: '异常', color: 'danger' },
  'completed': { label: '已完成', color: 'success' }
} as const

// 处理状态配置
export const processingStatusConfig = {
  'pending': { label: '未处理', color: 'info' },
  'processing': { label: '处理中', color: 'warning' },
  'completed': { label: '已处理', color: 'success' },
  'rejected': { label: '已驳回', color: 'danger' }
} as const

// 处理结果配置
export const processingResultConfig = {
  'focus': { label: '重点关注', color: 'warning' },
  'transfer': { label: '调岗', color: 'primary' },
  'dismiss': { label: '劝退', color: 'danger' },
  'normal': { label: '正常', color: 'success' }
} as const
```

### 2. 工具函数
```typescript
// 获取任务类型配置
export const getTaskTypeConfig = (taskType: 'background-check' | 'result-processing') => {
  if (taskType === 'background-check') {
    return {
      drawerTitle: '背审任务详情',
      historyTabLabel: '催办历史',
      personnelStatusConfig: backgroundCheckStatusConfig,
      getPersonnelStatusText: getBackgroundCheckStatusText,
      getPersonnelStatusColor: getBackgroundCheckStatusColor
    }
  } else {
    return {
      drawerTitle: '处理任务详情',
      historyTabLabel: '处理历史',
      personnelStatusConfig: processingStatusConfig,
      getPersonnelStatusText: getProcessingStatusText,
      getPersonnelStatusColor: getProcessingStatusColor
    }
  }
}
```

## 📝 使用方式

### 1. 背审任务使用
```vue
<template>
  <TaskDetailDrawerWrapper
    v-model="showTaskDetail"
    :task-id="selectedTaskId"
  />
</template>

<script setup>
import TaskDetailDrawerWrapper from '@/components/background-check/TaskDetailDrawerWrapper.vue'
</script>
```

### 2. 处理任务使用
```vue
<template>
  <ProcessingTaskDetailDrawerWrapper
    v-model="showTaskDetail"
    :task-id="selectedTaskId"
  />
</template>

<script setup>
import ProcessingTaskDetailDrawerWrapper from '@/components/result-processing/ProcessingTaskDetailDrawerWrapper.vue'
</script>
```

### 3. 直接使用通用组件
```vue
<template>
  <TaskDetailDrawer
    v-model="visible"
    :task-id="taskId"
    task-type="background-check"
    :task-detail="taskDetail"
    :history-list="historyList"
    :personnel-list="personnelList"
    :on-fetch-task-detail="fetchTaskDetail"
    :on-fetch-history="fetchHistory"
    :on-fetch-personnel="fetchPersonnel"
  />
</template>
```

## 🎯 核心优势

### 1. **代码复用**
- ✅ **统一结构**: 所有任务详情使用相同的组件结构
- ✅ **配置驱动**: 通过配置文件控制差异化展示
- ✅ **减少重复**: 避免多个相似组件的维护成本

### 2. **灵活扩展**
- ✅ **新任务类型**: 只需添加配置即可支持新的任务类型
- ✅ **自定义字段**: 通过配置文件轻松添加新的状态和字段
- ✅ **业务隔离**: 包装组件处理具体业务逻辑

### 3. **维护简单**
- ✅ **统一样式**: 所有任务详情使用相同的样式系统
- ✅ **集中配置**: 状态、颜色等配置集中管理
- ✅ **类型安全**: TypeScript提供完整的类型支持

## 🚀 实现效果

### 背审任务详情
- ✅ **任务信息**: 使用descriptions组件展示基本信息
- ✅ **催办历史**: 表格形式展示催办记录
- ✅ **人员列表**: 显示背审状态，异常时显示异常类型
- ✅ **状态筛选**: 支持未完成/已完成筛选

### 处理任务详情
- ✅ **任务信息**: 相同的信息展示结构
- ✅ **处理历史**: 时间线形式展示处理记录
- ✅ **人员列表**: 显示异常类型和处理状态，已处理时显示处理结果
- ✅ **状态筛选**: 支持未完成/已完成筛选

### 统一特性
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **加载状态**: 统一的loading和空状态处理
- ✅ **搜索分页**: 一致的搜索和分页功能
- ✅ **错误处理**: 统一的错误提示和处理

**通用任务详情抽屉组件设计完成，实现了代码复用和差异化展示的完美平衡！** 🎉
