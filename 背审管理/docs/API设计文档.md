# 背景审查模块 API 设计文档

## 概述

本文档定义了背景审查模块的RESTful API接口规范，包括人员管理、黑名单管理、统计分析和报表生成等核心功能的接口设计。

## 基础信息

- **Base URL**: `http://localhost:3000/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 1. 认证模块

### 1.1 用户登录

**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "name": "管理员",
      "role": "admin",
      "permissions": ["personnel:read", "personnel:write"]
    }
  }
}
```

### 1.2 获取用户信息

**接口地址**: `GET /auth/profile`

**请求头**:
```
Authorization: Bearer {token}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin",
    "name": "管理员",
    "role": "admin",
    "permissions": ["personnel:read", "personnel:write"]
  }
}
```

## 2. 人员管理模块

### 2.1 获取人员列表

**接口地址**: `GET /personnel`

**查询参数**:
```
page=1&pageSize=20&keyword=张三&personnelType=1&organization=保卫科&riskLevel=2
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| keyword | string | 否 | 关键词搜索（姓名、身份证号） |
| personnelType | number | 否 | 人员类别：1-有编制，2-无编制，3-外包 |
| organization | string | 否 | 所属单位 |
| riskLevel | number | 否 | 风险等级：1-低风险，2-中风险，3-高风险 |
| status | number | 否 | 状态：1-正常，2-黑名单，3-待审核 |

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "张三",
        "phone": "13800138000",
        "idCard": "110101199001011234",
        "ethnicity": "汉族",
        "position": "保安员",
        "securityCompany": "XX保安公司",
        "photoUrl": "https://example.com/photo.jpg",
        "education": "高中",
        "region": "北京市朝阳区",
        "address": "北京市朝阳区XX街道XX号",
        "politicalStatus": "群众",
        "organization": "保卫科",
        "personnelType": 1,
        "status": 1,
        "riskLevel": 1,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### 2.2 获取人员详情

**接口地址**: `GET /personnel/{id}`

**路径参数**:
- `id`: 人员ID

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "张三",
    "phone": "13800138000",
    "idCard": "110101199001011234",
    "ethnicity": "汉族",
    "position": "保安员",
    "securityCompany": "XX保安公司",
    "photoUrl": "https://example.com/photo.jpg",
    "education": "高中",
    "region": "北京市朝阳区",
    "address": "北京市朝阳区XX街道XX号",
    "politicalStatus": "群众",
    "organization": "保卫科",
    "personnelType": 1,
    "status": 1,
    "backgroundCheckResult": {
      "id": 1,
      "mentalHealth": "正常",
      "criminalRecord": "无",
      "drugUse": "无",
      "creditRecord": "良好",
      "politicalBackground": "清白",
      "riskLevel": 1,
      "reviewDate": "2024-01-01",
      "reviewer": "审查员A",
      "remarks": "审查通过"
    },
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2.3 创建人员信息

**接口地址**: `POST /personnel`

**请求参数**:
```json
{
  "name": "张三",
  "phone": "13800138000",
  "idCard": "110101199001011234",
  "ethnicity": "汉族",
  "position": "保安员",
  "securityCompany": "XX保安公司",
  "photoUrl": "https://example.com/photo.jpg",
  "education": "高中",
  "region": "北京市朝阳区",
  "address": "北京市朝阳区XX街道XX号",
  "politicalStatus": "群众",
  "organization": "保卫科",
  "personnelType": 1
}
```

**响应数据**:
```json
{
  "code": 201,
  "message": "创建成功",
  "data": {
    "id": 1
  }
}
```

### 2.4 更新人员信息

**接口地址**: `PUT /personnel/{id}`

**路径参数**:
- `id`: 人员ID

**请求参数**:
```json
{
  "name": "张三",
  "phone": "13800138001",
  "position": "高级保安员"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "更新成功"
}
```

### 2.5 删除人员信息

**接口地址**: `DELETE /personnel/{id}`

**路径参数**:
- `id`: 人员ID

**响应数据**:
```json
{
  "code": 200,
  "message": "删除成功"
}
```

### 2.6 批量操作

**接口地址**: `POST /personnel/batch`

**请求参数**:
```json
{
  "action": "addToBlacklist",
  "personnelIds": [1, 2, 3],
  "params": {
    "reason": "违规行为",
    "blacklistType": 1
  }
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "批量操作成功",
  "data": {
    "successCount": 3,
    "failCount": 0
  }
}
```

## 3. 背景审查结果模块

### 3.1 创建背景审查结果

**接口地址**: `POST /background-check`

**请求参数**:
```json
{
  "personnelId": 1,
  "mentalHealth": "正常",
  "criminalRecord": "无",
  "drugUse": "无",
  "creditRecord": "良好",
  "politicalBackground": "清白",
  "riskLevel": 1,
  "reviewDate": "2024-01-01",
  "reviewer": "审查员A",
  "remarks": "审查通过"
}
```

**响应数据**:
```json
{
  "code": 201,
  "message": "创建成功",
  "data": {
    "id": 1
  }
}
```

### 3.2 更新背景审查结果

**接口地址**: `PUT /background-check/{id}`

**路径参数**:
- `id`: 背景审查结果ID

**请求参数**:
```json
{
  "riskLevel": 2,
  "remarks": "需要进一步观察"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "更新成功"
}
```

## 4. 黑名单管理模块

### 4.1 获取黑名单列表

**接口地址**: `GET /blacklist`

**查询参数**:
```
page=1&pageSize=20&keyword=张三&blacklistType=1&status=1
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| keyword | string | 否 | 关键词搜索 |
| blacklistType | number | 否 | 黑名单类型：1-临时，2-永久 |
| status | number | 否 | 状态：1-生效，2-已解除 |

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "personnelId": 1,
        "personnelName": "张三",
        "personnelIdCard": "110101199001011234",
        "blacklistType": 1,
        "reason": "违规行为",
        "operator": "管理员A",
        "startDate": "2024-01-01",
        "endDate": "2024-12-31",
        "status": 1,
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

### 4.2 加入黑名单

**接口地址**: `POST /blacklist`

**请求参数**:
```json
{
  "personnelId": 1,
  "blacklistType": 1,
  "reason": "违规行为",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31"
}
```

**响应数据**:
```json
{
  "code": 201,
  "message": "加入黑名单成功",
  "data": {
    "id": 1
  }
}
```

### 4.3 移除黑名单

**接口地址**: `PUT /blacklist/{id}/remove`

**路径参数**:
- `id`: 黑名单记录ID

**请求参数**:
```json
{
  "reason": "问题已解决"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "移除黑名单成功"
}
```

### 4.4 黑名单预警检查

**接口地址**: `POST /blacklist/check`

**请求参数**:
```json
{
  "idCard": "110101199001011234"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "isBlacklisted": true,
    "blacklistInfo": {
      "id": 1,
      "reason": "违规行为",
      "blacklistType": 1,
      "startDate": "2024-01-01",
      "endDate": "2024-12-31"
    }
  }
}
```

## 5. 统计分析模块

### 5.1 获取总体统计数据

**接口地址**: `GET /statistics/overview`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalPersonnel": 1000,
    "normalPersonnel": 950,
    "blacklistPersonnel": 50,
    "personnelTypeDistribution": {
      "type1": 300,
      "type2": 400,
      "type3": 300
    },
    "riskDistribution": {
      "low": 800,
      "medium": 150,
      "high": 50
    }
  }
}
```

### 5.2 获取年度增量统计

**接口地址**: `GET /statistics/yearly-growth`

**查询参数**:
```
year=2024
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "year": 2024,
    "monthlyData": [
      {
        "month": 1,
        "newPersonnel": 50,
        "removedPersonnel": 5,
        "netGrowth": 45
      }
    ],
    "totalGrowth": 500,
    "growthRate": 0.15
  }
}
```

### 5.3 获取单位异常人员统计

**接口地址**: `GET /statistics/organization-risk`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "organization": "保卫科",
      "totalPersonnel": 100,
      "riskPersonnel": 10,
      "riskRate": 0.1,
      "riskDistribution": {
        "low": 5,
        "medium": 3,
        "high": 2
      }
    }
  ]
}
```

### 5.4 获取区域分布统计

**接口地址**: `GET /statistics/region-distribution`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "region": "北京市朝阳区",
      "totalPersonnel": 200,
      "riskPersonnel": 20,
      "coordinates": {
        "lng": 116.4074,
        "lat": 39.9042
      }
    }
  ]
}
```

## 6. 报表管理模块

### 6.1 获取报表模板列表

**接口地址**: `GET /reports/templates`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "背景审查汇总报表",
      "description": "包含所有人员的背景审查结果汇总",
      "type": "summary",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### 6.2 生成报表

**接口地址**: `POST /reports/generate`

**请求参数**:
```json
{
  "templateId": 1,
  "format": "pdf",
  "params": {
    "startDate": "2024-01-01",
    "endDate": "2024-12-31",
    "organization": "保卫科"
  }
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "报表生成成功",
  "data": {
    "reportId": "report_20240101_001",
    "downloadUrl": "https://example.com/reports/report_20240101_001.pdf",
    "expiresAt": "2024-01-08T00:00:00.000Z"
  }
}
```

### 6.3 获取报表生成状态

**接口地址**: `GET /reports/{reportId}/status`

**路径参数**:
- `reportId`: 报表ID

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "reportId": "report_20240101_001",
    "status": "completed",
    "progress": 100,
    "downloadUrl": "https://example.com/reports/report_20240101_001.pdf"
  }
}
```

## 7. 文件上传模块

### 7.1 上传人员照片

**接口地址**: `POST /upload/photo`

**请求参数**: FormData
- `file`: 图片文件（支持jpg、png格式，最大2MB）

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://example.com/photos/photo_20240101_001.jpg",
    "filename": "photo_20240101_001.jpg",
    "size": 1024000
  }
}
```

### 7.2 批量导入人员数据

**接口地址**: `POST /upload/personnel-import`

**请求参数**: FormData
- `file`: Excel文件

**响应数据**:
```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "totalCount": 100,
    "successCount": 95,
    "failCount": 5,
    "errors": [
      {
        "row": 10,
        "error": "身份证号格式错误"
      }
    ]
  }
}
```

## 8. 系统配置模块

### 8.1 获取系统配置

**接口地址**: `GET /config`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "personnelTypes": [
      { "value": 1, "label": "有编制人员" },
      { "value": 2, "label": "无编制人员" },
      { "value": 3, "label": "外包人员" }
    ],
    "riskLevels": [
      { "value": 1, "label": "低风险" },
      { "value": 2, "label": "中风险" },
      { "value": 3, "label": "高风险" }
    ],
    "educationLevels": [
      "小学", "初中", "高中", "大专", "本科", "硕士", "博士"
    ],
    "ethnicities": [
      "汉族", "蒙古族", "回族", "藏族", "维吾尔族", "苗族", "彝族", "壮族"
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如身份证号重复） |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 接口权限说明

| 权限代码 | 说明 |
|----------|------|
| personnel:read | 查看人员信息 |
| personnel:write | 编辑人员信息 |
| personnel:delete | 删除人员信息 |
| blacklist:read | 查看黑名单 |
| blacklist:write | 管理黑名单 |
| statistics:read | 查看统计数据 |
| reports:read | 查看报表 |
| reports:generate | 生成报表 |
| admin:all | 管理员权限 |

## 接口调用示例

### JavaScript/TypeScript

```typescript
// 获取人员列表
const getPersonnelList = async (params: any) => {
  const response = await fetch('/api/v1/personnel?' + new URLSearchParams(params), {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// 创建人员信息
const createPersonnel = async (data: any) => {
  const response = await fetch('/api/v1/personnel', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  return response.json();
};
```

### cURL

```bash
# 获取人员列表
curl -X GET "http://localhost:3000/api/v1/personnel?page=1&pageSize=20" \
  -H "Authorization: Bearer {token}"

# 创建人员信息
curl -X POST "http://localhost:3000/api/v1/personnel" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三",
    "phone": "13800138000",
    "idCard": "110101199001011234"
  }'
```

## 版本更新记录

### v1.0.0 (2024-01-01)
- 初始版本
- 基础CRUD接口
- 认证授权接口
- 统计分析接口
- 报表生成接口

---

**注意事项**：
1. 所有接口都需要进行身份认证，除了登录接口
2. 敏感数据传输必须使用HTTPS
3. 接口调用频率限制：每分钟最多1000次请求
4. 大文件上传建议使用分片上传
5. 长时间运行的任务（如报表生成）采用异步处理