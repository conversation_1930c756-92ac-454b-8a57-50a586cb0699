# 背审管理系统权限管理重构开发计划

## 📋 项目概述

本项目旨在对背审管理系统进行权限管理和流程重构，实现基于角色的差异化界面和功能控制，提升系统的安全性和用户体验。

## 🎯 开发目标

### 核心目标
1. **权限API集成**: 集成外部权限系统API，实现轻量级权限控制
2. **界面差异化**: 基于API权限信息实现不同角色的界面差异化
3. **流程重构**: 重构背审和处理流程，支持任务管理
4. **组件复用**: 最大化复用现有组件，减少开发成本

### 技术目标
1. **API集成**: 稳定可靠的外部权限API集成
2. **代码质量**: 高复用性、可维护性的代码结构
3. **性能优化**: 高效的API调用和权限缓存机制
4. **扩展性**: 便于后续功能扩展的设计

## 📅 详细开发计划

### 第一阶段：权限API集成基础架构（2天）

#### Day 1: 外部API集成和权限服务
**上午 (4小时)**
- [ ] 设计外部权限API调用接口
- [ ] 实现轻量级权限服务 (AuthService)
- [ ] 创建用户信息缓存机制

**下午 (4小时)**
- [ ] 实现Mock功能（开发阶段角色切换）
- [ ] 创建权限管理的Pinia Store
- [ ] 编写API调用和缓存工具函数

**预期产出**:
- `src/services/authService.ts` - 轻量级权限服务
- `src/stores/authStore.ts` - 权限状态管理
- `src/api/externalAuth.ts` - 外部API调用
- `src/utils/authCache.ts` - 权限缓存工具

#### Day 2: 菜单结构和工作台差异化
**上午 (4小时)**
- [ ] 基于API权限实现动态菜单渲染
- [ ] 实现路由守卫和权限检查
- [ ] 创建角色切换组件（Mock功能）

**下午 (4小时)**
- [ ] 创建管理员工作台组件
- [ ] 创建下级单位工作台组件
- [ ] 测试权限控制和界面差异化

**预期产出**:
- `src/config/menuConfig.ts` - 动态菜单配置
- `src/components/common/RoleSwitcher.vue` - 角色切换组件
- `src/views/admin/AdminDashboard.vue` - 管理员工作台
- `src/views/unit/UnitDashboard.vue` - 下级单位工作台
- `src/router/guards.ts` - 路由守卫

### 第二阶段：人员管理重构（2天）

#### Day 3: 人员分类页面
**上午 (4小时)**
- [ ] 创建专职保卫管理页面
- [ ] 创建保安人员管理页面
- [ ] 创建物流人员管理页面

**下午 (4小时)**
- [ ] 调整PersonnelTable组件支持分类过滤
- [ ] 实现多选功能
- [ ] 添加批量操作预留接口

**预期产出**:
- `src/views/personnel/SecurityGuard.vue` - 专职保卫页面
- `src/views/personnel/SecurityPersonnel.vue` - 保安人员页面  
- `src/views/personnel/LogisticsPersonnel.vue` - 物流人员页面
- 更新的 `PersonnelTable.vue`

#### Day 4: 组件优化和测试
**上午 (4小时)**
- [ ] 优化PersonnelTable组件的配置化
- [ ] 实现人员类型筛选逻辑
- [ ] 添加权限控制到人员管理

**下午 (4小时)**
- [ ] 测试人员分类功能
- [ ] 测试多选和批量操作
- [ ] 优化用户体验

**预期产出**:
- 完善的人员管理组件
- 测试报告和bug修复

### 第三阶段：任务管理功能开发（5天）

#### Day 5: 任务数据结构和API设计
**上午 (4小时)**
- [ ] 设计任务相关数据结构
- [ ] 设计任务管理API接口
- [ ] 创建任务Mock数据

**下午 (4小时)**
- [ ] 实现任务相关的API服务
- [ ] 创建任务状态管理Store
- [ ] 设计任务组件结构

**预期产出**:
- `src/types/task.ts` - 任务类型定义
- `src/api/taskService.ts` - 任务API服务
- `src/stores/taskStore.ts` - 任务状态管理
- `src/data/taskMockData.ts` - 任务Mock数据

#### Day 6: 管理员端任务管理 - 待背审
**上午 (4小时)**
- [ ] 创建待背审任务列表组件
- [ ] 实现任务创建功能
- [ ] 实现任务分配功能

**下午 (4小时)**
- [ ] 创建任务详情组件
- [ ] 实现任务查询和筛选
- [ ] 测试待背审功能

**预期产出**:
- `src/views/admin/tasks/PendingBackgroundCheck.vue`
- `src/components/tasks/TaskList.vue`
- `src/components/tasks/TaskCreate.vue`
- `src/components/tasks/TaskDetail.vue`

#### Day 7: 管理员端任务管理 - 待处理和已处理
**上午 (4小时)**
- [ ] 创建待处理任务管理页面
- [ ] 实现处理任务创建功能
- [ ] 实现任务状态跟踪

**下午 (4小时)**
- [ ] 创建已处理任务查询页面
- [ ] 实现任务重新分配功能
- [ ] 测试处理任务功能

**预期产出**:
- `src/views/admin/tasks/PendingProcessing.vue`
- `src/views/admin/tasks/CompletedTasks.vue`
- `src/components/tasks/TaskProcess.vue`

#### Day 8: 下级单位端工作处理 - 待背审
**上午 (4小时)**
- [ ] 创建下级单位待背审页面
- [ ] 实现任务接收和查看功能
- [ ] 实现背审结果上报功能

**下午 (4小时)**
- [ ] 创建任务处理表单组件
- [ ] 实现文件上传功能
- [ ] 测试背审上报功能

**预期产出**:
- `src/views/unit/work/PendingBackgroundCheck.vue`
- `src/components/tasks/TaskSubmit.vue`
- `src/components/tasks/FileUpload.vue`

#### Day 9: 下级单位端工作处理 - 待处理和已处理
**上午 (4小时)**
- [ ] 创建下级单位待处理页面
- [ ] 实现批量处理功能
- [ ] 实现处理结果提交

**下午 (4小时)**
- [ ] 创建已处理任务查询页面
- [ ] 实现任务历史记录查看
- [ ] 测试完整的任务流程

**预期产出**:
- `src/views/unit/work/PendingProcessing.vue`
- `src/views/unit/work/CompletedWork.vue`
- `src/components/tasks/BatchProcess.vue`

### 第四阶段：数据结构和API完善（2天）

#### Day 10: 数据结构调整
**上午 (4小时)**
- [ ] 设计任务相关数据库表结构
- [ ] 设计处理记录表结构
- [ ] 设计权限相关表结构

**下午 (4小时)**
- [ ] 编写数据库迁移脚本
- [ ] 更新现有数据结构
- [ ] 创建测试数据

**预期产出**:
- 数据库设计文档
- SQL迁移脚本
- 测试数据脚本

#### Day 11: API接口实现
**上午 (4小时)**
- [ ] 实现任务管理相关API
- [ ] 实现权限管理API
- [ ] 调整人员管理API

**下午 (4小时)**
- [ ] API接口测试
- [ ] 前后端联调
- [ ] 接口文档更新

**预期产出**:
- 完整的API接口实现
- API测试报告
- 更新的API文档

### 第五阶段：测试和优化（2天）

#### Day 12: 功能测试
**上午 (4小时)**
- [ ] 权限控制功能测试
- [ ] 任务管理流程测试
- [ ] 数据权限测试

**下午 (4小时)**
- [ ] 用户界面测试
- [ ] 兼容性测试
- [ ] 性能测试

**预期产出**:
- 功能测试报告
- Bug列表和修复计划

#### Day 13: 优化和部署准备
**上午 (4小时)**
- [ ] 性能优化
- [ ] 代码重构和清理
- [ ] 文档完善

**下午 (4小时)**
- [ ] 部署配置
- [ ] 用户手册编写
- [ ] 项目总结

**预期产出**:
- 优化后的系统
- 部署文档
- 用户手册
- 项目总结报告

## 🛠️ 技术实现要点

### 1. 权限管理实现
```typescript
// 权限服务核心实现
class AuthService {
  private currentUser: User | null = null
  private mockRole: 'admin' | 'unit' = 'admin'
  
  // 获取当前用户
  async getCurrentUser(): Promise<User> {
    if (!this.currentUser) {
      this.currentUser = await this.fetchUserInfo()
    }
    return this.currentUser
  }
  
  // 检查是否为管理员
  isAdmin(): boolean {
    return this.mockRole === 'admin'
  }
  
  // 检查权限
  hasPermission(permission: string): boolean {
    return this.currentUser?.permissions.includes(permission) || false
  }
  
  // 切换角色（开发阶段）
  switchRole(role: 'admin' | 'unit') {
    this.mockRole = role
    this.currentUser = null // 重置用户信息
    window.location.reload() // 重新加载页面
  }
}
```

### 2. 动态菜单实现
```typescript
// 菜单配置
const menuConfig = {
  admin: [
    {
      title: '工作台',
      path: '/admin/dashboard',
      icon: 'Dashboard'
    },
    {
      title: '背审人员管理',
      children: [
        { title: '专职保卫', path: '/personnel/security-guard' },
        { title: '保安人员', path: '/personnel/security' },
        { title: '物流人员', path: '/personnel/logistics' }
      ]
    },
    {
      title: '背审任务管理',
      children: [
        { title: '待背审', path: '/admin/tasks/pending-check' },
        { title: '待处理', path: '/admin/tasks/pending-process' },
        { title: '已处理', path: '/admin/tasks/completed' }
      ]
    }
  ],
  unit: [
    {
      title: '工作台',
      path: '/unit/dashboard',
      icon: 'Dashboard'
    },
    {
      title: '背审人员管理',
      children: [
        { title: '专职保卫', path: '/personnel/security-guard' },
        { title: '保安人员', path: '/personnel/security' },
        { title: '物流人员', path: '/personnel/logistics' }
      ]
    },
    {
      title: '背审工作处理',
      children: [
        { title: '待背审', path: '/unit/work/pending-check' },
        { title: '待处理', path: '/unit/work/pending-process' },
        { title: '已处理', path: '/unit/work/completed' }
      ]
    }
  ]
}
```

### 3. 组件复用策略
```typescript
// PersonnelTable组件增强
interface PersonnelTableProps {
  personnelType?: 'security_guard' | 'security' | 'logistics'
  viewMode?: 'admin' | 'unit'
  selectable?: boolean
  showActions?: ('view' | 'edit' | 'process')[]
  dataScope?: 'all' | 'organization' // 数据范围
}
```

## 📊 进度跟踪

### 里程碑
- **M1 (Day 2)**: 权限API集成基础架构完成
- **M2 (Day 4)**: 人员管理重构完成
- **M3 (Day 9)**: 任务管理功能完成
- **M4 (Day 11)**: 数据结构和API完成
- **M5 (Day 13)**: 系统测试和优化完成

### 风险控制
1. **API集成风险**: 外部API稳定性和响应时间，需要做好错误处理和降级方案
2. **进度风险**: 任务管理功能较复杂，可能需要额外时间
3. **质量风险**: 大量组件修改，需要回归测试

### 质量保证
1. **代码审查**: 每个功能模块完成后进行代码审查
2. **单元测试**: 关键功能编写单元测试
3. **集成测试**: 完整流程的集成测试
4. **用户测试**: 邀请用户进行功能测试

## 🎯 成功标准

### 功能标准
- [ ] 权限管理系统正常工作，角色切换功能正常
- [ ] 管理员和下级单位看到不同的界面和功能
- [ ] 人员管理按类型分类展示，支持多选
- [ ] 任务管理流程完整，状态跟踪准确
- [ ] 数据权限控制有效，安全性得到保障

### 性能标准
- [ ] 页面加载时间 < 2秒
- [ ] 权限检查响应时间 < 100ms
- [ ] 列表查询响应时间 < 1秒
- [ ] 系统并发支持 > 100用户

### 用户体验标准
- [ ] 界面友好，操作直观
- [ ] 权限提示清晰，错误处理完善
- [ ] 响应式设计，支持多种设备
- [ ] 无明显bug，系统稳定运行

## 📚 交付物清单

### 代码交付物
- [ ] 完整的前端代码
- [ ] 权限管理相关组件和服务
- [ ] 任务管理相关组件和页面
- [ ] 更新的人员管理组件
- [ ] 数据库设计和迁移脚本

### 文档交付物
- [ ] 需求分析文档
- [ ] 技术设计文档
- [ ] API接口文档
- [ ] 数据库设计文档
- [ ] 用户操作手册
- [ ] 部署指南
- [ ] 测试报告

### 其他交付物
- [ ] Mock数据和测试数据
- [ ] 配置文件和环境变量
- [ ] 构建和部署脚本
- [ ] 项目总结报告
