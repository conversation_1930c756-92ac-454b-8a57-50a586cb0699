# 统计报表页面使用说明

## 🚀 访问地址

**开发环境：** http://localhost:5174/statistical-reports

## 📋 使用步骤

### **第一步：选择报表类型**
1. 在左侧树形目录中点击要生成的报表
2. 可选择的报表类型：
   - 📄 异常人员名单报表
   - 📊 单位统计报表
   - 🏭 行业统计报表
   - ⚠️ 异常类型统计报表

### **第二步：配置参数**
1. 点击报表后，右侧会显示对应的参数配置表单
2. **必填参数**：时间范围（所有报表都需要）
3. **可选参数**：根据报表类型不同而不同
   - 区域选择（支持多选）
   - 异常类型筛选
   - 单位类型筛选
   - 统计维度选择

### **第三步：生成报表**
1. 配置完参数后，点击"生成报表"按钮
2. 系统会使用Mock数据生成测试报表
3. 生成成功后会在下方显示报表预览

### **第四步：查看和导出**
1. 在预览区域查看报表内容
2. 可以点击"导出Excel"按钮下载CSV文件
3. 可以点击"打印报表"按钮打印报表

## 📊 报表详情

### **1. 异常人员名单报表**
- **用途**：查看具体的异常人员详细信息
- **参数**：时间范围、区域、异常类型、处理状态
- **内容**：姓名、身份证号、所属单位、异常类型、处理状态等

### **2. 单位统计报表**
- **用途**：按单位统计异常人员分布情况
- **参数**：时间范围、区域、单位类型、统计维度
- **内容**：单位名称、总人员数、异常人员数、异常率、待处理人员等

### **3. 行业统计报表**
- **用途**：按行业分析异常人员分布和趋势
- **参数**：时间范围、统计维度、行业类型、区域
- **内容**：行业名称、总人员数、异常人员数、异常率、风险等级等

### **4. 异常类型统计报表**
- **用途**：分析各种异常类型的分布和处理情况
- **参数**：时间范围、统计维度、异常类型、区域
- **内容**：异常类型、人员数量、占比、处理进度、风险等级等

## 🎯 功能特点

### **智能筛选**
- 支持多维度参数筛选
- 实时生成符合条件的数据
- 参数重置功能

### **数据可视化**
- 彩色标签显示异常类型
- 进度条显示处理进度
- 风险等级颜色标识

### **导出功能**
- 支持CSV格式导出
- 兼容Excel打开
- 包含完整的表头和数据

### **响应式设计**
- 支持不同屏幕尺寸
- 移动端友好界面
- 自适应布局

## 🔧 技术特性

### **组件化架构**
- 每个报表都有独立的参数配置组件
- 每个报表都有独立的预览展示组件
- 模块化设计，易于维护和扩展

### **Mock数据生成**
- 智能生成真实的测试数据
- 支持参数筛选和过滤
- 随机生成姓名、身份证、单位等信息

### **性能优化**
- 按需加载组件
- 避免不必要的渲染
- 优化的数据结构

## 🐛 故障排除

### **问题1：页面空白或组件不显示**
- **原因**：组件导入路径错误或组件文件缺失
- **解决**：检查控制台错误信息，确认组件文件存在

### **问题2：生成报表后没有预览**
- **原因**：数据生成失败或预览条件不满足
- **解决**：检查控制台错误，确认参数配置正确

### **问题3：导出功能不工作**
- **原因**：浏览器阻止下载或数据为空
- **解决**：允许浏览器下载，确认有数据可导出

### **问题4：参数配置不显示**
- **原因**：未选择报表或组件加载失败
- **解决**：重新点击左侧树形目录中的报表项

## 📝 开发说明

### **文件结构**
```
src/
├── views/statistics/
│   └── StatisticalReports.vue          # 主页面
├── components/reports/                  # 报表组件
│   ├── AbnormalPersonnelParams.vue     # 异常人员参数
│   ├── AbnormalPersonnelPreview.vue    # 异常人员预览
│   ├── UnitStatisticsParams.vue        # 单位统计参数
│   ├── UnitStatisticsPreview.vue       # 单位统计预览
│   ├── IndustryStatisticsParams.vue    # 行业统计参数
│   ├── IndustryStatisticsPreview.vue   # 行业统计预览
│   ├── AbnormalTypeStatisticsParams.vue # 异常类型参数
│   └── AbnormalTypeStatisticsPreview.vue # 异常类型预览
└── utils/
    └── reportMockData.ts               # Mock数据生成器
```

### **扩展新报表**
1. 创建参数配置组件（xxxParams.vue）
2. 创建预览展示组件（xxxPreview.vue）
3. 在主页面中添加条件渲染
4. 在Mock数据生成器中添加对应的生成逻辑

## 🎉 总结

统计报表页面现在已经完全组件化，支持：
- ✅ 4种预置报表类型
- ✅ 动态参数配置
- ✅ 实时数据预览
- ✅ Excel导出功能
- ✅ 响应式设计
- ✅ Mock数据支持

所有功能都已经过测试，可以正常使用！
