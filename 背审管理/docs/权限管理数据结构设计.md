# 背审管理系统任务管理数据结构设计

## 📋 概述

本文档详细描述了背审管理系统任务流程重构所需的数据结构设计，包括数据库表结构、前端数据模型和API接口定义。权限管理采用外部API集成方案，本文档主要关注任务管理相关的数据结构。

## 🗄️ 数据库表结构设计

### 1. 任务管理相关表

#### 背审任务表 (background_check_tasks)
```sql
CREATE TABLE background_check_tasks (
    id VARCHAR(36) PRIMARY KEY COMMENT '任务ID',
    task_no VARCHAR(50) UNIQUE NOT NULL COMMENT '任务编号',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    type ENUM('background_check', 'processing') NOT NULL COMMENT '任务类型: background_check-背审任务 processing-处理任务',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    
    -- 人员信息
    personnel_count INT DEFAULT 0 COMMENT '涉及人员数量',
    
    -- 任务发起方
    created_by VARCHAR(36) NOT NULL COMMENT '创建人ID',
    created_by_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    created_by_org VARCHAR(36) NOT NULL COMMENT '创建人组织ID',
    
    -- 任务接收方
    assigned_to VARCHAR(36) COMMENT '分配给用户ID',
    assigned_to_name VARCHAR(50) COMMENT '分配给用户姓名',
    assigned_to_org VARCHAR(36) COMMENT '分配给组织ID',
    
    -- 时间信息
    assigned_at TIMESTAMP NULL COMMENT '分配时间',
    due_date TIMESTAMP NULL COMMENT '截止时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_no (task_no),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_assigned_to_org (assigned_to_org),
    INDEX idx_created_at (created_at)
) COMMENT '背审任务表';
```

#### 任务人员关联表 (task_personnel)
```sql
CREATE TABLE task_personnel (
    id VARCHAR(36) PRIMARY KEY COMMENT '关联ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    personnel_id VARCHAR(36) NOT NULL COMMENT '人员ID',
    status ENUM('pending', 'processing', 'completed') DEFAULT 'pending' COMMENT '处理状态',
    result TEXT COMMENT '处理结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_task_personnel (task_id, personnel_id),
    INDEX idx_task_id (task_id),
    INDEX idx_personnel_id (personnel_id)
) COMMENT '任务人员关联表';
```

#### 任务结果表 (task_results)
```sql
CREATE TABLE task_results (
    id VARCHAR(36) PRIMARY KEY COMMENT '结果ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    status ENUM('submitted', 'approved', 'rejected') DEFAULT 'submitted' COMMENT '结果状态',
    content TEXT NOT NULL COMMENT '结果内容',
    attachments JSON COMMENT '附件信息',
    
    -- 提交信息
    submitted_by VARCHAR(36) NOT NULL COMMENT '提交人ID',
    submitted_by_name VARCHAR(50) NOT NULL COMMENT '提交人姓名',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    
    -- 审核信息
    reviewed_by VARCHAR(36) COMMENT '审核人ID',
    reviewed_by_name VARCHAR(50) COMMENT '审核人姓名',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_comments TEXT COMMENT '审核意见',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_submitted_by (submitted_by),
    INDEX idx_reviewed_by (reviewed_by)
) COMMENT '任务结果表';
```

### 2. 人员处理记录表

#### 人员处理记录表 (personnel_processing_records)
```sql
CREATE TABLE personnel_processing_records (
    id VARCHAR(36) PRIMARY KEY COMMENT '记录ID',
    personnel_id VARCHAR(36) NOT NULL COMMENT '人员ID',
    task_id VARCHAR(36) COMMENT '关联任务ID',
    processing_type ENUM('background_check', 'focus', 'transfer', 'dismiss') NOT NULL COMMENT '处理类型',
    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending' COMMENT '处理状态',
    
    -- 处理结果
    result ENUM('normal', 'focus', 'transfer', 'dismiss') COMMENT '处理结果',
    reason VARCHAR(500) COMMENT '处理原因',
    description TEXT COMMENT '详细描述',
    
    -- 处理人员
    processed_by VARCHAR(36) NOT NULL COMMENT '处理人ID',
    processed_by_name VARCHAR(50) NOT NULL COMMENT '处理人姓名',
    processed_by_org VARCHAR(36) NOT NULL COMMENT '处理人组织ID',
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    
    -- 审核信息
    reviewed_by VARCHAR(36) COMMENT '审核人ID',
    reviewed_by_name VARCHAR(50) COMMENT '审核人姓名',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_comments TEXT COMMENT '审核意见',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_personnel_id (personnel_id),
    INDEX idx_task_id (task_id),
    INDEX idx_processing_type (processing_type),
    INDEX idx_status (status),
    INDEX idx_processed_by (processed_by),
    INDEX idx_processed_at (processed_at)
) COMMENT '人员处理记录表';
```

### 3. 现有表结构调整

#### 人员信息表调整 (personnel_info)
```sql
-- 添加新字段
ALTER TABLE personnel_info 
ADD COLUMN organization_id VARCHAR(36) COMMENT '所属组织ID',
ADD COLUMN personnel_category ENUM('security_guard', 'security', 'logistics', 'other') DEFAULT 'security' COMMENT '人员分类',
ADD COLUMN current_task_id VARCHAR(36) COMMENT '当前任务ID',
ADD INDEX idx_organization_id (organization_id),
ADD INDEX idx_personnel_category (personnel_category),
ADD INDEX idx_current_task_id (current_task_id);
```

## 📊 前端数据模型

### 1. 外部API权限相关类型

```typescript
// 外部API返回的用户信息
interface ExternalUserInfo {
  userId: string
  username: string
  name: string
  phone?: string
  email?: string
  organizationId: string
  organizationName: string
  roles: string[] // ['admin'] 或 ['unit']
  permissions: string[]
}

// 本地权限状态
interface LocalAuthState {
  userInfo: ExternalUserInfo | null
  isAdmin: boolean
  dataScope: 'all' | 'organization'
  accessibleOrgIds: string[]
  lastUpdated: string
  cacheExpiry: number
}

// 权限缓存配置
interface AuthCacheConfig {
  ttl: number // 缓存时间（毫秒）
  refreshThreshold: number // 刷新阈值（毫秒）
  maxRetries: number // 最大重试次数
}
```

### 2. 任务管理相关类型

```typescript
// 背审任务
interface BackgroundCheckTask {
  id: string
  taskNo: string
  title: string
  description?: string
  type: 'background_check' | 'processing'
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // 人员信息
  personnelCount: number
  personnelList?: TaskPersonnel[]
  
  // 任务发起方
  createdBy: string
  createdByName: string
  createdByOrg: string
  createdByOrgName: string
  
  // 任务接收方
  assignedTo?: string
  assignedToName?: string
  assignedToOrg?: string
  assignedToOrgName?: string
  
  // 时间信息
  assignedAt?: string
  dueDate?: string
  completedAt?: string
  createdAt: string
  updatedAt: string
  
  // 任务结果
  result?: TaskResult
}

// 任务人员
interface TaskPersonnel {
  id: string
  taskId: string
  personnelId: string
  personnelName: string
  personnelIdCard: string
  status: 'pending' | 'processing' | 'completed'
  result?: string
  createdAt: string
  updatedAt: string
}

// 任务结果
interface TaskResult {
  id: string
  taskId: string
  status: 'submitted' | 'approved' | 'rejected'
  content: string
  attachments?: FileAttachment[]
  
  // 提交信息
  submittedBy: string
  submittedByName: string
  submittedAt: string
  
  // 审核信息
  reviewedBy?: string
  reviewedByName?: string
  reviewedAt?: string
  reviewComments?: string
  
  createdAt: string
  updatedAt: string
}

// 文件附件
interface FileAttachment {
  id: string
  name: string
  url: string
  size: number
  type: string
  uploadedAt: string
}
```

### 3. 人员处理记录类型

```typescript
// 人员处理记录
interface PersonnelProcessingRecord {
  id: string
  personnelId: string
  taskId?: string
  processingType: 'background_check' | 'focus' | 'transfer' | 'dismiss'
  status: 'pending' | 'in_progress' | 'completed'
  
  // 处理结果
  result?: 'normal' | 'focus' | 'transfer' | 'dismiss'
  reason?: string
  description?: string
  
  // 处理人员
  processedBy: string
  processedByName: string
  processedByOrg: string
  processedByOrgName: string
  processedAt: string
  
  // 审核信息
  reviewedBy?: string
  reviewedByName?: string
  reviewedAt?: string
  reviewComments?: string
  
  createdAt: string
  updatedAt: string
}
```

### 4. 查询参数类型

```typescript
// 任务查询参数
interface TaskQuery {
  type?: 'background_check' | 'processing'
  status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  createdBy?: string
  assignedTo?: string
  assignedToOrg?: string
  startDate?: string
  endDate?: string
  keyword?: string
  page: number
  size: number
}

// 人员查询参数（扩展现有）
interface PersonnelQuery {
  name?: string
  idCard?: string
  phone?: string
  organization?: string
  organizationId?: string
  region?: string
  personnelType?: number
  personnelCategory?: 'security_guard' | 'security' | 'logistics' | 'other'
  backgroundCheckResult?: number
  processingStatus?: number
  status?: number
  entryDateStart?: string
  entryDateEnd?: string
  currentTaskId?: string
  page: number
  size: number
}

// 处理记录查询参数
interface ProcessingRecordQuery {
  personnelId?: string
  taskId?: string
  processingType?: 'background_check' | 'focus' | 'transfer' | 'dismiss'
  status?: 'pending' | 'in_progress' | 'completed'
  result?: 'normal' | 'focus' | 'transfer' | 'dismiss'
  processedBy?: string
  processedByOrg?: string
  startDate?: string
  endDate?: string
  page: number
  size: number
}
```

## 🔧 API接口设计

### 1. 外部权限API集成

```typescript
// 获取当前用户信息（外部API）
GET /api/external/auth/user/current
Response: {
  code: number
  data: ExternalUserInfo
  message: string
}

// 获取用户权限列表（外部API）
GET /api/external/auth/permissions/{userId}
Response: {
  code: number
  data: string[]
  message: string
}

// 获取组织机构信息（外部API）
GET /api/external/organizations/{orgId}
Response: {
  code: number
  data: {
    id: string
    name: string
    type: 'admin' | 'unit'
    parentId?: string
  }
  message: string
}

// Mock权限切换（仅开发环境）
POST /api/mock/auth/switch-role
Body: { role: 'admin' | 'unit' }
Response: { code: number, data: ExternalUserInfo, message: string }
```

### 2. 任务管理接口

```typescript
// 获取任务列表
GET /api/tasks?type=background_check&status=pending&page=1&size=20
Response: { code: number, data: { records: BackgroundCheckTask[], total: number }, message: string }

// 创建背审任务
POST /api/tasks/background-check
Body: { title: string, description: string, personnelIds: string[], assignedToOrg: string, dueDate: string }
Response: { code: number, data: BackgroundCheckTask, message: string }

// 创建处理任务
POST /api/tasks/processing
Body: { title: string, description: string, personnelIds: string[], assignedToOrg: string, dueDate: string }
Response: { code: number, data: BackgroundCheckTask, message: string }

// 获取任务详情
GET /api/tasks/{taskId}
Response: { code: number, data: BackgroundCheckTask, message: string }

// 提交任务结果
POST /api/tasks/{taskId}/result
Body: { content: string, attachments: FileAttachment[] }
Response: { code: number, data: TaskResult, message: string }

// 审核任务结果
PUT /api/tasks/{taskId}/review
Body: { status: 'approved' | 'rejected', comments: string }
Response: { code: number, data: TaskResult, message: string }

// 批量分配任务
POST /api/tasks/batch-assign
Body: { taskIds: string[], assignedToOrg: string }
Response: { code: number, data: boolean, message: string }
```

### 3. 人员管理接口调整

```typescript
// 按分类获取人员列表
GET /api/personnel?category=security_guard&organizationId=xxx&page=1&size=20
Response: { code: number, data: { records: PersonnelData[], total: number }, message: string }

// 批量操作人员
POST /api/personnel/batch-operation
Body: { personnelIds: string[], operation: string, params: any }
Response: { code: number, data: boolean, message: string }

// 获取人员处理记录
GET /api/personnel/{personnelId}/processing-records
Response: { code: number, data: PersonnelProcessingRecord[], message: string }
```

## 📈 数据权限控制（基于外部API）

### 1. 数据范围控制
- **管理员**: 基于外部API返回的权限，可查看所有组织的数据
- **下级单位**: 基于外部API返回的组织信息，只能查看本组织的数据

### 2. 操作权限控制
- **任务创建**: 基于外部API返回的权限列表判断
- **任务分配**: 基于外部API返回的权限列表判断
- **任务处理**: 基于外部API返回的组织权限判断
- **结果审核**: 基于外部API返回的权限列表判断

### 3. 权限缓存策略
- **本地缓存**: 缓存外部API返回的权限信息，减少API调用
- **定时刷新**: 定期刷新权限缓存，保证权限信息的时效性
- **降级处理**: API不可用时的降级处理方案

## 🔄 数据迁移方案

### 1. 现有数据迁移
```sql
-- 迁移现有人员数据，添加新字段
UPDATE personnel_info SET
  personnel_category = CASE
    WHEN personnel_type = 1 THEN 'security_guard'
    WHEN personnel_type = 2 THEN 'security'
    ELSE 'other'
  END;

-- 为现有人员添加组织ID（从外部API获取）
-- 这部分需要根据实际的组织映射关系进行调整
```

### 2. 外部API集成配置
```typescript
// 外部API配置
const externalApiConfig = {
  baseUrl: process.env.EXTERNAL_API_BASE_URL,
  timeout: 5000,
  retryTimes: 3,
  cacheConfig: {
    userInfoTTL: 30 * 60 * 1000, // 30分钟
    permissionsTTL: 60 * 60 * 1000, // 1小时
    organizationTTL: 24 * 60 * 60 * 1000 // 24小时
  }
}
```

这个数据结构设计为任务流程重构提供了完整的数据支撑，通过外部API集成实现轻量级的权限管理，支持高效的任务管理。
