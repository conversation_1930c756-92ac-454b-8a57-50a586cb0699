# 背审管理系统权限管理数据结构设计

## 📋 概述

本文档详细描述了背审管理系统权限管理和任务流程重构所需的数据结构设计，包括数据库表结构、前端数据模型和API接口定义。

## 🗄️ 数据库表结构设计

### 1. 用户和权限相关表

#### 用户表 (users)
```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    organization_id VARCHAR(36) NOT NULL COMMENT '所属组织ID',
    status TINYINT DEFAULT 1 COMMENT '状态: 1-正常 2-禁用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_organization_id (organization_id),
    INDEX idx_username (username),
    INDEX idx_status (status)
) COMMENT '用户表';
```

#### 角色表 (roles)
```sql
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY COMMENT '角色ID',
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '角色代码',
    description TEXT COMMENT '角色描述',
    level ENUM('admin', 'unit') NOT NULL COMMENT '角色级别: admin-管理员 unit-下级单位',
    status TINYINT DEFAULT 1 COMMENT '状态: 1-正常 2-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_level (level)
) COMMENT '角色表';
```

#### 权限表 (permissions)
```sql
CREATE TABLE permissions (
    id VARCHAR(36) PRIMARY KEY COMMENT '权限ID',
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) UNIQUE NOT NULL COMMENT '权限代码',
    resource VARCHAR(100) NOT NULL COMMENT '资源标识',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    description TEXT COMMENT '权限描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_code (code),
    INDEX idx_resource (resource)
) COMMENT '权限表';
```

#### 用户角色关联表 (user_roles)
```sql
CREATE TABLE user_roles (
    id VARCHAR(36) PRIMARY KEY COMMENT '关联ID',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    role_id VARCHAR(36) NOT NULL COMMENT '角色ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) COMMENT '用户角色关联表';
```

#### 角色权限关联表 (role_permissions)
```sql
CREATE TABLE role_permissions (
    id VARCHAR(36) PRIMARY KEY COMMENT '关联ID',
    role_id VARCHAR(36) NOT NULL COMMENT '角色ID',
    permission_id VARCHAR(36) NOT NULL COMMENT '权限ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
) COMMENT '角色权限关联表';
```

#### 组织机构表 (organizations)
```sql
CREATE TABLE organizations (
    id VARCHAR(36) PRIMARY KEY COMMENT '组织ID',
    name VARCHAR(200) NOT NULL COMMENT '组织名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '组织代码',
    type ENUM('admin', 'unit') NOT NULL COMMENT '组织类型: admin-管理机构 unit-下级单位',
    level INT NOT NULL DEFAULT 1 COMMENT '组织层级',
    parent_id VARCHAR(36) COMMENT '父级组织ID',
    address TEXT COMMENT '地址',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    status TINYINT DEFAULT 1 COMMENT '状态: 1-正常 2-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_type (type),
    INDEX idx_parent_id (parent_id)
) COMMENT '组织机构表';
```

### 2. 任务管理相关表

#### 背审任务表 (background_check_tasks)
```sql
CREATE TABLE background_check_tasks (
    id VARCHAR(36) PRIMARY KEY COMMENT '任务ID',
    task_no VARCHAR(50) UNIQUE NOT NULL COMMENT '任务编号',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    type ENUM('background_check', 'processing') NOT NULL COMMENT '任务类型: background_check-背审任务 processing-处理任务',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    
    -- 人员信息
    personnel_count INT DEFAULT 0 COMMENT '涉及人员数量',
    
    -- 任务发起方
    created_by VARCHAR(36) NOT NULL COMMENT '创建人ID',
    created_by_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    created_by_org VARCHAR(36) NOT NULL COMMENT '创建人组织ID',
    
    -- 任务接收方
    assigned_to VARCHAR(36) COMMENT '分配给用户ID',
    assigned_to_name VARCHAR(50) COMMENT '分配给用户姓名',
    assigned_to_org VARCHAR(36) COMMENT '分配给组织ID',
    
    -- 时间信息
    assigned_at TIMESTAMP NULL COMMENT '分配时间',
    due_date TIMESTAMP NULL COMMENT '截止时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_no (task_no),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_assigned_to_org (assigned_to_org),
    INDEX idx_created_at (created_at)
) COMMENT '背审任务表';
```

#### 任务人员关联表 (task_personnel)
```sql
CREATE TABLE task_personnel (
    id VARCHAR(36) PRIMARY KEY COMMENT '关联ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    personnel_id VARCHAR(36) NOT NULL COMMENT '人员ID',
    status ENUM('pending', 'processing', 'completed') DEFAULT 'pending' COMMENT '处理状态',
    result TEXT COMMENT '处理结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_task_personnel (task_id, personnel_id),
    INDEX idx_task_id (task_id),
    INDEX idx_personnel_id (personnel_id)
) COMMENT '任务人员关联表';
```

#### 任务结果表 (task_results)
```sql
CREATE TABLE task_results (
    id VARCHAR(36) PRIMARY KEY COMMENT '结果ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    status ENUM('submitted', 'approved', 'rejected') DEFAULT 'submitted' COMMENT '结果状态',
    content TEXT NOT NULL COMMENT '结果内容',
    attachments JSON COMMENT '附件信息',
    
    -- 提交信息
    submitted_by VARCHAR(36) NOT NULL COMMENT '提交人ID',
    submitted_by_name VARCHAR(50) NOT NULL COMMENT '提交人姓名',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    
    -- 审核信息
    reviewed_by VARCHAR(36) COMMENT '审核人ID',
    reviewed_by_name VARCHAR(50) COMMENT '审核人姓名',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_comments TEXT COMMENT '审核意见',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_submitted_by (submitted_by),
    INDEX idx_reviewed_by (reviewed_by)
) COMMENT '任务结果表';
```

### 3. 人员处理记录表

#### 人员处理记录表 (personnel_processing_records)
```sql
CREATE TABLE personnel_processing_records (
    id VARCHAR(36) PRIMARY KEY COMMENT '记录ID',
    personnel_id VARCHAR(36) NOT NULL COMMENT '人员ID',
    task_id VARCHAR(36) COMMENT '关联任务ID',
    processing_type ENUM('background_check', 'focus', 'transfer', 'dismiss') NOT NULL COMMENT '处理类型',
    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending' COMMENT '处理状态',
    
    -- 处理结果
    result ENUM('normal', 'focus', 'transfer', 'dismiss') COMMENT '处理结果',
    reason VARCHAR(500) COMMENT '处理原因',
    description TEXT COMMENT '详细描述',
    
    -- 处理人员
    processed_by VARCHAR(36) NOT NULL COMMENT '处理人ID',
    processed_by_name VARCHAR(50) NOT NULL COMMENT '处理人姓名',
    processed_by_org VARCHAR(36) NOT NULL COMMENT '处理人组织ID',
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    
    -- 审核信息
    reviewed_by VARCHAR(36) COMMENT '审核人ID',
    reviewed_by_name VARCHAR(50) COMMENT '审核人姓名',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_comments TEXT COMMENT '审核意见',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_personnel_id (personnel_id),
    INDEX idx_task_id (task_id),
    INDEX idx_processing_type (processing_type),
    INDEX idx_status (status),
    INDEX idx_processed_by (processed_by),
    INDEX idx_processed_at (processed_at)
) COMMENT '人员处理记录表';
```

### 4. 现有表结构调整

#### 人员信息表调整 (personnel_info)
```sql
-- 添加新字段
ALTER TABLE personnel_info 
ADD COLUMN organization_id VARCHAR(36) COMMENT '所属组织ID',
ADD COLUMN personnel_category ENUM('security_guard', 'security', 'logistics', 'other') DEFAULT 'security' COMMENT '人员分类',
ADD COLUMN current_task_id VARCHAR(36) COMMENT '当前任务ID',
ADD INDEX idx_organization_id (organization_id),
ADD INDEX idx_personnel_category (personnel_category),
ADD INDEX idx_current_task_id (current_task_id);
```

## 📊 前端数据模型

### 1. 用户和权限相关类型

```typescript
// 用户信息
interface User {
  id: string
  username: string
  name: string
  phone?: string
  email?: string
  organizationId: string
  organizationName: string
  organizationType: 'admin' | 'unit'
  roles: UserRole[]
  permissions: string[]
  status: number
  lastLoginAt?: string
  createdAt: string
}

// 用户角色
interface UserRole {
  id: string
  name: string
  code: string
  description?: string
  level: 'admin' | 'unit'
  permissions: Permission[]
}

// 权限
interface Permission {
  id: string
  name: string
  code: string
  resource: string
  action: string
  description?: string
}

// 组织机构
interface Organization {
  id: string
  name: string
  code: string
  type: 'admin' | 'unit'
  level: number
  parentId?: string
  address?: string
  contactPerson?: string
  contactPhone?: string
  status: number
  children?: Organization[]
}
```

### 2. 任务管理相关类型

```typescript
// 背审任务
interface BackgroundCheckTask {
  id: string
  taskNo: string
  title: string
  description?: string
  type: 'background_check' | 'processing'
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // 人员信息
  personnelCount: number
  personnelList?: TaskPersonnel[]
  
  // 任务发起方
  createdBy: string
  createdByName: string
  createdByOrg: string
  createdByOrgName: string
  
  // 任务接收方
  assignedTo?: string
  assignedToName?: string
  assignedToOrg?: string
  assignedToOrgName?: string
  
  // 时间信息
  assignedAt?: string
  dueDate?: string
  completedAt?: string
  createdAt: string
  updatedAt: string
  
  // 任务结果
  result?: TaskResult
}

// 任务人员
interface TaskPersonnel {
  id: string
  taskId: string
  personnelId: string
  personnelName: string
  personnelIdCard: string
  status: 'pending' | 'processing' | 'completed'
  result?: string
  createdAt: string
  updatedAt: string
}

// 任务结果
interface TaskResult {
  id: string
  taskId: string
  status: 'submitted' | 'approved' | 'rejected'
  content: string
  attachments?: FileAttachment[]
  
  // 提交信息
  submittedBy: string
  submittedByName: string
  submittedAt: string
  
  // 审核信息
  reviewedBy?: string
  reviewedByName?: string
  reviewedAt?: string
  reviewComments?: string
  
  createdAt: string
  updatedAt: string
}

// 文件附件
interface FileAttachment {
  id: string
  name: string
  url: string
  size: number
  type: string
  uploadedAt: string
}
```

### 3. 人员处理记录类型

```typescript
// 人员处理记录
interface PersonnelProcessingRecord {
  id: string
  personnelId: string
  taskId?: string
  processingType: 'background_check' | 'focus' | 'transfer' | 'dismiss'
  status: 'pending' | 'in_progress' | 'completed'
  
  // 处理结果
  result?: 'normal' | 'focus' | 'transfer' | 'dismiss'
  reason?: string
  description?: string
  
  // 处理人员
  processedBy: string
  processedByName: string
  processedByOrg: string
  processedByOrgName: string
  processedAt: string
  
  // 审核信息
  reviewedBy?: string
  reviewedByName?: string
  reviewedAt?: string
  reviewComments?: string
  
  createdAt: string
  updatedAt: string
}
```

### 4. 查询参数类型

```typescript
// 任务查询参数
interface TaskQuery {
  type?: 'background_check' | 'processing'
  status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  createdBy?: string
  assignedTo?: string
  assignedToOrg?: string
  startDate?: string
  endDate?: string
  keyword?: string
  page: number
  size: number
}

// 人员查询参数（扩展现有）
interface PersonnelQuery {
  name?: string
  idCard?: string
  phone?: string
  organization?: string
  organizationId?: string
  region?: string
  personnelType?: number
  personnelCategory?: 'security_guard' | 'security' | 'logistics' | 'other'
  backgroundCheckResult?: number
  processingStatus?: number
  status?: number
  entryDateStart?: string
  entryDateEnd?: string
  currentTaskId?: string
  page: number
  size: number
}

// 处理记录查询参数
interface ProcessingRecordQuery {
  personnelId?: string
  taskId?: string
  processingType?: 'background_check' | 'focus' | 'transfer' | 'dismiss'
  status?: 'pending' | 'in_progress' | 'completed'
  result?: 'normal' | 'focus' | 'transfer' | 'dismiss'
  processedBy?: string
  processedByOrg?: string
  startDate?: string
  endDate?: string
  page: number
  size: number
}
```

## 🔧 API接口设计

### 1. 权限管理接口

```typescript
// 获取当前用户信息
GET /api/auth/current-user
Response: { code: number, data: User, message: string }

// 获取用户权限列表
GET /api/auth/permissions
Response: { code: number, data: string[], message: string }

// 检查权限
POST /api/auth/check-permission
Body: { permission: string }
Response: { code: number, data: boolean, message: string }

// 切换角色（Mock开发用）
POST /api/auth/switch-role
Body: { role: 'admin' | 'unit' }
Response: { code: number, data: User, message: string }

// 获取组织机构列表
GET /api/organizations?type=unit&status=1
Response: { code: number, data: Organization[], message: string }
```

### 2. 任务管理接口

```typescript
// 获取任务列表
GET /api/tasks?type=background_check&status=pending&page=1&size=20
Response: { code: number, data: { records: BackgroundCheckTask[], total: number }, message: string }

// 创建背审任务
POST /api/tasks/background-check
Body: { title: string, description: string, personnelIds: string[], assignedToOrg: string, dueDate: string }
Response: { code: number, data: BackgroundCheckTask, message: string }

// 创建处理任务
POST /api/tasks/processing
Body: { title: string, description: string, personnelIds: string[], assignedToOrg: string, dueDate: string }
Response: { code: number, data: BackgroundCheckTask, message: string }

// 获取任务详情
GET /api/tasks/{taskId}
Response: { code: number, data: BackgroundCheckTask, message: string }

// 提交任务结果
POST /api/tasks/{taskId}/result
Body: { content: string, attachments: FileAttachment[] }
Response: { code: number, data: TaskResult, message: string }

// 审核任务结果
PUT /api/tasks/{taskId}/review
Body: { status: 'approved' | 'rejected', comments: string }
Response: { code: number, data: TaskResult, message: string }

// 批量分配任务
POST /api/tasks/batch-assign
Body: { taskIds: string[], assignedToOrg: string }
Response: { code: number, data: boolean, message: string }
```

### 3. 人员管理接口调整

```typescript
// 按分类获取人员列表
GET /api/personnel?category=security_guard&organizationId=xxx&page=1&size=20
Response: { code: number, data: { records: PersonnelData[], total: number }, message: string }

// 批量操作人员
POST /api/personnel/batch-operation
Body: { personnelIds: string[], operation: string, params: any }
Response: { code: number, data: boolean, message: string }

// 获取人员处理记录
GET /api/personnel/{personnelId}/processing-records
Response: { code: number, data: PersonnelProcessingRecord[], message: string }
```

## 📈 数据权限控制

### 1. 数据范围控制
- **管理员**: 可查看所有组织的数据
- **下级单位**: 只能查看本组织的数据

### 2. 操作权限控制
- **任务创建**: 仅管理员可创建任务
- **任务分配**: 仅管理员可分配任务
- **任务处理**: 被分配的下级单位可处理任务
- **结果审核**: 仅管理员可审核任务结果

### 3. 字段级权限
- **敏感信息**: 根据角色控制敏感字段的显示
- **操作记录**: 记录所有关键操作的日志

## 🔄 数据迁移方案

### 1. 现有数据迁移
```sql
-- 创建默认组织
INSERT INTO organizations (id, name, code, type, level) VALUES 
('default-admin', '管理机构', 'ADMIN', 'admin', 1),
('default-unit', '默认单位', 'UNIT001', 'unit', 2);

-- 迁移现有人员数据
UPDATE personnel_info SET 
  organization_id = 'default-unit',
  personnel_category = CASE 
    WHEN personnel_type = 1 THEN 'security_guard'
    WHEN personnel_type = 2 THEN 'security'
    ELSE 'other'
  END;
```

### 2. 权限数据初始化
```sql
-- 创建默认角色
INSERT INTO roles (id, name, code, level) VALUES 
('admin-role', '系统管理员', 'ADMIN', 'admin'),
('unit-role', '下级单位', 'UNIT', 'unit');

-- 创建默认用户
INSERT INTO users (id, username, name, organization_id) VALUES 
('admin-user', 'admin', '管理员', 'default-admin'),
('unit-user', 'unit001', '单位用户', 'default-unit');
```

这个数据结构设计为权限管理和任务流程重构提供了完整的数据支撑，支持灵活的权限控制和高效的任务管理。
