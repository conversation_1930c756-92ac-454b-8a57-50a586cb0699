# 报表打印导出功能实现说明

## 🎯 功能重构

### **原始需求**
1. 打印和导出功能应该在各个预览组件内部实现
2. 主页面只负责通知组件执行操作
3. 打印功能只打印预览内容，而不是整个窗口

### **实现方案**
1. ✅ **组件内部实现**：每个预览组件都有独立的打印和导出方法
2. ✅ **父子组件通信**：主页面通过ref调用子组件的方法
3. ✅ **精确打印**：只打印报表预览内容，不包含页面其他元素

## 🔧 技术实现

### **1. 组件方法暴露**

每个预览组件都通过 `defineExpose` 暴露打印和导出方法：

```typescript
// 暴露方法给父组件
defineExpose({
  handlePrint,
  handleExport
})
```

### **2. 父组件调用**

主页面通过ref引用调用子组件方法：

```typescript
// 预览组件引用
const previewComponentRef = ref()

// 处理打印报表
const handlePrintReport = () => {
  if (previewComponentRef.value && previewComponentRef.value.handlePrint) {
    previewComponentRef.value.handlePrint()
  } else {
    ElMessage.warning('当前报表不支持打印功能')
  }
}

// 处理导出报表
const handleExportReport = async () => {
  if (previewComponentRef.value && previewComponentRef.value.handleExport) {
    exporting.value = true
    try {
      await previewComponentRef.value.handleExport()
    } finally {
      exporting.value = false
    }
  } else {
    ElMessage.warning('当前报表不支持导出功能')
  }
}
```

### **3. 精确打印实现**

#### **打印原理**
1. 获取报表预览内容的DOM元素
2. 创建新的打印窗口
3. 将内容和样式写入打印窗口
4. 执行打印操作

#### **打印代码示例**
```typescript
const handlePrint = () => {
  // 获取要打印的内容
  const printContent = document.querySelector('.abnormal-personnel-preview .report-content')
  
  // 创建打印窗口
  const printWindow = window.open('', '_blank')
  
  // 写入HTML内容和样式
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>异常人员名单统计报表</title>
      <style>
        /* 打印专用样式 */
        body { font-family: Arial, sans-serif; margin: 20px; }
        .report-title { font-size: 20px; font-weight: bold; text-align: center; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      ${printContent.innerHTML}
    </body>
    </html>
  `)
  
  // 执行打印
  printWindow.document.close()
  printWindow.focus()
  printWindow.print()
  printWindow.close()
}
```

## 📊 各组件功能详情

### **1. 异常人员名单报表**

#### **打印功能**
- 打印标题：异常人员名单统计报表
- 打印内容：报表元信息 + 人员详细表格
- 特殊处理：异常类型标签样式优化

#### **导出功能**
- 文件名：异常人员名单报表_YYYY-MM-DD.csv
- 导出字段：序号、姓名、身份证号、所属单位、区域、异常类型、处理状态、入职日期

### **2. 单位统计报表**

#### **打印功能**
- 打印标题：单位异常人员统计报表
- 打印内容：统计元信息 + 单位统计表格
- 特殊处理：异常率颜色标识保留

#### **导出功能**
- 文件名：单位统计报表_YYYY-MM-DD.csv
- 导出字段：序号、单位名称、总人员数、异常人员数、异常率、待处理人员、所属区域、行业类型

### **3. 行业统计报表**

#### **打印功能**
- 打印标题：行业统计报表
- 打印内容：行业分析信息 + 行业统计表格
- 特殊处理：风险等级标签样式

#### **导出功能**
- 文件名：行业统计报表_YYYY-MM-DD.csv
- 导出字段：序号、行业名称、总人员数、异常人员数、异常率、待处理人员、风险等级

### **4. 异常类型统计报表**

#### **打印功能**
- 打印标题：异常类型统计报表
- 打印内容：类型分析信息 + 异常类型统计表格
- 特殊处理：占比进度条和处理进度条

#### **导出功能**
- 文件名：异常类型统计报表_YYYY-MM-DD.csv
- 导出字段：序号、异常类型、人员数量、占比、待处理数量、已处理数量、处理进度、风险等级

## 🎨 打印样式优化

### **通用样式**
- 字体：Arial, sans-serif
- 页边距：20px
- 标题：20px，粗体，居中
- 表格：全宽，边框合并，1px边框

### **特殊样式**
- 报表元信息：浅灰背景，圆角边框
- 表头：浅灰背景，粗体文字
- 异常率颜色：高风险红色，中风险橙色，低风险绿色
- 标签样式：背景色，白色文字，圆角

### **打印媒体查询**
```css
@media print {
  body { margin: 0; }
  .no-print { display: none; }
}
```

## 🚀 使用流程

### **打印报表**
1. 生成报表预览
2. 点击"打印报表"按钮
3. 系统自动打开打印窗口
4. 预览打印内容
5. 确认打印

### **导出报表**
1. 生成报表预览
2. 点击"导出Excel"按钮
3. 系统自动生成CSV文件
4. 浏览器下载文件
5. 用Excel打开查看

## ⚡ 性能优化

### **打印优化**
- 只获取必要的DOM内容
- 使用轻量级的打印样式
- 及时关闭打印窗口释放资源

### **导出优化**
- 在组件内部处理数据转换
- 使用Blob API高效生成文件
- 添加BOM头支持中文字符

### **内存管理**
- 及时清理创建的DOM元素
- 释放Blob对象URL
- 避免内存泄漏

## 🔍 错误处理

### **打印错误**
- DOM元素不存在：提示"无法找到打印内容"
- 打印窗口被阻止：提示"无法打开打印窗口"
- 组件方法不存在：提示"当前报表不支持打印功能"

### **导出错误**
- 数据为空：提示"暂无数据可导出"
- 文件生成失败：提示"导出报表失败"
- 组件方法不存在：提示"当前报表不支持导出功能"

## 📁 文件结构

```
src/
├── views/statistics/
│   └── StatisticalReports.vue          # 主页面（调用组件方法）
└── components/reports/
    ├── AbnormalPersonnelPreview.vue    # 异常人员预览（实现打印导出）
    ├── UnitStatisticsPreview.vue       # 单位统计预览（实现打印导出）
    ├── IndustryStatisticsPreview.vue   # 行业统计预览（实现打印导出）
    └── AbnormalTypeStatisticsPreview.vue # 异常类型预览（实现打印导出）
```

## ✅ 实现成果

### **架构优化**
✅ **职责分离**：主页面负责协调，组件负责具体实现  
✅ **方法暴露**：通过defineExpose暴露组件方法  
✅ **引用调用**：通过ref引用调用子组件方法  

### **功能完善**
✅ **精确打印**：只打印报表内容，不包含页面其他元素  
✅ **样式优化**：专门的打印样式，保证打印效果  
✅ **完整导出**：包含所有必要字段的CSV导出  

### **用户体验**
✅ **操作简单**：点击按钮即可打印或导出  
✅ **反馈及时**：成功和错误都有明确提示  
✅ **兼容性好**：支持各种浏览器和打印机  

## 🎉 总结

通过组件化的打印导出实现，我们达到了：

1. **更好的代码组织**：每个组件负责自己的打印导出逻辑
2. **更精确的打印**：只打印报表内容，排除无关元素
3. **更灵活的扩展**：新增报表类型时只需实现对应方法
4. **更好的维护性**：打印导出逻辑与组件紧密结合，便于维护

现在用户可以享受到专业级的报表打印和导出体验！
