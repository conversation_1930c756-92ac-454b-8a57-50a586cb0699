# 新增背景审查页面测试指南

## 🚀 测试环境

**开发服务器地址：** http://localhost:5174/

## 📋 测试页面列表

### **1. 医疗从业人员背景审查页面**
- **访问地址：** http://localhost:5174/medical-personnel
- **菜单路径：** 医疗机构背审 → 医疗从业人员
- **人员类型：** 全部人员、医生、护士、药师、技师
- **模拟数据：** 156条记录

### **2. 教职工背景审查页面**
- **访问地址：** http://localhost:5174/education-personnel
- **菜单路径：** 中小幼背审 → 教职工背审
- **人员类型：** 全部人员、教师、行政人员、后勤人员、其他人员
- **模拟数据：** 234条记录

### **3. 快递人员背景审查页面**
- **访问地址：** http://localhost:5174/delivery-personnel
- **菜单路径：** 寄递人员背审 → 快递人员背审管理
- **人员类型：** 全部人员、快递员、分拣员、配送员、客服人员
- **模拟数据：** 189条记录

## 🧪 功能测试步骤

### **第一步：页面访问测试**

1. **直接访问测试**
   - 在浏览器地址栏输入页面地址
   - 确认页面能正常加载，无JavaScript错误

2. **菜单导航测试**
   - 点击侧边栏对应的菜单项
   - 确认能正确跳转到目标页面

### **第二步：搜索筛选功能测试**

#### **基础搜索测试**
1. **姓名搜索**
   - 输入"李"，点击搜索
   - 确认结果中包含姓名带"李"的人员

2. **身份证号搜索**
   - 输入身份证号的部分数字
   - 确认能正确筛选

3. **所属单位搜索**
   - 输入单位名称关键词
   - 确认筛选结果正确

#### **下拉选择测试**
1. **区域筛选**
   - 选择"竞秀区"
   - 确认结果中只显示该区域的人员

2. **人员状态筛选**
   - 选择"在职"或"离职"
   - 确认状态标签显示正确

3. **背景审查结果筛选**
   - 选择"异常"
   - 确认异常类型选择框出现
   - 选择具体异常类型进行筛选

#### **日期范围测试**
1. **入职时间筛选**
   - 使用快捷选项（如"最近一个月"）
   - 使用自定义日期范围
   - 确认筛选结果正确

#### **重置功能测试**
1. 设置多个筛选条件
2. 点击"重置"按钮
3. 确认所有筛选条件被清空

### **第三步：数据表格功能测试**

#### **表格显示测试**
1. **列显示完整性**
   - 确认包含：区域、姓名、身份证号、手机号、所属单位、入职日期
   - 确认包含：在职状态、背审结果、处理状态

2. **标签显示测试**
   - **在职状态标签**：在职(绿色)、离职(灰色)
   - **背审结果标签**：未审查(橙色)、正常(绿色)、异常(红色)
   - **异常类型标签**：显示具体异常类型(红色)
   - **处理状态标签**：未处理(橙色)、无需处理(绿色)、重点关注(蓝色)、调岗/劝退(红色)

3. **操作列检查**
   - 确认**没有**编辑、删除、查看等操作按钮
   - 这是与SecurityPersonnel.vue的主要区别

#### **分页功能测试**
1. **分页控件**
   - 测试页码跳转
   - 测试每页显示数量调整(10/20/50/100)
   - 确认总数显示正确

2. **数据加载**
   - 切换页面时确认有加载状态
   - 确认数据正确更新

### **第四步：人员类型标签页测试**

#### **医疗人员页面**
- 点击"医生"标签，确认筛选出医生
- 点击"护士"标签，确认筛选出护士
- 点击"药师"标签，确认筛选出药师
- 点击"技师"标签，确认筛选出技师

#### **教职工页面**
- 点击"教师"标签，确认筛选出教师
- 点击"行政人员"标签，确认筛选出行政人员
- 点击"后勤人员"标签，确认筛选出后勤人员
- 点击"其他人员"标签，确认筛选出其他人员

#### **快递人员页面**
- 点击"快递员"标签，确认筛选出快递员
- 点击"分拣员"标签，确认筛选出分拣员
- 点击"配送员"标签，确认筛选出配送员
- 点击"客服人员"标签，确认筛选出客服人员

### **第五步：导出功能测试**

1. **导出按钮测试**
   - 点击"导出数据"按钮
   - 确认按钮显示加载状态
   - 确认显示成功提示消息

2. **筛选条件导出测试**
   - 设置筛选条件后导出
   - 确认导出的是筛选后的数据

## 🔍 错误检查清单

### **JavaScript错误检查**
- [ ] 打开浏览器开发者工具(F12)
- [ ] 查看Console标签页，确认无错误信息
- [ ] 查看Network标签页，确认API请求正常

### **页面显示检查**
- [ ] 页面布局正常，无样式错乱
- [ ] 所有按钮和输入框正常显示
- [ ] 表格数据正常加载和显示
- [ ] 标签颜色和文字正确

### **功能完整性检查**
- [ ] 所有搜索筛选功能正常工作
- [ ] 分页功能正常工作
- [ ] 标签页切换正常工作
- [ ] 导出功能正常工作

## 🎯 预期结果

### **成功标准**
1. **页面访问**：三个页面都能正常访问，无404错误
2. **数据显示**：每个页面都显示对应行业的模拟数据
3. **功能完整**：所有搜索、筛选、分页、导出功能正常工作
4. **界面一致**：与SecurityPersonnel.vue保持相同的视觉风格
5. **无操作列**：确认表格中没有编辑、删除等操作按钮

### **数据验证**
- **医疗人员**：单位名称包含"医院"、"中心"等医疗机构关键词
- **教职工**：单位名称包含"学校"、"中学"、"小学"等教育机构关键词
- **快递人员**：单位名称包含"快递"、"物流"、"速运"等物流公司关键词

## 🐛 常见问题排查

### **问题1：页面无法访问**
- **现象**：点击菜单或直接访问URL时出现404错误
- **排查**：检查路由配置是否正确，确认文件路径是否存在

### **问题2：JavaScript错误**
- **现象**：页面白屏或功能异常
- **排查**：查看浏览器控制台错误信息，检查导入的函数是否存在

### **问题3：数据不显示**
- **现象**：表格为空或一直显示加载状态
- **排查**：检查Mock数据生成函数是否正常工作

### **问题4：样式异常**
- **现象**：页面布局错乱或样式不一致
- **排查**：检查CSS样式是否正确加载，确认Element Plus组件正常

### **问题5：筛选功能异常**
- **现象**：筛选条件不生效或结果错误
- **排查**：检查筛选逻辑是否正确，确认参数传递正常

## ✅ 测试完成确认

完成所有测试后，请确认：

- [ ] 三个页面都能正常访问
- [ ] 所有搜索筛选功能正常
- [ ] 数据表格显示正确
- [ ] 分页功能正常
- [ ] 人员类型标签页正常
- [ ] 导出功能正常
- [ ] 无JavaScript错误
- [ ] 界面风格与原页面一致
- [ ] 已移除操作列

如果所有项目都通过测试，说明新增的背景审查页面开发成功！

## 🎉 测试成功后的使用

测试通过后，您可以：

1. **演示功能**：向用户展示新增的三个行业背景审查功能
2. **数据管理**：使用这些页面查看和管理不同行业的人员信息
3. **报表导出**：导出各行业的人员背景审查数据
4. **进一步开发**：基于这些页面继续开发更多功能

新增的页面为背景审查系统提供了更全面的行业覆盖，大大提升了系统的实用性！
