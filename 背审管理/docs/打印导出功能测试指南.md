# 打印导出功能测试指南

## 🚀 测试环境

**访问地址：** http://localhost:5174/statistical-reports

## 📋 测试步骤

### **第一步：生成报表**
1. 打开统计报表页面
2. 点击左侧树形目录中的任意报表（如"异常人员名单报表"）
3. 配置必要参数（至少选择时间范围）
4. 点击"生成报表"按钮
5. 确认下方显示报表预览内容

### **第二步：测试打印功能**
1. 在报表预览区域的右上角找到"打印报表"按钮
2. 点击"打印报表"按钮
3. **预期结果：**
   - 自动打开新的打印窗口
   - 打印窗口只包含报表内容（标题、元信息、表格）
   - 不包含页面导航、侧边栏等其他元素
   - 样式清晰，适合打印

### **第三步：测试导出功能**
1. 在报表预览区域的右上角找到"导出Excel"按钮
2. 点击"导出Excel"按钮
3. **预期结果：**
   - 按钮显示加载状态
   - 自动下载CSV文件
   - 文件名格式：报表类型_YYYY-MM-DD.csv
   - 用Excel打开文件，内容完整且格式正确

## 🧪 详细测试用例

### **测试用例1：异常人员名单报表**

#### **操作步骤：**
1. 选择"异常人员名单报表"
2. 设置时间范围：最近30天
3. 选择区域：保定市竞秀区
4. 选择异常类型：违法犯罪记录
5. 生成报表

#### **打印测试：**
- ✅ 打印窗口标题：异常人员名单统计报表
- ✅ 包含统计时间、生成时间、总人数等元信息
- ✅ 表格包含：序号、姓名、身份证号、单位、区域、异常类型、处理状态
- ✅ 异常类型标签样式正确

#### **导出测试：**
- ✅ 文件名：异常人员名单报表_2024-XX-XX.csv
- ✅ 包含8列：序号、姓名、身份证号、所属单位、区域、异常类型、处理状态、入职日期
- ✅ 中文字符显示正常
- ✅ 数据完整无缺失

### **测试用例2：单位统计报表**

#### **操作步骤：**
1. 选择"单位统计报表"
2. 设置时间范围：最近30天
3. 选择统计维度：按区域
4. 生成报表

#### **打印测试：**
- ✅ 打印窗口标题：单位异常人员统计报表
- ✅ 表格包含：序号、单位名称、总人员数、异常人员数、异常率、待处理人员、区域、行业
- ✅ 异常率颜色标识正确（高风险红色、中风险橙色、低风险绿色）

#### **导出测试：**
- ✅ 文件名：单位统计报表_2024-XX-XX.csv
- ✅ 包含8列数据
- ✅ 异常率以百分比形式显示

### **测试用例3：行业统计报表**

#### **操作步骤：**
1. 选择"行业统计报表"
2. 设置时间范围：最近30天
3. 选择统计维度：按行业
4. 生成报表

#### **打印测试：**
- ✅ 打印窗口标题：行业统计报表
- ✅ 表格包含风险等级标签
- ✅ 样式清晰易读

#### **导出测试：**
- ✅ 文件名：行业统计报表_2024-XX-XX.csv
- ✅ 包含风险等级文本（高风险/中风险/低风险）

### **测试用例4：异常类型统计报表**

#### **操作步骤：**
1. 选择"异常类型统计报表"
2. 设置时间范围：最近30天
3. 选择统计维度：按区域
4. 生成报表

#### **打印测试：**
- ✅ 打印窗口标题：异常类型统计报表
- ✅ 表格包含占比进度条和处理进度条
- ✅ 进度条在打印中显示为百分比

#### **导出测试：**
- ✅ 文件名：异常类型统计报表_2024-XX-XX.csv
- ✅ 包含处理进度百分比
- ✅ 包含风险等级评估

## 🔍 错误场景测试

### **测试用例5：无数据场景**

#### **操作步骤：**
1. 不生成任何报表
2. 直接点击打印或导出按钮

#### **预期结果：**
- ✅ 打印：提示"当前报表不支持打印功能"
- ✅ 导出：提示"当前报表不支持导出功能"

### **测试用例6：浏览器兼容性**

#### **测试浏览器：**
- Chrome（推荐）
- Firefox
- Safari
- Edge

#### **预期结果：**
- ✅ 所有浏览器都能正常打印
- ✅ 所有浏览器都能正常下载文件
- ✅ 打印样式在不同浏览器中一致

## 📊 验证要点

### **打印功能验证：**
1. **内容完整性**：所有报表数据都包含在打印内容中
2. **样式正确性**：表格边框、字体、颜色等样式正确
3. **布局合理性**：内容在打印页面中布局合理，不会被截断
4. **纯净性**：只包含报表内容，不包含页面其他元素

### **导出功能验证：**
1. **文件格式**：生成的是CSV格式文件
2. **文件命名**：文件名包含报表类型和日期
3. **数据完整**：所有表格数据都正确导出
4. **中文支持**：中文字符在Excel中正确显示
5. **格式兼容**：Excel能正确打开和显示

### **用户体验验证：**
1. **操作简单**：点击按钮即可完成操作
2. **反馈及时**：有明确的成功或错误提示
3. **性能良好**：打印和导出操作响应迅速
4. **错误处理**：异常情况有友好的错误提示

## 🐛 常见问题排查

### **问题1：打印窗口空白**
- **原因**：DOM元素选择器错误或内容为空
- **检查**：确认报表已生成且有数据
- **解决**：重新生成报表后再尝试打印

### **问题2：导出文件中文乱码**
- **原因**：编码问题
- **检查**：确认使用Excel打开，而不是记事本
- **解决**：使用Excel的"数据"->"从文本"功能导入

### **问题3：打印样式错误**
- **原因**：CSS样式冲突或浏览器兼容性
- **检查**：在不同浏览器中测试
- **解决**：使用Chrome浏览器获得最佳效果

### **问题4：导出按钮一直加载**
- **原因**：JavaScript错误或网络问题
- **检查**：打开浏览器开发者工具查看错误信息
- **解决**：刷新页面重新尝试

## ✅ 测试检查清单

### **基础功能测试**
- [ ] 异常人员名单报表打印正常
- [ ] 异常人员名单报表导出正常
- [ ] 单位统计报表打印正常
- [ ] 单位统计报表导出正常
- [ ] 行业统计报表打印正常
- [ ] 行业统计报表导出正常
- [ ] 异常类型统计报表打印正常
- [ ] 异常类型统计报表导出正常

### **样式和格式测试**
- [ ] 打印内容只包含报表，无其他页面元素
- [ ] 打印样式清晰，适合纸质输出
- [ ] 导出文件命名规范
- [ ] 导出数据完整准确
- [ ] 中文字符显示正常

### **错误处理测试**
- [ ] 无数据时的错误提示正确
- [ ] 浏览器兼容性良好
- [ ] 异常情况处理得当

## 🎉 测试完成

完成所有测试用例后，您应该能够：
1. 成功打印任何类型的报表
2. 成功导出任何类型的报表为Excel文件
3. 获得专业级的打印和导出体验

如果遇到任何问题，请检查浏览器控制台的错误信息，或参考常见问题排查部分。
