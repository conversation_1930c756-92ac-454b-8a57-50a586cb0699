# 背审结果处理模块需求分析

## 模块概述

背审结果处理模块是背审管理系统的重要组成部分，主要针对背审过程中发现异常的人员进行后续处理。该模块与背审管理模块紧密关联，复用其核心设计模式和组件。

## 业务背景

### 业务流程
1. **背审完成**: 背审任务完成后，系统识别出有异常的人员
2. **异常分类**: 根据异常类型和严重程度进行分类
3. **处理分配**: 将异常人员分配给相应的处理人员
4. **处理执行**: 处理人员对异常进行调查和处理
5. **结果确认**: 处理完成后确认处理结果
6. **再次处理**: 如需要可以对已处理的人员再次下发处理任务

### 核心价值
- **异常管控**: 确保所有背审异常都得到妥善处理
- **流程闭环**: 形成从发现异常到处理完成的完整闭环
- **责任追溯**: 明确处理责任人和处理过程
- **质量保证**: 通过再次处理机制确保处理质量

## 功能需求

### 1. 异常人员管理

#### 1.1 人员列表展示
- **状态分类**: 
  - 待处理：发现异常但尚未分配处理任务的人员
  - 已处理：已完成处理的人员
- **信息展示**:
  - 基本信息：姓名、身份证号、联系方式、所属机构
  - 异常信息：异常类型、异常等级、发现时间
  - 处理信息：处理状态、处理人、处理时间
  - 背审信息：关联的背审任务、背审结果

#### 1.2 搜索筛选功能
- **基础搜索**: 姓名、身份证号、联系方式
- **状态筛选**: 待处理、已处理
- **异常筛选**: 异常类型、异常等级
- **时间筛选**: 发现时间范围、处理时间范围
- **处理人筛选**: 按处理人或处理部门筛选

#### 1.3 批量操作
- **批量下发**: 选中多个待处理人员，批量下发处理任务
- **批量分配**: 批量分配给指定的处理人或部门
- **批量导出**: 导出异常人员信息和处理记录

### 2. 处理任务管理

#### 2.1 任务创建
- **任务信息**:
  - 任务标题、任务描述
  - 处理要求、处理期限
  - 优先级设置
- **人员关联**: 关联需要处理的异常人员
- **分配方式**:
  - 部门分配：分配给指定部门
  - 个人分配：分配给具体处理人员

#### 2.2 任务列表管理
- **任务状态**: 
  - 未完成：包含待分配、进行中等状态
  - 已完成：处理完成的任务
- **任务信息展示**:
  - 基本信息：任务编号、标题、状态、优先级
  - 人员信息：关联人员数量、处理进度
  - 时间信息：创建时间、截止时间、完成时间
  - 处理信息：处理人、处理部门

#### 2.3 任务操作
- **任务详情**: 查看任务详细信息和处理进度
- **任务撤销**: 撤销未完成的任务（需要填写原因）
- **催办功能**: 对逾期或延迟的任务进行催办
- **再次下发**: 对已完成的任务可以再次下发处理

### 3. 处理详情管理

#### 3.1 任务详情抽屉
包含三个Tab页面：

**Tab 1: 任务信息**
- 任务基本信息（编号、标题、描述、状态等）
- 处理要求和期限
- 创建人和分配信息
- 任务进度统计

**Tab 2: 处理历史**
- 处理记录：处理时间、处理人、处理内容
- 催办记录：催办时间、催办人、催办原因
- 状态变更：任务状态变更历史
- 反馈信息：处理人的反馈和说明

**Tab 3: 关联人员**
- 异常人员列表：显示所有关联的异常人员
- 人员搜索：支持姓名、身份证号搜索
- 处理状态：每个人员的具体处理状态
- 异常详情：查看具体的异常信息和处理结果

## 数据模型设计

### 1. 异常人员模型 (AbnormalPersonnel)
```typescript
interface AbnormalPersonnel {
  id: string
  // 基本信息
  name: string
  idCard: string
  phone: string
  organization: string
  
  // 异常信息
  abnormalType: string        // 异常类型
  abnormalLevel: 'low' | 'medium' | 'high' | 'critical'  // 异常等级
  abnormalDescription: string // 异常描述
  discoveredAt: string       // 发现时间
  
  // 背审关联
  backgroundCheckTaskId: string  // 关联的背审任务ID
  backgroundCheckResult: string  // 背审结果
  
  // 处理状态
  processingStatus: 'pending' | 'assigned' | 'processing' | 'completed'
  assignedTo?: string        // 分配给谁
  assignedToName?: string    // 分配人姓名
  assignedAt?: string        // 分配时间
  processedAt?: string       // 处理完成时间
  
  // 处理结果
  processingResult?: string   // 处理结果
  processingNote?: string     // 处理说明
}
```

### 2. 处理任务模型 (ProcessingTask)
```typescript
interface ProcessingTask {
  id: string
  taskNo: string
  title: string
  description?: string
  
  // 任务状态
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // 人员关联
  personnelCount: number
  personnelIds: string[]
  completedCount: number
  
  // 任务分配
  assignmentType: 'department' | 'individual'
  assignedToOrg?: string
  assignedToOrgName?: string
  assignedToUser?: string
  assignedToUserName?: string
  
  // 时间管理
  createdAt: string
  assignedAt?: string
  dueDate: string
  completedAt?: string
  
  // 进度统计
  progress: {
    total: number
    completed: number
    pending: number
    percentage: number
  }
  
  // 催办信息
  reminderCount?: number
  lastReminderAt?: string
  
  // 逾期状态
  isOverdue: boolean
  overdueBy?: number
}
```

## 页面设计

### 1. 异常人员管理页面 (AbnormalPersonnelPage)
**路由**: `/result-processing/personnel`

**页面结构**:
- 搜索表单区域
- 状态标签页（待处理、已处理）
- 表格标题行（统计信息、批量操作按钮）
- 人员列表表格
- 分页组件

**复用组件**:
- SearchForm：搜索表单
- PersonnelAvatar：人员头像
- 批量操作弹窗：复用StartCheckTaskDialog的设计模式

### 2. 处理任务管理页面 (ProcessingTaskPage)
**路由**: `/result-processing/tasks`

**页面结构**:
- 搜索表单区域
- 表格标题行（统计信息、操作按钮）
- 任务列表表格
- 分页组件
- 任务详情抽屉

**复用组件**:
- SearchForm：搜索表单
- TaskDetailDrawer：任务详情抽屉（调整为处理任务的内容）

## 技术实现方案

### 1. 组件复用策略
- **SearchForm**: 直接复用，调整配置项
- **PersonnelAvatar**: 直接复用
- **表格布局**: 复用样式和交互模式
- **弹窗组件**: 复用设计模式，调整业务逻辑

### 2. 数据管理
- **Mock数据**: 参考背审模块的数据结构设计
- **状态管理**: 复用选择状态保持等逻辑
- **API接口**: 设计与背审模块一致的接口规范

### 3. 路由配置
```typescript
{
  path: '/result-processing',
  name: 'ResultProcessing',
  children: [
    {
      path: 'personnel',
      name: 'AbnormalPersonnel',
      component: () => import('@/views/result-processing/AbnormalPersonnelPage.vue')
    },
    {
      path: 'tasks',
      name: 'ProcessingTasks',
      component: () => import('@/views/result-processing/ProcessingTaskPage.vue')
    }
  ]
}
```

## 开发计划

### 第一阶段：基础框架搭建
1. 创建页面组件和路由配置
2. 设计数据模型和Mock数据
3. 复用SearchForm等通用组件

### 第二阶段：异常人员管理
1. 实现人员列表展示和搜索
2. 实现状态标签页切换
3. 实现批量操作功能

### 第三阶段：处理任务管理
1. 实现任务列表展示和搜索
2. 实现任务创建和分配
3. 实现任务详情抽屉

### 第四阶段：功能完善
1. 实现催办和撤销功能
2. 完善错误处理和用户反馈
3. 优化用户体验和性能

## 验收标准

### 功能验收
- [ ] 异常人员列表正确展示和搜索
- [ ] 处理任务创建和分配功能正常
- [ ] 任务详情信息完整准确
- [ ] 批量操作功能正常
- [ ] 催办和撤销功能正常

### 性能验收
- [ ] 页面加载时间 < 2秒
- [ ] 搜索响应时间 < 1秒
- [ ] 大数据量下表格渲染流畅

### 用户体验验收
- [ ] 操作流程清晰直观
- [ ] 错误提示友好明确
- [ ] 响应式设计适配良好

## 风险评估

### 技术风险
- **组件复用兼容性**: 需要确保复用组件能适应新的业务场景
- **数据模型复杂性**: 异常处理的数据关系较为复杂

### 业务风险
- **需求变更**: 业务流程可能在开发过程中发生变化
- **用户接受度**: 新模块的用户体验需要与现有模块保持一致

### 缓解措施
- 充分复用已验证的组件和模式
- 保持与业务方的密切沟通
- 采用迭代开发，及时收集反馈
