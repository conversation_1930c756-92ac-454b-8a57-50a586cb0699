# 背景审查模块数据库设计文档

## 概述

本文档详细描述了背景审查模块的数据库设计，包括表结构、字段定义、索引策略、约束关系等。数据库采用MySQL 8.0，支持事务处理和高并发访问。

## 数据库基本信息

- **数据库名称**: `background_check_system`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **存储引擎**: `InnoDB`
- **数据库版本**: `MySQL 8.0+`

## 表结构设计

### 1. 人员信息表 (personnel_info)

存储安保人员的基本信息。

```sql
CREATE TABLE personnel_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    id_card VARCHAR(18) UNIQUE NOT NULL COMMENT '身份证号',
    ethnicity VARCHAR(20) NOT NULL COMMENT '民族',
    position VARCHAR(100) NOT NULL COMMENT '职位',
    security_company VARCHAR(200) COMMENT '保安公司',
    photo_url VARCHAR(500) COMMENT '人脸照片URL',
    education VARCHAR(50) NOT NULL COMMENT '学历',
    region VARCHAR(100) NOT NULL COMMENT '区域',
    address TEXT NOT NULL COMMENT '详细地址',
    political_status VARCHAR(50) NOT NULL COMMENT '政治面貌',
    organization VARCHAR(200) NOT NULL COMMENT '所属单位',
    personnel_type TINYINT NOT NULL COMMENT '人员类别：1-有编制，2-无编制，3-外包',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，2-黑名单，3-待审核',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    
    -- 索引
    INDEX idx_id_card (id_card),
    INDEX idx_name (name),
    INDEX idx_phone (phone),
    INDEX idx_organization (organization),
    INDEX idx_personnel_type (personnel_type),
    INDEX idx_status (status),
    INDEX idx_region (region),
    INDEX idx_created_at (created_at),
    
    -- 全文索引
    FULLTEXT idx_fulltext_search (name, organization, address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人员信息表';
```

**字段说明**:

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|---------|
| id | BIGINT | - | NO | AUTO_INCREMENT | 主键ID |
| name | VARCHAR | 50 | NO | - | 姓名 |
| phone | VARCHAR | 20 | NO | - | 手机号 |
| id_card | VARCHAR | 18 | NO | - | 身份证号（唯一） |
| ethnicity | VARCHAR | 20 | NO | - | 民族 |
| position | VARCHAR | 100 | NO | - | 职位 |
| security_company | VARCHAR | 200 | YES | NULL | 保安公司 |
| photo_url | VARCHAR | 500 | YES | NULL | 人脸照片URL |
| education | VARCHAR | 50 | NO | - | 学历 |
| region | VARCHAR | 100 | NO | - | 区域 |
| address | TEXT | - | NO | - | 详细地址 |
| political_status | VARCHAR | 50 | NO | - | 政治面貌 |
| organization | VARCHAR | 200 | NO | - | 所属单位 |
| personnel_type | TINYINT | - | NO | - | 人员类别 |
| status | TINYINT | - | NO | 1 | 状态 |

### 2. 背景审查结果表 (background_check_results)

存储人员的背景审查结果详情。

```sql
CREATE TABLE background_check_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    personnel_id BIGINT NOT NULL COMMENT '人员ID',
    mental_health VARCHAR(100) COMMENT '精神健康状况',
    criminal_record TEXT COMMENT '违法犯罪记录',
    drug_use VARCHAR(100) COMMENT '吸毒记录',
    credit_record VARCHAR(100) COMMENT '信用记录',
    political_background TEXT COMMENT '政治背景',
    risk_level TINYINT NOT NULL COMMENT '风险等级：1-低风险，2-中风险，3-高风险',
    review_date DATE NOT NULL COMMENT '审查日期',
    reviewer VARCHAR(50) NOT NULL COMMENT '审查人员',
    reviewer_id BIGINT COMMENT '审查人员ID',
    remarks TEXT COMMENT '备注信息',
    attachment_urls JSON COMMENT '附件URL列表',
    version INT DEFAULT 1 COMMENT '版本号',
    is_latest BOOLEAN DEFAULT TRUE COMMENT '是否最新版本',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    
    -- 外键约束
    FOREIGN KEY (personnel_id) REFERENCES personnel_info(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_personnel_id (personnel_id),
    INDEX idx_risk_level (risk_level),
    INDEX idx_review_date (review_date),
    INDEX idx_reviewer (reviewer),
    INDEX idx_is_latest (is_latest),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_personnel_latest (personnel_id, is_latest)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='背景审查结果表';
```

### 3. 黑名单表 (blacklist)

存储黑名单人员信息和相关操作记录。

```sql
CREATE TABLE blacklist (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    personnel_id BIGINT NOT NULL COMMENT '人员ID',
    blacklist_type TINYINT NOT NULL COMMENT '黑名单类型：1-临时，2-永久',
    reason TEXT NOT NULL COMMENT '加入黑名单原因',
    operator VARCHAR(50) NOT NULL COMMENT '操作人员',
    operator_id BIGINT COMMENT '操作人员ID',
    start_date DATE NOT NULL COMMENT '生效日期',
    end_date DATE COMMENT '失效日期（永久黑名单为NULL）',
    status TINYINT DEFAULT 1 COMMENT '状态：1-生效，2-已解除，3-已过期',
    remove_reason TEXT COMMENT '解除原因',
    remove_operator VARCHAR(50) COMMENT '解除操作人员',
    remove_date DATE COMMENT '解除日期',
    approval_status TINYINT DEFAULT 1 COMMENT '审批状态：1-待审批，2-已通过，3-已拒绝',
    approver VARCHAR(50) COMMENT '审批人',
    approval_date DATE COMMENT '审批日期',
    approval_remarks TEXT COMMENT '审批备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    
    -- 外键约束
    FOREIGN KEY (personnel_id) REFERENCES personnel_info(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_personnel_id (personnel_id),
    INDEX idx_blacklist_type (blacklist_type),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date),
    INDEX idx_operator (operator),
    INDEX idx_approval_status (approval_status),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_personnel_status (personnel_id, status),
    INDEX idx_date_range (start_date, end_date, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='黑名单表';
```

### 4. 操作日志表 (operation_logs)

记录系统中所有重要操作的日志信息。

```sql
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT COMMENT '操作用户ID',
    username VARCHAR(50) NOT NULL COMMENT '操作用户名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_module VARCHAR(50) NOT NULL COMMENT '操作模块',
    operation_desc TEXT NOT NULL COMMENT '操作描述',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id BIGINT COMMENT '目标ID',
    old_data JSON COMMENT '操作前数据',
    new_data JSON COMMENT '操作后数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params JSON COMMENT '请求参数',
    response_code INT COMMENT '响应状态码',
    execution_time INT COMMENT '执行时间(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_username (username),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_module (operation_module),
    INDEX idx_target_type_id (target_type, target_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_user_operation (user_id, operation_type, created_at),
    INDEX idx_module_time (operation_module, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
```

### 5. 系统用户表 (system_users)

存储系统用户信息和权限。

```sql
CREATE TABLE system_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    role VARCHAR(50) NOT NULL COMMENT '角色',
    permissions JSON COMMENT '权限列表',
    department VARCHAR(100) COMMENT '部门',
    position VARCHAR(100) COMMENT '职位',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，2-禁用，3-锁定',
    last_login_at TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    password_updated_at TIMESTAMP COMMENT '密码更新时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    
    -- 索引
    INDEX idx_username (username),
    INDEX idx_real_name (real_name),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_department (department),
    INDEX idx_last_login_at (last_login_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';
```

### 6. 系统配置表 (system_configs)

存储系统配置信息。

```sql
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型：string, number, boolean, json',
    config_group VARCHAR(50) COMMENT '配置分组',
    description TEXT COMMENT '配置描述',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统配置',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密存储',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    
    -- 索引
    INDEX idx_config_key (config_key),
    INDEX idx_config_group (config_group),
    INDEX idx_is_system (is_system),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';
```

### 7. 文件管理表 (file_management)

存储上传文件的信息。

```sql
CREATE TABLE file_management (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_url VARCHAR(500) COMMENT '文件访问URL',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    related_type VARCHAR(50) COMMENT '关联类型',
    related_id BIGINT COMMENT '关联ID',
    upload_user VARCHAR(50) COMMENT '上传用户',
    upload_ip VARCHAR(45) COMMENT '上传IP',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，2-已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_stored_name (stored_name),
    INDEX idx_file_hash (file_hash),
    INDEX idx_related_type_id (related_type, related_id),
    INDEX idx_upload_user (upload_user),
    INDEX idx_file_type (file_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件管理表';
```

### 8. 数据同步记录表 (data_sync_records)

记录数据同步的历史和状态。

```sql
CREATE TABLE data_sync_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    sync_type VARCHAR(50) NOT NULL COMMENT '同步类型',
    sync_source VARCHAR(100) NOT NULL COMMENT '同步来源',
    sync_status TINYINT NOT NULL COMMENT '同步状态：1-进行中，2-成功，3-失败',
    total_count INT DEFAULT 0 COMMENT '总记录数',
    success_count INT DEFAULT 0 COMMENT '成功记录数',
    fail_count INT DEFAULT 0 COMMENT '失败记录数',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration INT COMMENT '耗时（秒）',
    error_message TEXT COMMENT '错误信息',
    sync_details JSON COMMENT '同步详情',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_sync_type (sync_type),
    INDEX idx_sync_source (sync_source),
    INDEX idx_sync_status (sync_status),
    INDEX idx_start_time (start_time),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_type_status_time (sync_type, sync_status, start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步记录表';
```

### 9. 报表任务表 (report_tasks)

存储报表生成任务信息。

```sql
CREATE TABLE report_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    task_id VARCHAR(100) UNIQUE NOT NULL COMMENT '任务ID',
    template_id BIGINT COMMENT '报表模板ID',
    template_name VARCHAR(200) NOT NULL COMMENT '报表模板名称',
    report_name VARCHAR(200) NOT NULL COMMENT '报表名称',
    report_format VARCHAR(20) NOT NULL COMMENT '报表格式：pdf, excel, word',
    parameters JSON COMMENT '报表参数',
    status TINYINT DEFAULT 1 COMMENT '状态：1-待处理，2-处理中，3-已完成，4-失败',
    progress INT DEFAULT 0 COMMENT '进度百分比',
    file_path VARCHAR(500) COMMENT '生成文件路径',
    file_url VARCHAR(500) COMMENT '文件下载URL',
    file_size BIGINT COMMENT '文件大小',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration INT COMMENT '耗时（秒）',
    error_message TEXT COMMENT '错误信息',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    expires_at TIMESTAMP COMMENT '过期时间',
    
    -- 索引
    INDEX idx_task_id (task_id),
    INDEX idx_template_id (template_id),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_start_time (start_time),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报表任务表';
```

## 视图设计

### 1. 人员详情视图 (v_personnel_details)

```sql
CREATE VIEW v_personnel_details AS
SELECT 
    p.id,
    p.name,
    p.phone,
    p.id_card,
    p.ethnicity,
    p.position,
    p.security_company,
    p.photo_url,
    p.education,
    p.region,
    p.address,
    p.political_status,
    p.organization,
    p.personnel_type,
    p.status,
    p.created_at,
    p.updated_at,
    bcr.risk_level,
    bcr.mental_health,
    bcr.criminal_record,
    bcr.drug_use,
    bcr.credit_record,
    bcr.political_background,
    bcr.review_date,
    bcr.reviewer,
    bcr.remarks,
    bl.id as blacklist_id,
    bl.blacklist_type,
    bl.reason as blacklist_reason,
    bl.start_date as blacklist_start_date,
    bl.end_date as blacklist_end_date,
    bl.status as blacklist_status
FROM personnel_info p
LEFT JOIN background_check_results bcr ON p.id = bcr.personnel_id AND bcr.is_latest = TRUE
LEFT JOIN blacklist bl ON p.id = bl.personnel_id AND bl.status = 1;
```

### 2. 统计汇总视图 (v_statistics_summary)

```sql
CREATE VIEW v_statistics_summary AS
SELECT 
    COUNT(*) as total_personnel,
    COUNT(CASE WHEN status = 1 THEN 1 END) as normal_personnel,
    COUNT(CASE WHEN status = 2 THEN 1 END) as blacklist_personnel,
    COUNT(CASE WHEN personnel_type = 1 THEN 1 END) as type1_personnel,
    COUNT(CASE WHEN personnel_type = 2 THEN 1 END) as type2_personnel,
    COUNT(CASE WHEN personnel_type = 3 THEN 1 END) as type3_personnel,
    organization,
    region
FROM personnel_info
GROUP BY organization, region;
```

## 索引策略

### 1. 主键索引
所有表都使用自增BIGINT作为主键，确保唯一性和查询性能。

### 2. 唯一索引
- `personnel_info.id_card`: 身份证号唯一索引
- `system_users.username`: 用户名唯一索引
- `system_configs.config_key`: 配置键唯一索引

### 3. 普通索引
- 外键字段索引
- 常用查询字段索引
- 时间字段索引

### 4. 复合索引
- `(personnel_id, is_latest)`: 用于查询最新背景审查结果
- `(personnel_id, status)`: 用于查询特定状态的黑名单记录
- `(start_date, end_date, status)`: 用于黑名单日期范围查询

### 5. 全文索引
- `personnel_info`: 姓名、单位、地址全文搜索

## 数据约束

### 1. 外键约束
- `background_check_results.personnel_id` → `personnel_info.id`
- `blacklist.personnel_id` → `personnel_info.id`

### 2. 检查约束
```sql
-- 人员类别约束
ALTER TABLE personnel_info ADD CONSTRAINT chk_personnel_type 
CHECK (personnel_type IN (1, 2, 3));

-- 状态约束
ALTER TABLE personnel_info ADD CONSTRAINT chk_status 
CHECK (status IN (1, 2, 3));

-- 风险等级约束
ALTER TABLE background_check_results ADD CONSTRAINT chk_risk_level 
CHECK (risk_level IN (1, 2, 3));

-- 黑名单类型约束
ALTER TABLE blacklist ADD CONSTRAINT chk_blacklist_type 
CHECK (blacklist_type IN (1, 2));

-- 日期约束
ALTER TABLE blacklist ADD CONSTRAINT chk_date_range 
CHECK (end_date IS NULL OR end_date >= start_date);
```

## 触发器设计

### 1. 人员状态同步触发器

```sql
DELIMITER //
CREATE TRIGGER tr_update_personnel_status
AFTER INSERT ON blacklist
FOR EACH ROW
BEGIN
    IF NEW.status = 1 THEN
        UPDATE personnel_info 
        SET status = 2, updated_at = CURRENT_TIMESTAMP 
        WHERE id = NEW.personnel_id;
    END IF;
END//

CREATE TRIGGER tr_restore_personnel_status
AFTER UPDATE ON blacklist
FOR EACH ROW
BEGIN
    IF OLD.status = 1 AND NEW.status != 1 THEN
        UPDATE personnel_info 
        SET status = 1, updated_at = CURRENT_TIMESTAMP 
        WHERE id = NEW.personnel_id
        AND NOT EXISTS (
            SELECT 1 FROM blacklist 
            WHERE personnel_id = NEW.personnel_id 
            AND status = 1 
            AND id != NEW.id
        );
    END IF;
END//
DELIMITER ;
```

### 2. 背景审查结果版本控制触发器

```sql
DELIMITER //
CREATE TRIGGER tr_background_check_version
BEFORE INSERT ON background_check_results
FOR EACH ROW
BEGIN
    -- 将之前的记录标记为非最新
    UPDATE background_check_results 
    SET is_latest = FALSE 
    WHERE personnel_id = NEW.personnel_id AND is_latest = TRUE;
    
    -- 设置新记录为最新
    SET NEW.is_latest = TRUE;
    SET NEW.version = COALESCE(
        (SELECT MAX(version) + 1 FROM background_check_results WHERE personnel_id = NEW.personnel_id), 
        1
    );
END//
DELIMITER ;
```

## 存储过程

### 1. 批量导入人员数据

```sql
DELIMITER //
CREATE PROCEDURE sp_batch_import_personnel(
    IN p_data JSON,
    OUT p_success_count INT,
    OUT p_fail_count INT,
    OUT p_error_details JSON
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_index INT DEFAULT 0;
    DECLARE v_count INT;
    DECLARE v_personnel JSON;
    DECLARE v_error_list JSON DEFAULT JSON_ARRAY();
    
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
        
        SET v_error_list = JSON_ARRAY_APPEND(v_error_list, '$', 
            JSON_OBJECT('index', v_index, 'error', @text));
        SET p_fail_count = p_fail_count + 1;
    END;
    
    SET p_success_count = 0;
    SET p_fail_count = 0;
    SET v_count = JSON_LENGTH(p_data);
    
    WHILE v_index < v_count DO
        SET v_personnel = JSON_EXTRACT(p_data, CONCAT('$[', v_index, ']'));
        
        INSERT INTO personnel_info (
            name, phone, id_card, ethnicity, position, 
            security_company, education, region, address, 
            political_status, organization, personnel_type
        ) VALUES (
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.name')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.phone')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.idCard')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.ethnicity')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.position')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.securityCompany')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.education')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.region')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.address')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.politicalStatus')),
            JSON_UNQUOTE(JSON_EXTRACT(v_personnel, '$.organization')),
            JSON_EXTRACT(v_personnel, '$.personnelType')
        );
        
        SET p_success_count = p_success_count + 1;
        SET v_index = v_index + 1;
    END WHILE;
    
    SET p_error_details = v_error_list;
END//
DELIMITER ;
```

### 2. 统计数据生成

```sql
DELIMITER //
CREATE PROCEDURE sp_generate_statistics(
    IN p_start_date DATE,
    IN p_end_date DATE,
    OUT p_result JSON
)
BEGIN
    DECLARE v_total_personnel INT;
    DECLARE v_normal_personnel INT;
    DECLARE v_blacklist_personnel INT;
    DECLARE v_risk_distribution JSON;
    DECLARE v_type_distribution JSON;
    DECLARE v_region_distribution JSON;
    
    -- 总体统计
    SELECT 
        COUNT(*),
        COUNT(CASE WHEN status = 1 THEN 1 END),
        COUNT(CASE WHEN status = 2 THEN 1 END)
    INTO v_total_personnel, v_normal_personnel, v_blacklist_personnel
    FROM personnel_info
    WHERE created_at BETWEEN p_start_date AND p_end_date;
    
    -- 风险等级分布
    SELECT JSON_OBJECT(
        'low', COUNT(CASE WHEN bcr.risk_level = 1 THEN 1 END),
        'medium', COUNT(CASE WHEN bcr.risk_level = 2 THEN 1 END),
        'high', COUNT(CASE WHEN bcr.risk_level = 3 THEN 1 END)
    ) INTO v_risk_distribution
    FROM personnel_info p
    LEFT JOIN background_check_results bcr ON p.id = bcr.personnel_id AND bcr.is_latest = TRUE
    WHERE p.created_at BETWEEN p_start_date AND p_end_date;
    
    -- 人员类型分布
    SELECT JSON_OBJECT(
        'type1', COUNT(CASE WHEN personnel_type = 1 THEN 1 END),
        'type2', COUNT(CASE WHEN personnel_type = 2 THEN 1 END),
        'type3', COUNT(CASE WHEN personnel_type = 3 THEN 1 END)
    ) INTO v_type_distribution
    FROM personnel_info
    WHERE created_at BETWEEN p_start_date AND p_end_date;
    
    -- 区域分布
    SELECT JSON_ARRAYAGG(
        JSON_OBJECT(
            'region', region,
            'count', cnt
        )
    ) INTO v_region_distribution
    FROM (
        SELECT region, COUNT(*) as cnt
        FROM personnel_info
        WHERE created_at BETWEEN p_start_date AND p_end_date
        GROUP BY region
        ORDER BY cnt DESC
        LIMIT 20
    ) t;
    
    SET p_result = JSON_OBJECT(
        'totalPersonnel', v_total_personnel,
        'normalPersonnel', v_normal_personnel,
        'blacklistPersonnel', v_blacklist_personnel,
        'riskDistribution', v_risk_distribution,
        'typeDistribution', v_type_distribution,
        'regionDistribution', v_region_distribution,
        'generatedAt', NOW()
    );
END//
DELIMITER ;
```

## 数据初始化

### 1. 系统配置初始化

```sql
INSERT INTO system_configs (config_key, config_value, config_type, config_group, description) VALUES
('system.name', '背景审查管理系统', 'string', 'system', '系统名称'),
('system.version', '1.0.0', 'string', 'system', '系统版本'),
('personnel.types', '[{"value":1,"label":"有编制人员"},{"value":2,"label":"无编制人员"},{"value":3,"label":"外包人员"}]', 'json', 'personnel', '人员类型配置'),
('risk.levels', '[{"value":1,"label":"低风险"},{"value":2,"label":"中风险"},{"value":3,"label":"高风险"}]', 'json', 'risk', '风险等级配置'),
('education.levels', '["小学","初中","高中","大专","本科","硕士","博士"]', 'json', 'education', '学历配置'),
('ethnicities', '["汉族","蒙古族","回族","藏族","维吾尔族","苗族","彝族","壮族"]', 'json', 'ethnicity', '民族配置'),
('blacklist.auto_alert', 'true', 'boolean', 'blacklist', '黑名单自动预警'),
('report.retention_days', '30', 'number', 'report', '报表保留天数'),
('file.max_size', '10485760', 'number', 'file', '文件最大大小（字节）'),
('sync.interval', '3600', 'number', 'sync', '数据同步间隔（秒）');
```

### 2. 默认管理员用户

```sql
INSERT INTO system_users (username, password, real_name, role, permissions, department, status) VALUES
('admin', '$2b$10$encrypted_password_hash', '系统管理员', 'admin', 
 '["personnel:read","personnel:write","personnel:delete","blacklist:read","blacklist:write","statistics:read","reports:read","reports:generate","admin:all"]', 
 '系统管理部', 1);
```

## 性能优化建议

### 1. 查询优化
- 使用合适的索引
- 避免全表扫描
- 使用LIMIT限制结果集
- 合理使用JOIN

### 2. 存储优化
- 定期清理过期数据
- 使用分区表处理大数据量
- 压缩历史数据

### 3. 缓存策略
- 缓存热点数据
- 使用Redis缓存查询结果
- 实现查询结果缓存

### 4. 监控指标
- 慢查询监控
- 索引使用率
- 表空间使用情况
- 连接数监控

## 备份策略

### 1. 全量备份
- 每日凌晨进行全量备份
- 保留最近30天的备份文件
- 备份文件异地存储

### 2. 增量备份
- 每小时进行增量备份
- 基于binlog的增量备份
- 快速恢复能力

### 3. 备份脚本示例

```bash
#!/bin/bash
# 全量备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="background_check_system"

mysqldump -u backup_user -p$BACKUP_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  $DB_NAME > $BACKUP_DIR/full_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/full_backup_$DATE.sql

# 删除30天前的备份
find $BACKUP_DIR -name "full_backup_*.sql.gz" -mtime +30 -delete
```

---

**注意事项**：
1. 定期检查和优化索引使用情况
2. 监控数据库性能指标
3. 及时清理无用数据和日志
4. 确保备份策略的有效性
5. 定期进行数据库安全检查