#!/bin/bash

# 检查 .env 文件是否存在
if [ ! -f .env ]; then
    echo "错误: .env 文件不存在！"
    echo "请确保 .env 文件包含必要的环境变量配置。"
    exit 1
fi

# 停止并删除现有容器
echo "停止并删除现有容器..."
docker-compose down

# 删除现有镜像
echo "删除现有镜像..."
docker rmi datamanage-cursor-app || true

# 构建新镜像
echo "开始构建新镜像..."
if ! docker-compose build; then
    echo "错误: 镜像构建失败！"
    exit 1
fi

# 启动服务
echo "启动服务..."
if ! docker-compose up -d; then
    echo "错误: 服务启动失败！"
    exit 1
fi

# 检查服务是否正常运行
echo "检查服务状态..."
if ! docker-compose ps | grep -q "Up"; then
    echo "错误: 服务未能正常启动！"
    docker-compose logs
    exit 1
fi

echo "应用已成功部署！"
echo "前端访问地址: http://localhost:3000"
echo "API文档地址: http://localhost:3000/api" 