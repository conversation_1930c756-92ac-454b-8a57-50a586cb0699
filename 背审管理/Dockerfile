# 构建阶段
FROM node:20-alpine as builder

# 设置工作目录
WORKDIR /app

# 复制前端项目文件
COPY frontend/package*.json ./
RUN npm install
COPY frontend/ .
RUN npm run build

# 复制后端项目文件
WORKDIR /app/backend
COPY backend/package*.json ./
RUN npm install --omit=dev --no-audit --no-optional
COPY backend/ .
RUN npm run build

# 创建静态文件目录
RUN mkdir -p /app/public
# 复制前端构建文件到后端的静态文件目录
RUN cp -r ../dist/* /app/public/

# 生产阶段
FROM node:20-alpine

WORKDIR /app

# 只复制必要的文件
COPY --from=builder /app/public ./public
COPY --from=builder /app/backend/dist ./dist
COPY --from=builder /app/backend/package*.json ./
COPY --from=builder /app/backend/node_modules ./node_modules


# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000

# 清理不必要的文件
RUN rm -rf /root/.npm /root/.cache && \
    rm -rf /var/cache/apk/* && \
    rm -rf /tmp/*

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["node", "dist/main"] 