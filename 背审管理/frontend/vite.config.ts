import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    plugins: [
      vue(),
      vueJsx(),
      vueDevTools(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
      extensions: ['.js', '.json', '.vue', '.ts'], // 添加 '.vue' 和 '.ts' 后缀
    },
    server: {
      proxy: {
        '/magic-api': {
          target: 'http://101.126.139.172:9999',
          changeOrigin: true,
          secure: false,
        }
      }
    },
    base: env.VITE_BASE_PATH,
  }
})
