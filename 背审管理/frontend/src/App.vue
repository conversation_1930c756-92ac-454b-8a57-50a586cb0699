<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  HomeFilled,
  UserFilled,
  Document,
  Warning,
  DataAnalysis,
  Notebook,
  ArrowRight,
  ArrowLeft,
  ArrowDown,
  User,
  SwitchButton,
  View,
  FirstAidKit,
  School,
  Box,
  Van
} from '@element-plus/icons-vue'

const isCollapse = ref(false)
const route = useRoute()
const currentModule = ref('')
const currentPage = ref('')

// 声明window类型
declare global {
  interface Window {
    __POWERED_BY_WUJIE__?: boolean
  }
}

// 监听路由变化，更新面包屑
watch(() => route.path, (path) => {
  const parts = path.split('/').filter(Boolean)
  if (parts.length > 0) {
    currentModule.value = parts[0]
    if (parts.length > 1) {
      currentPage.value = parts[1]
    }
  }
}, { immediate: true })

// 根据路径获取菜单标题
const getMenuTitle = (module: string, page: string = '') => {
  const moduleMap: { [key: string]: string } = {
    'dashboard': '工作台',
    'security-personnel': '安保人员管理',
    'pending-personnel': '待处理人员',
    'focus-personnel': '背审关注人员',
    'data-dashboard': '数据看板',
    'statistical-reports': '统计报表',
    'special-reports': '专项报表',
    'medical-staff': '医疗从业人员',
    'medical-security': '医疗安保人员',
    'education-staff': '教职工背审',
    'education-security': '中小幼安保人员',
    'logistics-staff': '快递人员背审管理'
  }

  const pageMap: { [key: string]: { [key: string]: string } } = {
    'security-audit': {
      'dashboard': '工作台',
      'security-personnel': '安保人员管理',
      'pending-personnel': '待处理人员',
      'focus-personnel': '背审关注人员'
    },
    'statistics': {
      'data-dashboard': '数据看板',
      'statistical-reports': '统计报表',
      'special-reports': '专项报表'
    }
  }

  return {
    module: moduleMap[module] || module,
    page: pageMap[module]?.[page] || page
  }
}

// 计算当前面包屑标题
const breadcrumbTitles = computed(() => {
  return getMenuTitle(currentModule.value, currentPage.value)
})

const fromWujie = ref(window.__POWERED_BY_WUJIE__)
console.log("fromWujie", fromWujie.value)
</script>

<template>
  <el-container v-if="fromWujie" style="height: 100%;">
    <el-main class="wenjieappmain">
      <RouterView />
    </el-main>
  </el-container>
  <el-container class="app-container" v-else>
    <el-aside :width="isCollapse ? '64px' : '200px'" class="app-aside">
      <div class="logo-container">
        <h1 v-if="!isCollapse">安保背审管理系统</h1>
        <h1 v-else>安保</h1>
      </div>
      <el-scrollbar class="menu-scrollbar">
        <el-menu :default-active="route.path" class="app-menu" unique-opened="true" :collapse="isCollapse"
          :collapse-transition="false" router>

          <!-- 安保背审模块 -->
          <el-sub-menu index="/security-audit">
            <template #title>
              <el-icon>
                <UserFilled />
              </el-icon>
              <span>安保背审</span>
            </template>
            <el-menu-item index="/dashboard">
              <el-icon>
                <HomeFilled />
              </el-icon>工作台
            </el-menu-item>
            <!-- 背审人员管理子菜单 -->
            <el-sub-menu index="/personnel">
              <template #title>
                <el-icon>
                  <User />
                </el-icon>背审人员管理
              </template>
              <el-menu-item index="/personnel/security-guard">
                <el-icon>
                  <UserFilled />
                </el-icon>专职保卫
              </el-menu-item>
              <el-menu-item index="/personnel/security-personnel">
                <el-icon>
                  <User />
                </el-icon>保安人员
              </el-menu-item>
              <el-menu-item index="/personnel/logistics-personnel">
                <el-icon>
                  <Van />
                </el-icon>物流人员
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item index="/pending-personnel">
              <el-icon>
                <Warning />
              </el-icon>待处理人员
            </el-menu-item>
            <el-menu-item index="/focus-personnel">
              <el-icon>
                <View />
              </el-icon>背审关注人员
            </el-menu-item>
          </el-sub-menu>

          <!-- 背审管理模块 -->
          <el-sub-menu index="/background-check">
            <template #title>
              <el-icon>
                <Document />
              </el-icon>
              <span>背审管理</span>
            </template>
            <el-menu-item index="/background-check/personnel">
              <el-icon>
                <User />
              </el-icon>背审人员
            </el-menu-item>
            <el-menu-item index="/background-check/tasks">
              <el-icon>
                <Notebook />
              </el-icon>背审任务
            </el-menu-item>
          </el-sub-menu>

          <!-- 统计报表模块 -->
          <el-sub-menu index="/statistics">
            <template #title>
              <el-icon>
                <DataAnalysis />
              </el-icon>
              <span>统计报表</span>
            </template>
            <el-menu-item index="/data-dashboard">
              <el-icon>
                <DataAnalysis />
              </el-icon>数据看板
            </el-menu-item>
            <el-menu-item index="/statistical-reports">
              <el-icon>
                <Document />
              </el-icon>统计报表
            </el-menu-item>
            <el-menu-item index="/special-reports">
              <el-icon>
                <Notebook />
              </el-icon>专项报表
            </el-menu-item>
          </el-sub-menu>

          <!-- 医疗机构背审模块 -->
          <el-sub-menu index="/medical">
            <template #title>
              <el-icon>
                <FirstAidKit />
              </el-icon>
              <span>医疗机构背审</span>
            </template>
            <el-menu-item index="/medical-personnel">
              <el-icon>
                <User />
              </el-icon>医疗从业人员
            </el-menu-item>
            <el-menu-item index="/medical-security">
              <el-icon>
                <UserFilled />
              </el-icon>医疗安保人员
            </el-menu-item>
          </el-sub-menu>

          <!-- 中小幼背审模块 -->
          <el-sub-menu index="/education">
            <template #title>
              <el-icon>
                <School />
              </el-icon>
              <span>中小幼背审</span>
            </template>
            <el-menu-item index="/education-personnel">
              <el-icon>
                <User />
              </el-icon>教职工背审
            </el-menu-item>
            <el-menu-item index="/education-security">
              <el-icon>
                <UserFilled />
              </el-icon>中小幼安保人员
            </el-menu-item>
          </el-sub-menu>

          <!-- 寄递人员背审模块 -->
          <el-sub-menu index="/logistics">
            <template #title>
              <el-icon>
                <Box />
              </el-icon>
              <span>寄递人员背审</span>
            </template>
            <el-menu-item index="/delivery-personnel">
              <el-icon>
                <User />
              </el-icon>快递人员背审管理
            </el-menu-item>
          </el-sub-menu>

        </el-menu>
      </el-scrollbar>
      <div class="collapse-btn" @click="isCollapse = !isCollapse">
        <el-icon v-if="isCollapse">
          <ArrowRight />
        </el-icon>
        <el-icon v-else>
          <ArrowLeft />
        </el-icon>
      </div>
    </el-aside>
    <el-container>
      <el-header class="app-header">
        <div class="header-left">
          <el-breadcrumb>
            <el-breadcrumb-item>首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="breadcrumbTitles.module">{{ breadcrumbTitles.module }}</el-breadcrumb-item>
            <el-breadcrumb-item v-if="breadcrumbTitles.page">{{ breadcrumbTitles.page }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-dropdown>
            <span class="user-dropdown">
              管理员 <el-icon>
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-icon>
                    <User />
                  </el-icon>个人信息
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-icon>
                    <SwitchButton />
                  </el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      <el-main class="app-main">
        <RouterView />
      </el-main>
    </el-container>
  </el-container>
</template>

<style lang="scss" scoped>
.app-container {
  height: 100vh;
  width: 100vw;
}

.app-aside {
  background-color: var(--system-sidebar-bg);
  color: #fff;
  transition: width 0.3s;
  position: relative;
  overflow: hidden;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3f6a;
}

.logo-container h1 {
  font-size: 16px;
  color: #fff;
  margin: 0;
  white-space: nowrap;
}

.menu-scrollbar {
  height: calc(100vh - 120px);
}

.app-menu {
  border-right: none;
  background-color: transparent;
}

.app-menu :deep(.el-sub-menu__title) {
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.app-menu :deep(.el-menu-item) {
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &.is-active {
    background-color: var(--system-sidebar-active);
    color: #ffffff;
  }
}

.collapse-btn {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #2b3f6a;
  color: #fff;

  &:hover {
    background-color: #36507f;
  }
}

.app-header {
  background-color: #fff;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  .el-breadcrumb {
    font-size: 14px;
  }
}

.header-right {
  .user-dropdown {
    display: flex;
    align-items: center;
    cursor: pointer;

    .el-icon {
      margin-left: 4px;
    }
  }
}

.app-main {
  background-color: #f5f7fa;
  padding: 15px;
  overflow-y: auto;
}

.wenjieappmain {
  padding: 20px 20px 60px 20px;
  background-color: #f5f7fa;
}
</style>
