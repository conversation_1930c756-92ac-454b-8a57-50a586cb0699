// 权限状态管理

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authService from '@/services/authService'
import type { UserInfo, UserRole, DataScope } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<UserInfo | null>(null)
  const loading = ref(false)
  const initialized = ref(false)

  // 计算属性
  const isAdmin = computed(() => user.value?.roles.includes('admin') || false)
  const isLoggedIn = computed(() => !!user.value)
  const currentRole = computed(() => authService.getCurrentRole())
  const userPermissions = computed(() => user.value?.permissions || [])
  const dataScope = computed(() => authService.getDataScope())
  
  const currentOrganization = computed(() => ({
    id: user.value?.organizationId || '',
    name: user.value?.organizationName || ''
  }))

  // 方法
  const initAuth = async () => {
    if (initialized.value) return
    
    try {
      loading.value = true
      user.value = await authService.getCurrentUser()
      initialized.value = true
      console.log('✅ 权限初始化完成:', user.value)
    } catch (error) {
      console.error('❌ 权限初始化失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const switchRole = async (role: UserRole) => {
    try {
      loading.value = true
      authService.switchRole(role)
      user.value = await authService.getCurrentUser()
      console.log(`✅ 角色切换成功: ${role}`, user.value)
    } catch (error) {
      console.error('❌ 角色切换失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const hasPermission = (permission: string): boolean => {
    return authService.hasPermission(permission)
  }

  const getAccessibleOrganizationIds = async (): Promise<string[]> => {
    return await authService.getAccessibleOrganizationIds()
  }

  const refreshUserInfo = async () => {
    try {
      loading.value = true
      user.value = await authService.getCurrentUser()
      console.log('✅ 用户信息刷新成功')
    } catch (error) {
      console.error('❌ 用户信息刷新失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    initialized.value = false
    authService.reset()
    console.log('🚪 用户已登出')
  }

  // 监听角色变化事件
  if (typeof window !== 'undefined') {
    window.addEventListener('auth-role-changed', async (event: any) => {
      const { role, user: newUser } = event.detail
      user.value = newUser
      console.log(`🔄 监听到角色变化: ${role}`)
    })
  }

  return {
    // 状态
    user,
    loading,
    initialized,
    
    // 计算属性
    isAdmin,
    isLoggedIn,
    currentRole,
    userPermissions,
    dataScope,
    currentOrganization,
    
    // 方法
    initAuth,
    switchRole,
    hasPermission,
    getAccessibleOrganizationIds,
    refreshUserInfo,
    logout
  }
})
