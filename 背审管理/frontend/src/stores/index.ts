// 状态管理基础配置
import { defineStore } from 'pinia'
import { ref } from 'vue'

// 示例 store
export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const userInfo = ref(null)
  
  // 方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }
  
  const setUserInfo = (info: any) => {
    userInfo.value = info
  }
  
  const clearUserInfo = () => {
    userInfo.value = null
  }
  
  return {
    loading,
    userInfo,
    setLoading,
    setUserInfo,
    clearUserInfo
  }
})

export default {
  useAppStore
}