// 搜索表单字段选项接口
export interface FieldOption {
  label: string
  value: any
}

// 搜索表单字段配置接口
export interface FieldConfig {
  key: string
  label: string
  type: 'input' | 'select' | 'multiSelect' | 'date' | 'daterange' | 'number' | 'switch' | 'radio' | 'checkbox'
  placeholder?: string
  span?: number
  clearable?: boolean
  disabled?: boolean
  hidden?: boolean
  options?: FieldOption[]
  // 日期相关
  format?: string
  valueFormat?: string
  rangeSeparator?: string
  startPlaceholder?: string
  endPlaceholder?: string
  shortcuts?: any[]
  // 数字相关
  min?: number
  max?: number
  step?: number
  // 开关相关
  activeText?: string
  inactiveText?: string
  // 条件显示
  showWhen?: {
    field: string
    value: any
  }
  // 字段变化回调
  onChange?: (value: any, formData: any) => void
}

// 搜索表单配置接口
export interface SearchFormConfig {
  title?: string
  labelWidth?: string
  gutter?: number
  defaultSpan?: number
  showSearchButton?: boolean
  showResetButton?: boolean
  fields: FieldConfig[][]
}

// 搜索表单数据接口
export interface SearchFormData {
  [key: string]: any
}