// 权限管理相关类型定义

/**
 * 用户信息接口
 */
export interface UserInfo {
  userId: string
  username: string
  name: string
  phone?: string
  email?: string
  organizationId: string
  organizationName: string
  roles: string[] // ['admin'] 或 ['unit']
  permissions: string[]
}

/**
 * 用户角色类型
 */
export type UserRole = 'admin' | 'unit'

/**
 * 数据访问范围
 */
export type DataScope = 'all' | 'organization'

/**
 * API响应格式
 */
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

/**
 * 权限检查结果
 */
export interface PermissionCheck {
  hasPermission: boolean
  reason?: string
}
