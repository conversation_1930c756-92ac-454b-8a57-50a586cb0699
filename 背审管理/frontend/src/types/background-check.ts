// 背景审查相关类型定义

// 统计相关接口
export interface StatisticsQuery {
  startDate?: string;
  endDate?: string;
  region?: string;
  personnelType?: number;
}

export interface StatisticsOverview {
  totalPersonnel: number;
  normalPersonnel: number;
  riskPersonnel: number;
  blacklistPersonnel: number;
}

export interface PersonnelTypeDistribution {
  type: number;
  name: string;
  count: number;
}

export interface RiskLevelDistribution {
  level: number;
  name: string;
  count: number;
}

export interface RegionDistribution {
  region: string;
  count: number;
}

export interface TrendData {
  date: string;
  count: number;
}

export interface OrganizationStatistics {
  organization: string;
  totalCount: number;
  normalCount: number;
  riskCount: number;
  blacklistCount: number;
  riskRate: number;
}

// 报表相关接口
export interface ReportGenerateData {
  reportType: string;
  format: string;
  startDate: string;
  endDate: string;
  reportName: string;
  region?: string;
  personnelType?: string;
  riskLevel?: string;
  remarks?: string;
}

export interface ReportInfo {
  id: number;
  reportName: string;
  reportType: string;
  format: string;
  generateTime: string;
  status: number;
  fileSize?: number;
  downloadUrl?: string;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReportQuery {
  reportName?: string;
  reportType?: string;
  status?: number;
  page: number;
  size: number;
}

export interface ReportPreview {
  id: number;
  reportName: string;
  content: string;
  previewUrl: string;
}