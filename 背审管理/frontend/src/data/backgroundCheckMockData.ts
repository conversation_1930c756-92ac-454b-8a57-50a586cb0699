// 背审任务管理Mock数据

export interface BackgroundCheckTask {
  id: string
  taskNo: string
  title: string
  description?: string
  type: 'background_check'
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // 人员信息
  personnelCount: number
  personnelIds: number[]
  completedCount: number
  
  // 任务分配
  assignmentType: 'department' | 'individual' // 分配类型
  assignedToOrg?: string
  assignedToOrgName?: string
  assignedToUser?: string
  assignedToUserName?: string
  
  // 创建信息
  createdBy: string
  createdByName: string
  createdByOrg: string
  
  // 时间管理
  createdAt: string
  assignedAt?: string
  dueDate: string
  completedAt?: string
  
  // 进度统计
  progress: {
    total: number
    completed: number
    pending: number
    percentage: number
  }
  
  // 逾期状态
  isOverdue: boolean
  overdueBy?: number // 逾期天数

  // 催办信息
  reminderCount?: number // 催办次数
}

export interface TaskPersonnelRelation {
  id: string
  taskId: string
  personnelId: number
  status: 'pending' | 'processing' | 'completed'
  result?: 'normal' | 'abnormal' | 'risk'
  resultDetails?: string
  processedBy?: string
  processedByName?: string
  processedAt?: string
  createdAt: string
  updatedAt: string
}

export interface BackgroundCheckRecord {
  id: string
  personnelId: number
  taskId: string
  checkType: 'initial' | 'periodic' | 'special'
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  result?: 'normal' | 'abnormal' | 'risk'
  resultDetails?: string
  
  // 时间记录
  startedAt: string
  completedAt?: string
  
  // 处理信息
  processedBy?: string
  processedByName?: string
  processedByOrg?: string
  
  // 审核信息
  reviewedBy?: string
  reviewedByName?: string
  reviewedAt?: string
  reviewComments?: string
  
  createdAt: string
  updatedAt: string
}

export interface ReminderRecord {
  id: string
  taskId: string
  reminderType: 'overdue' | 'deadline_approaching' | 'manual'
  content: string
  sentToOrg: string
  sentToUser?: string
  sentBy: string
  sentByName: string
  sentAt: string
  readAt?: string
}

// 生成任务编号
export function generateTaskNo(): string {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  const sequence = Math.floor(Math.random() * 9999) + 1
  return `BG${date}${sequence.toString().padStart(4, '0')}`
}

// 计算是否逾期
export function calculateOverdue(dueDate: string): { isOverdue: boolean; overdueBy?: number } {
  const due = new Date(dueDate)
  const now = new Date()
  const diffTime = now.getTime() - due.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return {
    isOverdue: diffDays > 0,
    overdueBy: diffDays > 0 ? diffDays : undefined
  }
}

// 背审任务Mock数据
export const backgroundCheckTasksMockData: BackgroundCheckTask[] = [
  {
    id: 'task-001',
    taskNo: 'BG20240115001',
    title: '新员工入职背审',
    description: '2024年1月新入职人员背景审查',
    type: 'background_check',
    status: 'in_progress',
    priority: 'medium',
    personnelCount: 15,
    personnelIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
    completedCount: 9,
    assignmentType: 'department',
    assignedToOrg: 'org-001',
    assignedToOrgName: '莲池分局',
    createdBy: 'admin-001',
    createdByName: '张管理员',
    createdByOrg: 'org-admin',
    createdAt: '2024-01-15 09:00:00',
    assignedAt: '2024-01-15 09:30:00',
    dueDate: '2024-02-01 18:00:00',
    progress: {
      total: 15,
      completed: 9,
      pending: 6,
      percentage: 60
    },
    isOverdue: false,
    reminderCount: 2
  },
  {
    id: 'task-002',
    taskNo: 'BG20240110002',
    title: '定期背审任务',
    description: '2024年第一季度定期背景审查',
    type: 'background_check',
    status: 'completed',
    priority: 'low',
    personnelCount: 8,
    personnelIds: [16, 17, 18, 19, 20, 21, 22, 23],
    completedCount: 8,
    assignmentType: 'individual',
    assignedToOrg: 'org-002',
    assignedToOrgName: '竞秀分局',
    assignedToUser: 'user-001',
    assignedToUserName: '李审查员',
    createdBy: 'admin-001',
    createdByName: '张管理员',
    createdByOrg: 'org-admin',
    createdAt: '2024-01-10 14:00:00',
    assignedAt: '2024-01-10 14:30:00',
    dueDate: '2024-01-25 18:00:00',
    completedAt: '2024-01-24 16:30:00',
    progress: {
      total: 8,
      completed: 8,
      pending: 0,
      percentage: 100
    },
    isOverdue: false,
    reminderCount: 0
  },
  {
    id: 'task-003',
    taskNo: 'BG20240105003',
    title: '高风险人员专项背审',
    description: '针对高风险等级人员的专项背景审查',
    type: 'background_check',
    status: 'in_progress',
    priority: 'high',
    personnelCount: 3,
    personnelIds: [24, 25, 26],
    completedCount: 1,
    assignmentType: 'department',
    assignedToOrg: 'org-003',
    assignedToOrgName: '满城分局',
    createdBy: 'admin-001',
    createdByName: '张管理员',
    createdByOrg: 'org-admin',
    createdAt: '2024-01-05 10:00:00',
    assignedAt: '2024-01-05 10:30:00',
    dueDate: '2024-01-20 18:00:00',
    progress: {
      total: 3,
      completed: 1,
      pending: 2,
      percentage: 33
    },
    reminderCount: 3,
    ...calculateOverdue('2024-01-20 18:00:00')
  }
]

// 任务人员关联Mock数据
export const taskPersonnelRelationsMockData: TaskPersonnelRelation[] = [
  // task-001 的人员关联
  {
    id: 'rel-001',
    taskId: 'task-001',
    personnelId: 1,
    status: 'completed',
    result: 'normal',
    resultDetails: '背景审查正常，无异常情况',
    processedBy: 'user-001',
    processedByName: '李审查员',
    processedAt: '2024-01-16 10:30:00',
    createdAt: '2024-01-15 09:30:00',
    updatedAt: '2024-01-16 10:30:00'
  },
  {
    id: 'rel-002',
    taskId: 'task-001',
    personnelId: 2,
    status: 'completed',
    result: 'abnormal',
    resultDetails: '发现交通违法记录',
    processedBy: 'user-001',
    processedByName: '李审查员',
    processedAt: '2024-01-17 14:20:00',
    createdAt: '2024-01-15 09:30:00',
    updatedAt: '2024-01-17 14:20:00'
  },
  {
    id: 'rel-003',
    taskId: 'task-001',
    personnelId: 3,
    status: 'processing',
    processedBy: 'user-002',
    processedByName: '王审查员',
    createdAt: '2024-01-15 09:30:00',
    updatedAt: '2024-01-18 09:00:00'
  }
]

// 背审记录Mock数据
export const backgroundCheckRecordsMockData: BackgroundCheckRecord[] = [
  {
    id: 'record-001',
    personnelId: 1,
    taskId: 'task-001',
    checkType: 'initial',
    status: 'completed',
    result: 'normal',
    resultDetails: '背景审查正常，无异常情况',
    startedAt: '2024-01-15 09:30:00',
    completedAt: '2024-01-16 10:30:00',
    processedBy: 'user-001',
    processedByName: '李审查员',
    processedByOrg: 'org-001',
    reviewedBy: 'admin-001',
    reviewedByName: '张管理员',
    reviewedAt: '2024-01-16 11:00:00',
    reviewComments: '审核通过',
    createdAt: '2024-01-15 09:30:00',
    updatedAt: '2024-01-16 11:00:00'
  }
]

// 催办记录Mock数据
export const reminderRecordsMockData: ReminderRecord[] = [
  {
    id: 'reminder-001',
    taskId: 'task-003',
    reminderType: 'overdue',
    content: '任务已逾期，请尽快处理',
    sentToOrg: 'org-003',
    sentBy: 'admin-001',
    sentByName: '张管理员',
    sentAt: '2024-01-21 09:00:00'
  }
]
