// 背审结果处理模块Mock数据

// 异常人员接口
export interface AbnormalPersonnel {
  id: string
  // 基本信息
  name: string
  idCard: string
  phone: string
  organization: string
  avatar?: string
  
  // 异常信息
  abnormalType: 'criminal_record' | 'credit_issue' | 'education_fraud' | 'work_experience' | 'identity_issue' | 'reference_problem'
  abnormalLevel: 'critical' | 'high' | 'medium' | 'low'
  abnormalDescription: string
  discoveredAt: string
  
  // 背审关联
  backgroundCheckTaskId: string
  backgroundCheckTaskNo: string
  backgroundCheckResult: string
  
  // 处理状态
  processingStatus: 'pending' | 'assigned' | 'processing' | 'completed' | 'rejected'
  assignedTo?: string
  assignedToName?: string
  assignedAt?: string
  processedAt?: string
  
  // 处理结果
  processingResult?: string
  processingNote?: string
  processingTaskId?: string
}

// 处理任务接口
export interface ProcessingTask {
  id: string
  taskNo: string
  title: string
  description?: string
  
  // 任务状态
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // 人员关联
  personnelCount: number
  personnelIds: string[]
  completedCount: number
  
  // 任务分配
  assignmentType: 'department' | 'individual'
  assignedToOrg?: string
  assignedToOrgName?: string
  assignedToUser?: string
  assignedToUserName?: string
  
  // 创建信息
  createdBy: string
  createdByName: string
  createdByOrg: string
  
  // 时间管理
  createdAt: string
  assignedAt?: string
  dueDate: string
  completedAt?: string
  
  // 进度统计
  progress: {
    total: number
    completed: number
    pending: number
    percentage: number
  }
  
  // 催办信息
  reminderCount?: number
  lastReminderAt?: string
  
  // 逾期状态
  isOverdue: boolean
  overdueBy?: number
  
  // 处理要求
  processingRequirement?: string
  processingDeadline?: string
}

// 处理历史记录接口
export interface ProcessingHistory {
  id: string
  taskId: string
  personnelId?: string
  operationType: 'create' | 'assign' | 'process' | 'complete' | 'cancel' | 'remind'
  operatorId: string
  operatorName: string
  operatedAt: string
  content: string
  note?: string
}

// 催办记录接口
export interface ReminderRecord {
  id: string
  taskId: string
  reminderTime: string
  reminderUser: string
  reminderUserId: string
  isRead: boolean
  readTime?: string
  feedback?: string
  feedbackTime?: string
}

// 计算逾期状态的工具函数
export const calculateOverdue = (dueDate: string) => {
  const now = new Date()
  const due = new Date(dueDate)
  const diffTime = now.getTime() - due.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return {
    isOverdue: diffDays > 0,
    overdueBy: diffDays > 0 ? diffDays : undefined
  }
}

// 生成任务编号
export const generateTaskNo = (prefix: string = 'PT') => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `${prefix}${year}${month}${day}${random}`
}

// 异常人员Mock数据
export const abnormalPersonnelMockData: AbnormalPersonnel[] = [
  {
    id: 'abnormal-001',
    name: '张三',
    idCard: '110101199001011234',
    phone: '***********',
    organization: '技术部',
    avatar: '/avatars/zhang-san.jpg',
    abnormalType: 'criminal_record',
    abnormalLevel: 'critical',
    abnormalDescription: '存在刑事犯罪记录，涉嫌诈骗罪',
    discoveredAt: '2024-01-15 14:30:00',
    backgroundCheckTaskId: 'task-001',
    backgroundCheckTaskNo: 'BG20240115001',
    backgroundCheckResult: '背审不通过',
    processingStatus: 'pending',
    processingTaskId: 'pt-001'
  },
  {
    id: 'abnormal-002',
    name: '李四',
    idCard: '110101199002022345',
    phone: '***********',
    organization: '市场部',
    avatar: '/avatars/li-si.jpg',
    abnormalType: 'credit_issue',
    abnormalLevel: 'high',
    abnormalDescription: '个人征信存在多次逾期记录，信用等级较低',
    discoveredAt: '2024-01-16 09:15:00',
    backgroundCheckTaskId: 'task-001',
    backgroundCheckTaskNo: 'BG20240115001',
    backgroundCheckResult: '背审有异常',
    processingStatus: 'processing',
    assignedTo: 'user-001',
    assignedToName: '王处理员',
    assignedAt: '2024-01-16 10:00:00',
    processingTaskId: 'pt-001'
  },
  {
    id: 'abnormal-003',
    name: '王五',
    idCard: '110101199003033456',
    phone: '***********',
    organization: '财务部',
    avatar: '/avatars/wang-wu.jpg',
    abnormalType: 'education_fraud',
    abnormalLevel: 'medium',
    abnormalDescription: '学历证书无法通过学信网验证，疑似造假',
    discoveredAt: '2024-01-17 16:45:00',
    backgroundCheckTaskId: 'task-002',
    backgroundCheckTaskNo: 'BG20240110002',
    backgroundCheckResult: '背审有异常',
    processingStatus: 'completed',
    assignedTo: 'user-002',
    assignedToName: '赵处理员',
    assignedAt: '2024-01-17 17:00:00',
    processedAt: '2024-01-20 15:30:00',
    processingResult: '已核实，学历确实存在问题，建议降级使用',
    processingNote: '经过进一步调查，该员工实际学历为大专，但提供了虚假的本科证书',
    processingTaskId: 'pt-002'
  },
  {
    id: 'abnormal-004',
    name: '赵六',
    idCard: '110101199004044567',
    phone: '***********',
    organization: '人事部',
    avatar: '/avatars/zhao-liu.jpg',
    abnormalType: 'work_experience',
    abnormalLevel: 'low',
    abnormalDescription: '工作经历存在时间重叠，部分经历无法核实',
    discoveredAt: '2024-01-18 11:20:00',
    backgroundCheckTaskId: 'task-003',
    backgroundCheckTaskNo: 'BG20240118001',
    backgroundCheckResult: '背审有异常',
    processingStatus: 'assigned',
    assignedTo: 'user-003',
    assignedToName: '孙处理员',
    assignedAt: '2024-01-18 14:00:00',
    processingTaskId: 'pt-003'
  },
  {
    id: 'abnormal-005',
    name: '钱七',
    idCard: '110101199005055678',
    phone: '***********',
    organization: '技术部',
    avatar: '/avatars/qian-qi.jpg',
    abnormalType: 'identity_issue',
    abnormalLevel: 'high',
    abnormalDescription: '身份证信息与公安系统记录不符',
    discoveredAt: '2024-01-19 15:30:00',
    backgroundCheckTaskId: 'task-003',
    backgroundCheckTaskNo: 'BG20240118001',
    backgroundCheckResult: '背审不通过',
    processingStatus: 'pending'
  }
]

// 处理任务Mock数据
export const processingTaskMockData: ProcessingTask[] = [
  {
    id: 'pt-001',
    taskNo: 'PT20240115001',
    title: '新员工异常处理',
    description: '处理新入职员工背审中发现的异常情况',
    status: 'in_progress',
    priority: 'high',
    personnelCount: 2,
    personnelIds: ['abnormal-001', 'abnormal-002'],
    completedCount: 0,
    assignmentType: 'individual',
    assignedToUser: 'user-001',
    assignedToUserName: '王处理员',
    createdBy: 'admin-001',
    createdByName: '张管理员',
    createdByOrg: 'org-admin',
    createdAt: '2024-01-16 09:00:00',
    assignedAt: '2024-01-16 09:30:00',
    dueDate: '2024-01-26 18:00:00',
    progress: {
      total: 2,
      completed: 0,
      pending: 2,
      percentage: 0
    },
    reminderCount: 1,
    lastReminderAt: '2024-01-20 14:30:00',
    isOverdue: false,
    processingRequirement: '需要详细调查异常情况，提供处理建议',
    processingDeadline: '2024-01-26 18:00:00'
  },
  {
    id: 'pt-002',
    taskNo: 'PT20240117001',
    title: '学历异常专项处理',
    description: '处理学历验证异常的人员',
    status: 'completed',
    priority: 'medium',
    personnelCount: 1,
    personnelIds: ['abnormal-003'],
    completedCount: 1,
    assignmentType: 'individual',
    assignedToUser: 'user-002',
    assignedToUserName: '赵处理员',
    createdBy: 'admin-001',
    createdByName: '张管理员',
    createdByOrg: 'org-admin',
    createdAt: '2024-01-17 17:00:00',
    assignedAt: '2024-01-17 17:15:00',
    dueDate: '2024-01-24 18:00:00',
    completedAt: '2024-01-20 15:30:00',
    progress: {
      total: 1,
      completed: 1,
      pending: 0,
      percentage: 100
    },
    reminderCount: 0,
    isOverdue: false,
    processingRequirement: '核实学历真实性，提供处理意见',
    processingDeadline: '2024-01-24 18:00:00'
  },
  {
    id: 'pt-003',
    taskNo: 'PT20240118001',
    title: '工作经历核实任务',
    description: '核实工作经历异常人员的真实情况',
    status: 'assigned',
    priority: 'low',
    personnelCount: 2,
    personnelIds: ['abnormal-004', 'abnormal-005'],
    completedCount: 0,
    assignmentType: 'individual',
    assignedToUser: 'user-003',
    assignedToUserName: '孙处理员',
    createdBy: 'admin-001',
    createdByName: '张管理员',
    createdByOrg: 'org-admin',
    createdAt: '2024-01-18 14:00:00',
    assignedAt: '2024-01-18 14:15:00',
    dueDate: '2024-01-28 18:00:00',
    progress: {
      total: 2,
      completed: 0,
      pending: 2,
      percentage: 0
    },
    reminderCount: 0,
    isOverdue: false,
    processingRequirement: '详细核实工作经历和身份信息的真实性',
    processingDeadline: '2024-01-28 18:00:00'
  }
]

// 处理历史Mock数据
export const processingHistoryMockData: ProcessingHistory[] = [
  {
    id: 'history-001',
    taskId: 'pt-001',
    operationType: 'create',
    operatorId: 'admin-001',
    operatorName: '张管理员',
    operatedAt: '2024-01-16 09:00:00',
    content: '创建处理任务：新员工异常处理'
  },
  {
    id: 'history-002',
    taskId: 'pt-001',
    operationType: 'assign',
    operatorId: 'admin-001',
    operatorName: '张管理员',
    operatedAt: '2024-01-16 09:30:00',
    content: '任务已分配给王处理员'
  },
  {
    id: 'history-003',
    taskId: 'pt-001',
    operationType: 'remind',
    operatorId: 'admin-001',
    operatorName: '张管理员',
    operatedAt: '2024-01-20 14:30:00',
    content: '催办任务处理进度'
  }
]

// 催办记录Mock数据
export const reminderRecordMockData: ReminderRecord[] = [
  {
    id: 'reminder-001',
    taskId: 'pt-001',
    reminderTime: '2024-01-20 14:30:00',
    reminderUser: '张管理员',
    reminderUserId: 'admin-001',
    isRead: true,
    readTime: '2024-01-20 15:00:00',
    feedback: '已收到催办通知，正在加快处理进度',
    feedbackTime: '2024-01-20 15:30:00'
  }
]
