// 人员管理Mock数据
// 所有数据来自河北省保定市

export interface PersonnelData {
  id: number
  name: string
  idCard: string
  phone: string
  email: string
  department: string
  position: string
  personnelType: number // 1-专职保卫 2-保安人员
  backgroundCheckResult: number // 0-未审查 1-正常 2-异常
  abnormalTypes?: string[] // 异常类型数组，当backgroundCheckResult为2时使用
  processingStatus?: number // 0-未处理 1-无需处理 2-重点关注 3-调岗/劝退
  status: number // 1-在职 2-离职
  entryDate: string
  region: string // 区域
  industry?: string // 所属行业
  createTime: string
  updateTime: string
  organization?: string // 所属单位
}

// 河北省保定市人员Mock数据
export const personnelMockData: PersonnelData[] = [
  {
    id: 1,
    name: '张建国',
    idCard: '130602198501151234',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市公安局',
    position: '安保队长',
    personnelType: 1,
    backgroundCheckResult: 1,
    processingStatus: 1,
    status: 1,
    entryDate: '2020-03-15',
    region: '保定市竞秀区',
    industry: 'government',
    organization: '保定市公安局',
    createTime: '2020-03-15 09:00:00',
    updateTime: '2023-12-01 10:30:00'
  },
  {
    id: 2,
    name: '李明华',
    idCard: '130603199002201567',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市政府',
    position: '安全员',
    personnelType: 2,
    backgroundCheckResult: 2,
    abnormalTypes: ['criminal_record', 'credit_issues'],
    processingStatus: 2, // 重点关注
    status: 1,
    entryDate: '2021-06-20',
    region: '保定市莲池区',
    industry: 'government',
    organization: '保定市政府',
    createTime: '2021-06-20 14:00:00',
    updateTime: '2023-11-28 16:45:00'
  },
  {
    id: 3,
    name: '王秀芳',
    idCard: '130604198803121890',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市教育局',
    position: '校园安保',
    personnelType: 1,
    backgroundCheckResult: 1,
    status: 1,
    entryDate: '2019-09-01',
    region: '保定市满城区',
    organization: '保定市教育局',
    createTime: '2019-09-01 08:30:00',
    updateTime: '2023-12-01 09:15:00'
  },
  {
    id: 4,
    name: '刘德华',
    idCard: '130605197512051234',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市交通局',
    position: '交通安全员',
    personnelType: 2,
    backgroundCheckResult: 2,
    abnormalTypes: ['drug_use'],
    processingStatus: 2, // 黑名单
    status: 1,
    entryDate: '2022-01-10',
    region: '保定市清苑区',
    organization: '保定市交通局',
    createTime: '2022-01-10 10:00:00',
    updateTime: '2023-11-30 14:20:00'
  },
  {
    id: 5,
    name: '陈志强',
    idCard: '130606198907081567',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市卫健委',
    position: '医院安保',
    personnelType: 1,
    backgroundCheckResult: 1,
    status: 1,
    entryDate: '2020-11-15',
    region: '保定市徐水区',
    organization: '保定市卫健委',
    createTime: '2020-11-15 11:30:00',
    updateTime: '2023-12-01 08:45:00'
  },
  {
    id: 6,
    name: '赵丽娟',
    idCard: '130607199204151890',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市文旅局',
    position: '景区安保',
    personnelType: 2,
    backgroundCheckResult: 1,
    status: 1,
    entryDate: '2021-04-01',
    region: '保定市涞水县',
    organization: '保定市文旅局',
    createTime: '2021-04-01 09:15:00',
    updateTime: '2023-11-29 15:30:00'
  },
  {
    id: 7,
    name: '孙国庆',
    idCard: '130608198106101234',
    phone: '13803128007',
    email: '<EMAIL>',
    department: '保定市住建局',
    position: '工地安全员',
    personnelType: 2,
    backgroundCheckResult: 3,
    status: 2,
    entryDate: '2018-05-20',
    region: '保定市涞源县',
    organization: '保定市住建局',
    createTime: '2018-05-20 13:45:00',
    updateTime: '2023-10-15 16:00:00'
  },
  {
    id: 8,
    name: '周美玲',
    idCard: '130609199508251567',
    phone: '13803128008',
    email: '<EMAIL>',
    department: '保定市商务局',
    position: '商场安保',
    personnelType: 1,
    backgroundCheckResult: 1,
    status: 1,
    entryDate: '2022-08-01',
    region: '保定市易县',
    organization: '保定市商务局',
    createTime: '2022-08-01 10:20:00',
    updateTime: '2023-12-01 11:10:00'
  },
  {
    id: 9,
    name: '吴建军',
    idCard: '130610197903121890',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市应急局',
    position: '应急安全员',
    personnelType: 1,
    backgroundCheckResult: 2,
    abnormalTypes: ['political_issues'],
    processingStatus: 0, // 未处理
    status: 1,
    entryDate: '2019-12-01',
    region: '保定市定兴县',
    organization: '保定市应急局',
    createTime: '2019-12-01 14:30:00',
    updateTime: '2023-11-28 09:25:00'
  },
  {
    id: 10,
    name: '郑小红',
    idCard: '130611199101051234',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市民政局',
    position: '社区安保',
    personnelType: 2,
    backgroundCheckResult: 1,
    status: 1,
    entryDate: '2021-10-15',
    region: '保定市顺平县',
    organization: '保定市民政局',
    createTime: '2021-10-15 08:45:00',
    updateTime: '2023-12-01 13:20:00'
  },
  {
    id: 11,
    name: '马志远',
    idCard: '130612198712201567',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市环保局',
    position: '环保安全员',
    personnelType: 2,
    backgroundCheckResult: 2,
    abnormalTypes: ['credit_issues', 'violence'],
    processingStatus: 0, // 未处理
    status: 1,
    entryDate: '2020-07-01',
    region: '保定市唐县',
    organization: '保定市环保局',
    createTime: '2020-07-01 15:00:00',
    updateTime: '2023-11-30 10:40:00'
  },
  {
    id: 12,
    name: '冯丽华',
    idCard: '130613199406081890',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市财政局',
    position: '办公安保',
    personnelType: 1,
    backgroundCheckResult: 1,
    status: 1,
    entryDate: '2022-03-01',
    region: '保定市望都县',
    organization: '保定市财政局',
    createTime: '2022-03-01 09:30:00',
    updateTime: '2023-12-01 14:15:00'
  },
  {
    id: 13,
    name: '高建设',
    idCard: '130614198209151234',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市水利局',
    position: '水库安全员',
    personnelType: 1,
    backgroundCheckResult: 3,
    status: 3,
    entryDate: '2017-04-15',
    region: '保定市安新县',
    organization: '保定市水利局',
    createTime: '2017-04-15 11:20:00',
    updateTime: '2023-09-20 16:30:00'
  },
  {
    id: 14,
    name: '许美娟',
    idCard: '130615199011101567',
    phone: '13803128014',
    email: '<EMAIL>',
    department: '保定市农业局',
    position: '农场安保',
    personnelType: 2,
    backgroundCheckResult: 1,
    status: 1,
    entryDate: '2021-05-01',
    region: '保定市雄县',
    organization: '保定市农业局',
    createTime: '2021-05-01 12:00:00',
    updateTime: '2023-11-29 08:50:00'
  },
  {
    id: 15,
    name: '田国强',
    idCard: '130616198505251890',
    phone: '***********',
    email: '<EMAIL>',
    department: '保定市科技局',
    position: '实验室安全员',
    personnelType: 1,
    backgroundCheckResult: 2,
    abnormalTypes: ['mental_illness', 'violence'],
    processingStatus: 0, // 未处理
    status: 1,
    entryDate: '2019-08-01',
    region: '保定市容城县',
    organization: '保定市科技局',
    createTime: '2019-08-01 10:15:00',
    updateTime: '2023-12-01 15:45:00'
  }
]

// 保定市区域列表
export const baodingRegions = [
  '保定市竞秀区',
  '保定市莲池区',
  '保定市满城区',
  '保定市清苑区',
  '保定市徐水区',
  '保定市涞水县',
  '保定市涞源县',
  '保定市易县',
  '保定市定兴县',
  '保定市顺平县',
  '保定市唐县',
  '保定市望都县',
  '保定市安新县',
  '保定市雄县',
  '保定市容城县',
  '保定市高阳县',
  '保定市博野县',
  '保定市蠡县',
  '保定市曲阳县',
  '保定市阜平县',
  '保定市涿州市',
  '保定市定州市',
  '保定市安国市',
  '保定市高碑店市'
]

// 保定市政府部门列表
export const baodingDepartments = [
  '保定市公安局',
  '保定市政府',
  '保定市教育局',
  '保定市交通局',
  '保定市卫健委',
  '保定市文旅局',
  '保定市住建局',
  '保定市商务局',
  '保定市应急局',
  '保定市民政局',
  '保定市环保局',
  '保定市财政局',
  '保定市水利局',
  '保定市农业局',
  '保定市科技局',
  '保定市工信局',
  '保定市人社局',
  '保定市自然资源局',
  '保定市市场监管局',
  '保定市统计局'
]

// 职位类型
export const positionTypes = [
  '安保队长',
  '安全员',
  '校园安保',
  '交通安全员',
  '医院安保',
  '景区安保',
  '工地安全员',
  '商场安保',
  '应急安全员',
  '社区安保',
  '环保安全员',
  '办公安保',
  '水库安全员',
  '农场安保',
  '实验室安全员',
  '门卫',
  '巡逻员',
  '监控员',
  '消防员',
  '保安班长'
]

// 获取人员类型文本
export const getPersonnelTypeText = (type: number): string => {
  const typeMap: Record<number, string> = {
    1: '专职保卫',
    2: '保安人员'
  }
  return typeMap[type] || '未分类'
}

// 行业类型定义
export const industryTypes = [
  { value: 'government', label: '党政机关' },
  { value: 'education', label: '中小幼' },
  { value: 'healthcare', label: '医疗机构' },
  { value: 'finance', label: '金融单位' },
  { value: 'enterprise', label: '重点企业' },
  { value: 'cultural', label: '文博单位' },
  { value: 'logistics', label: '寄递物流' }
]

// 获取行业类型文本
export const getIndustryTypeText = (industry: string): string => {
  const found = industryTypes.find(item => item.value === industry)
  return found ? found.label : industry || '未分类'
}

// 获取在职状态文本
export const getEmploymentStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    1: '在职',
    2: '离职'
  }
  return statusMap[status] || '未知'
}

// 获取在职状态标签类型
export const getEmploymentStatusType = (status: number): string => {
  const typeMap: Record<number, string> = {
    1: 'success',
    2: 'info'
  }
  return typeMap[status] || ''
}

// 背景审查异常类型定义
export const backgroundCheckAbnormalTypes = [
  { value: 'criminal_record', label: '重大刑事犯罪前科' },
  { value: 'drug_use', label: '吸毒记录' },
  { value: 'mental_illness', label: '精神疾病' },
  { value: 'credit_issues', label: '信用问题' },
  { value: 'political_issues', label: '政治问题' },
  { value: 'fraud', label: '诈骗记录' },
  { value: 'violence', label: '暴力倾向' },
  { value: 'theft', label: '盗窃记录' },
  { value: 'other', label: '其他异常' }
]

// 获取背景审查结果文本
export const getBackgroundCheckResultText = (result: number): string => {
  const resultMap: Record<number, string> = {
    0: '未审查',
    1: '正常',
    2: '异常'
  }
  return resultMap[result] || '未知'
}

// 获取背景审查结果标签类型
export const getBackgroundCheckResultType = (result: number): string => {
  const typeMap: Record<number, string> = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  }
  return typeMap[result] || ''
}

// 获取单个异常类型文本
export const getSingleAbnormalTypeText = (type: string): string => {
  if (!type) return ''
  const found = backgroundCheckAbnormalTypes.find(item => item.value === type)
  return found ? found.label : type
}

// 获取异常类型文本（数组）
export const getAbnormalTypeText = (types: string[]): string => {
  if (!types || types.length === 0) return ''
  return types.map(type => getSingleAbnormalTypeText(type)).join('、')
}

// 处理记录数据结构
export interface ProcessingRecord {
  id: number
  personnelId: number
  operatorId: number
  operatorName: string
  fromStatus: number // 原状态：0-无需处理 1-重点关注 2-黑名单
  toStatus: number   // 新状态：0-无需处理 1-重点关注 2-黑名单
  reason: string     // 操作原因
  operateTime: string // 操作时间
  createTime: string
}

// 通知记录数据结构
export interface NotificationRecord {
  id: number
  personnelId: number
  personnelName: string
  organizationId: number
  organizationName: string
  notificationType: number // 1-建议辞退 2-建议调岗 3-建议加强监管 4-其他
  notificationContent: string
  sendStatus: number // 1-待发送 2-已发送 3-发送失败
  sendTime?: string
  readStatus: number // 1-未读 2-已读
  readTime?: string
  response?: string // 接收方回复
  responseTime?: string
  createTime: string
  updateTime: string
}

// 获取处理状态文本
export const getProcessingStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '未处理',
    1: '无需处理',
    2: '重点关注',
    3: '调岗/劝退'
  }
  return statusMap[status] || '未处理'
}

// 获取处理状态标签类型
export const getProcessingStatusType = (status: number): string => {
  const typeMap: Record<number, string> = {
    0: 'warning',
    1: 'success',
    2: 'primary',
    3: 'danger'
  }
  return typeMap[status] || ''
}

// 获取通知类型文本
export const getNotificationTypeText = (type: number): string => {
  const typeMap: Record<number, string> = {
    1: '建议辞退',
    2: '建议调岗',
    3: '建议加强监管',
    4: '其他'
  }
  return typeMap[type] || '未知'
}

// 获取状态文本
export const getStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    1: '正常',
    2: '离职',
    3: '黑名单'
  }
  return statusMap[status] || '未知'
}

// 生成随机人员数据
export const generateRandomPersonnel = (count: number = 1): PersonnelData[] => {
  const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
  const givenNames = ['建国', '明华', '秀芳', '德华', '志强', '丽娟', '国庆', '美玲', '建军', '小红', '志远', '丽华', '建设', '美娟', '国强']
  
  const result: PersonnelData[] = []
  
  for (let i = 0; i < count; i++) {
    const id = Date.now() + Math.random() * 1000
    const surname = surnames[Math.floor(Math.random() * surnames.length)]
    const givenName = givenNames[Math.floor(Math.random() * givenNames.length)]
    const name = surname + givenName
    
    // 生成保定市身份证号（1306开头）
    const areaCode = '1306' + String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')
    const birthYear = 1980 + Math.floor(Math.random() * 25)
    const birthMonth = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')
    const birthDay = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')
    const sequence = String(Math.floor(Math.random() * 999) + 1).padStart(3, '0')
    const checkDigit = Math.floor(Math.random() * 10)
    const idCard = areaCode + birthYear + birthMonth + birthDay + sequence + checkDigit
    
    const phone = '1380312' + String(Math.floor(Math.random() * 9000) + 1000)
    const email = `${name.toLowerCase()}@baoding.com`
    const department = baodingDepartments[Math.floor(Math.random() * baodingDepartments.length)]
    const position = positionTypes[Math.floor(Math.random() * positionTypes.length)]
    const region = baodingRegions[Math.floor(Math.random() * baodingRegions.length)]
    
    result.push({
      id: Math.floor(id),
      name,
      idCard,
      phone,
      email,
      department,
      position,
      personnelType: Math.floor(Math.random() * 2) + 1,
      backgroundCheckResult: Math.floor(Math.random() * 3),
      abnormalTypes: Math.random() > 0.7 ? [backgroundCheckAbnormalTypes[Math.floor(Math.random() * backgroundCheckAbnormalTypes.length)].value] : undefined,
      processingStatus: Math.floor(Math.random() * 4), // 0-未处理 1-无需处理 2-重点关注 3-调岗/劝退
      status: Math.floor(Math.random() * 2) + 1, // 1-在职 2-离职
      entryDate: `${2018 + Math.floor(Math.random() * 6)}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      region,
      industry: industryTypes[Math.floor(Math.random() * industryTypes.length)].value,
      organization: department,
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    })
  }
  
  return result
}