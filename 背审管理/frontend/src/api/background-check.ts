import { mockApi } from '@/utils/mockData'

// 数据管理相关接口
export interface PersonnelInfo {
  id: number
  name: string
  phone: string
  idCard: string
  ethnicity: string
  position: string
  securityCompany: string
  photo: string
  education: string
  region: string
  address: string
  politicalStatus: string
  organization: string
  personnelType: number // 1-有编制 2-无编制 3-外包
  backgroundCheckResult: string
  riskLevel: number // 1-低风险 2-中风险 3-高风险
  status: number
  entryDate: string // 入职日期
  createdAt: string
  updatedAt: string
}

export interface PersonnelQuery {
  name?: string
  idCard?: string
  phone?: string
  organization?: string
  region?: string
  personnelType?: number
  riskLevel?: number
  status?: number
  entryDateStart?: string // 入职开始日期
  entryDateEnd?: string // 入职结束日期
  focusDateStart?: string // 关注开始日期（用于关注人员页面）
  focusDateEnd?: string // 关注结束日期（用于关注人员页面）
  page: number
  size: number
}

export interface PersonnelUpdateData {
  id: number
  name?: string
  phone?: string
  position?: string
  organization?: string
  region?: string
  address?: string
  politicalStatus?: string
  backgroundCheckResult?: number
  abnormalTypes?: string[]
  riskLevel?: number
  status?: number
  entryDate?: string // 入职日期
}

// 获取人员列表
export const getPersonnelList = (params: PersonnelQuery) => {
  return mockApi.getPersonnelList(params)
}

// 获取人员详情
export const getPersonnelDetail = (id: number) => {
  return mockApi.getPersonnelDetail(id)
}

// 更新人员信息
export const updatePersonnel = (data: PersonnelUpdateData) => {
  return mockApi.updatePersonnel(data)
}

// 加入黑名单
export const addToBlacklist = (data: {
  personnelId: number
  blacklistType: number
  reason: string
  effectiveDate: string
  expiryDate?: string
}) => {
  return mockApi.addToBlacklist(data)
}

// 发送通知（旧版本，保留兼容性）
export const sendNotificationOld = (data: {
  personnelIds: number[]
  organizationId: number
  title: string
  content: string
  type: number
}) => {
  return mockApi.sendNotification(data)
}

// 黑名单管理相关接口
export interface BlacklistInfo {
  id: number
  personnelId: number
  personnelName: string
  idCard: string
  organization: string
  blacklistType: number // 1-违法犯罪 2-精神疾病 3-吸毒 4-其他
  reason: string
  operatorId: number
  operatorName: string
  effectiveDate: string
  expiryDate?: string
  status: number // 1-生效 2-失效 3-已解除
  createdAt: string
  updatedAt: string
}

export interface BlacklistQuery {
  personnelName?: string
  idCard?: string
  organization?: string
  blacklistType?: number
  status?: number
  effectiveDate?: string
  expiryDate?: string
  page: number
  size: number
}

export interface BlacklistCreateData {
  personnelId: number
  blacklistType: number
  reason: string
  effectiveDate: string
  expiryDate?: string
}

export interface BlacklistUpdateData {
  id: number
  blacklistType?: number
  reason?: string
  effectiveDate?: string
  expiryDate?: string
  status?: number
}

// 获取黑名单列表
export const getBlacklistList = (params: BlacklistQuery) => {
  return mockApi.getBlacklistList(params)
}

// 获取黑名单详情
export const getBlacklistDetail = (id: number) => {
  return mockApi.getBlacklistDetail(id)
}

// 创建黑名单记录
export const createBlacklist = (data: BlacklistCreateData) => {
  return mockApi.createBlacklist(data)
}

// 更新黑名单记录
export const updateBlacklist = (data: BlacklistUpdateData) => {
  return mockApi.updateBlacklist(data)
}

// 解除黑名单
export const removeFromBlacklist = (id: number, reason: string) => {
  return mockApi.removeFromBlacklist(id, reason)
}

// 批量解除黑名单
export const batchRemoveFromBlacklist = (ids: number[], reason: string) => {
  return mockApi.batchRemoveFromBlacklist(ids, reason)
}

// 统计分析相关接口
export interface StatisticsQuery {
  startDate: string
  endDate: string
  region?: string
  organizationType?: string
}

export interface StatisticsOverview {
  totalPersonnel: number
  totalBlacklist: number
  highRiskCount: number
  mediumRiskCount: number
  lowRiskCount: number
  newPersonnelThisMonth: number
  newBlacklistThisMonth: number
  riskTrend: number // 风险趋势百分比
}

export interface PersonnelTypeDistribution {
  type: string
  count: number
  percentage: number
}

export interface RiskLevelDistribution {
  level: string
  count: number
  percentage: number
}

export interface RegionDistribution {
  region: string
  totalCount: number
  riskCount: number
  riskRate: number
}

export interface PersonnelTrend {
  date: string
  newCount: number
  totalCount: number
  riskCount: number
}

export interface OrganizationAbnormal {
  organizationId: number
  organizationName: string
  totalPersonnel: number
  abnormalPersonnel: number
  abnormalRate: number
  riskLevel: number
}

// 获取统计概览
export const getStatisticsOverview = (params: StatisticsQuery) => {
  return mockApi.getStatisticsOverview(params)
}

// 获取人员类型分布
export const getPersonnelTypeDistribution = (params: StatisticsQuery) => {
  return mockApi.getPersonnelTypeDistribution(params)
}

// 获取风险等级分布
export const getRiskLevelDistribution = (params: StatisticsQuery) => {
  return mockApi.getRiskLevelDistribution(params)
}

// 获取区域分布
export const getRegionDistribution = (params: StatisticsQuery) => {
  return mockApi.getRegionDistribution(params)
}

// 获取人员增量趋势
export const getPersonnelTrend = (params: StatisticsQuery) => {
  return mockApi.getPersonnelTrend(params)
}

// 获取各单位异常人员统计
export const getOrganizationAbnormal = (params: StatisticsQuery) => {
  return mockApi.getOrganizationAbnormal(params)
}

// 报表管理相关接口
export interface ReportInfo {
  id: number
  reportName: string
  reportType: string
  format: string
  status: number // 1-待处理 2-处理中 3-已完成 4-失败
  progress: number
  fileSize?: number
  filePath?: string
  createdBy: string
  createdAt: string
  duration?: number
  errorMessage?: string
}

export interface ReportQuery {
  reportName?: string
  reportType?: string
  status?: number
  page: number
  size: number
}

export interface ReportGenerateData {
  reportType: string
  format: string
  reportName: string
  startDate: string
  endDate: string
  region?: string
  personnelType?: string
  riskLevel?: string
  remarks?: string
}

// 生成报表
export const generateReport = (data: ReportGenerateData) => {
  return mockApi.generateReport(data)
}

// 获取报表列表
export const getReportList = (params: ReportQuery) => {
  return mockApi.getReportList(params)
}

// 下载报表
export const downloadReport = (id: number) => {
  return mockApi.downloadReport(id)
}

// 删除报表
export const deleteReport = (id: number) => {
  return mockApi.deleteReport(id)
}

// 重试报表生成
export const retryReport = (id: number) => {
  return mockApi.retryReport(id)
}

// 获取报表预览
export const getReportPreview = (id: number) => {
  return mockApi.getReportPreview(id)
}

// 通用接口
// 获取组织列表
export const getOrganizationList = () => {
  return mockApi.getOrganizationList()
}

// 获取区域列表
export const getRegionList = () => {
  return mockApi.getRegionList()
}

// 文件上传
export const uploadFile = (file: File) => {
  return mockApi.uploadFile(file)
}

// 导出人员数据
export const exportPersonnelData = (params: any) => {
  return mockApi.exportPersonnelData(params)
}

// 导出黑名单数据
export const exportBlacklistData = (params: any) => {
  return mockApi.exportBlacklistData(params)
}

// 处理操作相关接口
export interface ProcessingStatusUpdateData {
  personnelId: number
  fromStatus: number
  toStatus: number
  reason: string
}

export interface ProcessingHistoryQuery {
  personnelId: number
  page?: number
  size?: number
}

// 更新处理状态
export const updateProcessingStatus = (data: ProcessingStatusUpdateData) => {
  return mockApi.updateProcessingStatus(data)
}

// 批量更新正常人员的处理状态为"无需处理"
export const batchUpdateNormalPersonnelStatus = () => {
  return mockApi.batchUpdateNormalPersonnelStatus()
}

// 获取处理历史
export const getProcessingHistory = (personnelId: number) => {
  return mockApi.getProcessingHistory(personnelId)
}

// 通知管理相关接口
export interface NotificationSendData {
  personnelId: number
  organizationName: string
  notificationType: number
  notificationContent: string
}

export interface NotificationHistoryQuery {
  personnelId?: number
  organizationId?: number
  notificationType?: number
  sendStatus?: number
  page?: number
  size?: number
}

// 发送通知
export const sendNotification = (data: NotificationSendData) => {
  return mockApi.sendPersonnelNotification(data)
}

// 获取通知历史
export const getNotificationHistory = (personnelId: number) => {
  return mockApi.getNotificationHistory(personnelId)
}

// 获取所有通知记录（用于通知记录管理页面）
export const getAllNotificationRecords = (params: NotificationHistoryQuery) => {
  return mockApi.getAllNotificationRecords(params)
}

// 导出数据
export const exportData = (params: {
  type: string // 'personnel' | 'blacklist'
  format: string // 'excel' | 'csv'
  filters?: any
}) => {
  return mockApi.exportData(params)
}

// 数据同步状态
export const getSyncStatus = () => {
  return mockApi.getSyncStatus()
}

// 手动触发数据同步
export const triggerSync = () => {
  return mockApi.triggerSync()
}