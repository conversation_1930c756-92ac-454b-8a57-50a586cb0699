// API 基础配置和工具函数
import request from '@/utils/request'

// 示例 API 接口
export const exampleApi = {
  // 获取数据示例
  getData: () => request.get('/api/data'),
  
  // 创建数据示例
  createData: (data: any) => request.post('/api/data', data),
  
  // 更新数据示例
  updateData: (id: string, data: any) => request.put(`/api/data/${id}`, data),
  
  // 删除数据示例
  deleteData: (id: string) => request.delete(`/api/data/${id}`)
}

export default {
  exampleApi
}