<template>
  <BaseChart
    title="工作量统计"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    data: ['审查数量', '处理数量'],
    bottom: '5%',
    textStyle: {
      fontSize: 12
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    axisLabel: {
      fontSize: 11
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 11
    }
  },
  series: [
    {
      name: '审查数量',
      type: 'bar',
      data: [89, 76, 94, 108, 87, 95],
      itemStyle: {
        color: '#409EFF'
      },
      barWidth: '35%'
    },
    {
      name: '处理数量',
      type: 'bar',
      data: [67, 58, 72, 89, 71, 78],
      itemStyle: {
        color: '#67C23A'
      },
      barWidth: '35%'
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开工作量统计详情')
}

onMounted(() => {
  fetchData()
})
</script>
