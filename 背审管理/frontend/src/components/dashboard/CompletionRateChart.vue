<template>
  <BaseChart
    title="处理完成率趋势"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
    formatter: function (params: any) {
      return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value}%`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    top: '10%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLabel: {
        fontSize: 11
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        fontSize: 11,
        formatter: '{value}%'
      }
    }
  ],
  series: [
    {
      name: '完成率',
      type: 'line',
      smooth: true,
      lineStyle: {
        width: 3
      },
      areaStyle: {
        opacity: 0.3,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#67C23A'
          }, {
            offset: 1, color: 'rgba(103, 194, 58, 0.1)'
          }]
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: [67, 83, 75, 91, 89, 86],
      itemStyle: {
        color: '#67C23A'
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开处理完成率趋势详情')
}

onMounted(() => {
  fetchData()
})
</script>
