<template>
  <el-card class="metrics-card" shadow="hover" @click="handleClick">
    <template #header>
      <div class="card-header">
        <span class="card-title">人员总览</span>
        <el-icon class="expand-icon">
          <FullScreen />
        </el-icon>
      </div>
    </template>
    
    <div v-loading="loading" class="metrics-content">
      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="4" animated />
      </div>
      
      <div v-else-if="error" class="error-state">
        <el-result icon="error" title="加载失败" :sub-title="error">
          <template #extra>
            <el-button type="primary" size="small" @click="fetchData">重试</el-button>
          </template>
        </el-result>
      </div>
      
      <div v-else class="metrics-grid">
        <div class="metric-item">
          <div class="metric-icon security">
            <el-icon><User /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metricsData.totalPersonnel }}</div>
            <div class="metric-label">安保人员</div>
            <div class="metric-trend">
              <span class="trend-value" :class="metricsData.totalTrend > 0 ? 'positive' : 'negative'">
                {{ metricsData.totalTrend > 0 ? '+' : '' }}{{ metricsData.totalTrend }}%
              </span>
              <span class="trend-label">同比</span>
            </div>
          </div>
        </div>
        
        <div class="metric-item">
          <div class="metric-icon abnormal">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metricsData.abnormalPersonnel }}</div>
            <div class="metric-label">异常人员</div>
            <div class="metric-trend">
              <span class="trend-value" :class="metricsData.abnormalTrend > 0 ? 'negative' : 'positive'">
                {{ metricsData.abnormalTrend > 0 ? '+' : '' }}{{ metricsData.abnormalTrend }}%
              </span>
              <span class="trend-label">同比</span>
            </div>
          </div>
        </div>
        
        <div class="metric-item">
          <div class="metric-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metricsData.pendingPersonnel }}</div>
            <div class="metric-label">待处理人员</div>
            <div class="metric-trend">
              <span class="trend-value" :class="metricsData.pendingTrend > 0 ? 'negative' : 'positive'">
                {{ metricsData.pendingTrend > 0 ? '+' : '' }}{{ metricsData.pendingTrend }}%
              </span>
              <span class="trend-label">同比</span>
            </div>
          </div>
        </div>
        
        <div class="metric-item">
          <div class="metric-icon focus">
            <el-icon><View /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metricsData.focusPersonnel }}</div>
            <div class="metric-label">关注人员</div>
            <div class="metric-trend">
              <span class="trend-value" :class="metricsData.focusTrend > 0 ? 'negative' : 'positive'">
                {{ metricsData.focusTrend > 0 ? '+' : '' }}{{ metricsData.focusTrend }}%
              </span>
              <span class="trend-label">同比</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { FullScreen, User, Warning, Clock, View } from '@element-plus/icons-vue'

const emit = defineEmits<{
  click: []
}>()

const loading = ref(false)
const error = ref('')

const metricsData = ref({
  totalPersonnel: 1248,
  totalTrend: 5.2,
  abnormalPersonnel: 127,
  abnormalTrend: -8.3,
  pendingPersonnel: 45,
  pendingTrend: -12.5,
  focusPersonnel: 38,
  focusTrend: -15.2
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    console.log('人员指标数据加载完成')
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleClick = () => {
  emit('click')
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.metrics-card {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.metrics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.expand-icon {
  color: #909399;
  font-size: 16px;
  transition: color 0.3s ease;
}

.metrics-card:hover .expand-icon {
  color: #409eff;
}

.metrics-content {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-state {
  width: 100%;
  padding: 20px;
}

.error-state {
  width: 100%;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  width: 100%;
  height: 100%;
  align-content: center;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.metric-item:hover {
  background: #e3f2fd;
  transform: translateY(-2px);
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.metric-icon.security {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon.abnormal {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-icon.pending {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.metric-icon.focus {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-value {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.trend-value.positive {
  color: #67c23a;
  background: #f0f9ff;
}

.trend-value.negative {
  color: #f56c6c;
  background: #fef0f0;
}

.trend-label {
  font-size: 12px;
  color: #909399;
}

/* 紧凑型设计 */
:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
