<template>
  <BaseChart
    title="处理状态分布"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '处理状态',
      type: 'pie',
      radius: ['30%', '65%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outside',
        fontSize: 11,
        formatter: '{b}\n{c}人'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '12',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: true
      },
      data: [
        { 
          value: 45, 
          name: '未处理',
          itemStyle: { color: '#E6A23C' }
        },
        { 
          value: 38, 
          name: '重点关注',
          itemStyle: { color: '#F56C6C' }
        },
        { 
          value: 32, 
          name: '调岗/劝退',
          itemStyle: { color: '#909399' }
        },
        { 
          value: 12, 
          name: '无需处理',
          itemStyle: { color: '#67C23A' }
        }
      ]
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开处理状态分布详情')
}

onMounted(() => {
  fetchData()
})
</script>
