<template>
  <el-dialog
    v-model="dialogVisible"
    title="人员指标详情分析"
    width="90%"
    :before-close="handleClose"
    class="detail-dialog"
  >
    <div class="detail-content">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-card class="filter-card" shadow="never">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleDateChange"
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="6">
              <el-form-item label="人员类型">
                <el-select v-model="selectedType" placeholder="请选择类型" @change="handleTypeChange">
                  <el-option label="全部类型" value="" />
                  <el-option label="专职保卫" value="security" />
                  <el-option label="保安人员" value="guard" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 简化的指标卡片 -->
      <div class="metrics-cards">
        <el-row :gutter="20">
          <el-col :span="6" v-for="metric in detailMetrics" :key="metric.key">
            <el-card class="metric-detail-card" shadow="hover">
              <div class="metric-content">
                <div class="metric-title">{{ metric.title }}</div>
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-trends">
                  <div class="trend-item">
                    <span class="trend-label">同比:</span>
                    <span class="trend-value" :class="metric.yearOverYear > 0 ? 'positive' : 'negative'">
                      {{ metric.yearOverYear > 0 ? '+' : '' }}{{ metric.yearOverYear }}%
                    </span>
                  </div>
                  <div class="trend-item">
                    <span class="trend-label">环比:</span>
                    <span class="trend-value" :class="metric.monthOverMonth > 0 ? 'positive' : 'negative'">
                      {{ metric.monthOverMonth > 0 ? '+' : '' }}{{ metric.monthOverMonth }}%
                    </span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 趋势分析图表 -->
      <div class="trend-charts">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">人员数量趋势</span>
              </template>
              <v-chart class="chart" :option="trendChartOptions" autoresize />
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">同比增长率分析</span>
              </template>
              <v-chart class="chart" :option="growthChartOptions" autoresize />
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 详细数据表格 -->
      <div class="data-table">
        <el-card class="table-card" shadow="never">
          <template #header>
            <div class="table-header">
              <span class="table-title">详细数据</span>
              <el-button type="primary" size="small" @click="exportData">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </template>
          <el-table :data="tableData" stripe style="width: 100%">
            <el-table-column prop="type" label="人员类型" width="120" />
            <el-table-column prop="current" label="当前数量" width="100" />
            <el-table-column prop="lastMonth" label="上月数量" width="100" />
            <el-table-column prop="lastYear" label="去年同期" width="100" />
            <el-table-column prop="monthGrowth" label="环比增长" width="100">
              <template #default="{ row }">
                <span :class="row.monthGrowth > 0 ? 'positive' : 'negative'">
                  {{ row.monthGrowth > 0 ? '+' : '' }}{{ row.monthGrowth }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="yearGrowth" label="同比增长" width="100">
              <template #default="{ row }">
                <span :class="row.yearGrowth > 0 ? 'positive' : 'negative'">
                  {{ row.yearGrowth > 0 ? '+' : '' }}{{ row.yearGrowth }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportChart">导出图表</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Warning, Clock, View, Download } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 筛选条件
const dateRange = ref(['2024-01-01', '2024-06-30'])
const selectedType = ref('')

// 详细指标数据
const detailMetrics = ref([
  {
    key: 'total',
    title: '安保人员',
    value: 1248,
    yearOverYear: 5.2,
    monthOverMonth: 2.1
  },
  {
    key: 'abnormal',
    title: '异常人员',
    value: 127,
    yearOverYear: -8.3,
    monthOverMonth: -3.2
  },
  {
    key: 'pending',
    title: '待处理人员',
    value: 45,
    yearOverYear: -12.5,
    monthOverMonth: -5.8
  },
  {
    key: 'focus',
    title: '关注人员',
    value: 38,
    yearOverYear: -15.2,
    monthOverMonth: -7.1
  }
])

// 趋势图表配置
const trendChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  legend: {
    data: ['安保人员', '异常人员', '待处理人员', '关注人员']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '安保人员',
      type: 'line',
      data: [1180, 1195, 1210, 1225, 1240, 1248],
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '异常人员',
      type: 'line',
      data: [145, 138, 132, 129, 128, 127],
      itemStyle: { color: '#F56C6C' }
    },
    {
      name: '待处理人员',
      type: 'line',
      data: [58, 52, 48, 46, 45, 45],
      itemStyle: { color: '#E6A23C' }
    },
    {
      name: '关注人员',
      type: 'line',
      data: [48, 45, 42, 40, 39, 38],
      itemStyle: { color: '#67C23A' }
    }
  ]
})

// 增长率图表配置
const growthChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let result = `${params[0].name}<br/>`
      params.forEach((param: any) => {
        result += `${param.seriesName}: ${param.value}%<br/>`
      })
      return result
    }
  },
  legend: {
    data: ['同比增长率', '环比增长率']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['安保人员', '异常人员', '待处理人员', '关注人员']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: '同比增长率',
      type: 'bar',
      data: [5.2, -8.3, -12.5, -15.2],
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '环比增长率',
      type: 'bar',
      data: [2.1, -3.2, -5.8, -7.1],
      itemStyle: { color: '#67C23A' }
    }
  ]
})

// 表格数据
const tableData = ref([
  {
    type: '安保人员',
    current: 1248,
    lastMonth: 1240,
    lastYear: 1186,
    monthGrowth: 2.1,
    yearGrowth: 5.2,
    status: '正常'
  },
  {
    type: '异常人员',
    current: 127,
    lastMonth: 128,
    lastYear: 139,
    monthGrowth: -3.2,
    yearGrowth: -8.3,
    status: '下降'
  },
  {
    type: '待处理人员',
    current: 45,
    lastMonth: 46,
    lastYear: 52,
    monthGrowth: -5.8,
    yearGrowth: -12.5,
    status: '改善'
  },
  {
    type: '关注人员',
    current: 38,
    lastMonth: 39,
    lastYear: 45,
    monthGrowth: -7.1,
    yearGrowth: -15.2,
    status: '改善'
  }
])

const handleClose = () => {
  dialogVisible.value = false
}

// 筛选相关方法
const handleDateChange = (dates: string[]) => {
  console.log('日期范围变更:', dates)
  // 这里可以调用API重新获取数据
}

const handleTypeChange = (type: string) => {
  console.log('人员类型变更:', type)
  // 这里可以调用API重新获取数据
}

const handleSearch = () => {
  ElMessage.success('查询条件已应用')
  // 这里可以调用API重新获取数据
}

const handleReset = () => {
  dateRange.value = ['2024-01-01', '2024-06-30']
  selectedType.value = ''
  ElMessage.info('筛选条件已重置')
  // 这里可以调用API重新获取数据
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '正常': 'success',
    '下降': 'danger',
    '改善': 'success'
  }
  return typeMap[status] || 'info'
}

const exportData = () => {
  ElMessage.success('数据导出成功')
}

const exportChart = () => {
  ElMessage.success('图表导出成功')
}
</script>

<style scoped>
.detail-dialog {
  --el-dialog-padding-primary: 20px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 筛选条件样式 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  border: 1px solid #e4e7ed;
}

/* 指标卡片样式 */
.metrics-cards {
  margin-bottom: 24px;
}

.metric-detail-card {
  height: 120px;
  transition: all 0.3s ease;
}

.metric-detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.metric-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
}

.metric-trends {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-label {
  font-size: 12px;
  color: #909399;
}

.trend-value {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.trend-value.positive {
  color: #67c23a;
  background: #f0f9ff;
}

.trend-value.negative {
  color: #f56c6c;
  background: #fef0f0;
}

/* 图表样式 */
.trend-charts {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart {
  width: 100%;
  height: 320px;
}

/* 表格样式 */
.data-table {
  margin-bottom: 24px;
}

.table-card {
  min-height: 300px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-content {
    gap: 16px;
  }

  .metric-detail-card {
    height: 120px;
  }

  .chart-card {
    height: 300px;
  }

  .chart {
    height: 220px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
