<template>
  <el-dialog
    v-model="dialogVisible"
    title="月度新增人员趋势详情分析"
    width="85%"
    :before-close="handleClose"
    class="detail-dialog"
  >
    <div class="detail-content">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-card class="filter-card" shadow="never">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleDateChange"
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="6">
              <el-form-item label="统计维度">
                <el-select v-model="selectedDimension" placeholder="请选择维度" @change="handleDimensionChange">
                  <el-option label="按月统计" value="month" />
                  <el-option label="按季度统计" value="quarter" />
                  <el-option label="按年统计" value="year" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 简化的关键指标概览 -->
      <div class="key-metrics">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="metric-card" shadow="hover">
              <div class="metric-content">
                <div class="metric-title">半年新增总数</div>
                <div class="metric-value">457</div>
                <div class="metric-trends">
                  <span class="trend-label">同比:</span>
                  <span class="trend-value positive">+6.8%</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="metric-card" shadow="hover">
              <div class="metric-content">
                <div class="metric-title">月均新增</div>
                <div class="metric-value">76</div>
                <div class="metric-trends">
                  <span class="trend-label">环比:</span>
                  <span class="trend-value positive">+4.2%</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="metric-card" shadow="hover">
              <div class="metric-content">
                <div class="metric-title">峰值月份(4月)</div>
                <div class="metric-value">101</div>
                <div class="metric-trends">
                  <span class="trend-label">同比:</span>
                  <span class="trend-value positive">+12.5%</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="metric-card" shadow="hover">
              <div class="metric-content">
                <div class="metric-title">平均增长率</div>
                <div class="metric-value">6.1%</div>
                <div class="metric-trends">
                  <span class="trend-label">状态:</span>
                  <span class="trend-value positive">稳定增长</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 详细分析图表 -->
      <div class="analysis-charts">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">月度新增趋势与同比分析</span>
              </template>
              <v-chart class="chart" :option="trendChartOptions" autoresize />
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">环比增长率分析</span>
              </template>
              <v-chart class="chart" :option="monthOverMonthOptions" autoresize />
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 季度对比分析 -->
      <div class="quarterly-analysis">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">季度新增对比</span>
              </template>
              <v-chart class="chart" :option="quarterlyChartOptions" autoresize />
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">年度同期对比</span>
              </template>
              <v-chart class="chart" :option="yearlyComparisonOptions" autoresize />
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 详细数据表格 -->
      <div class="data-table">
        <el-card class="table-card" shadow="never">
          <template #header>
            <div class="table-header">
              <span class="table-title">月度新增详细数据</span>
              <el-button type="primary" size="small" @click="exportData">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </template>
          <el-table :data="tableData" stripe style="width: 100%">
            <el-table-column prop="month" label="月份" width="80" />
            <el-table-column prop="newPersonnel" label="新增人员" width="100" />
            <el-table-column prop="lastMonth" label="上月新增" width="100" />
            <el-table-column prop="lastYear" label="去年同期" width="100" />
            <el-table-column prop="monthGrowth" label="环比增长" width="100">
              <template #default="{ row }">
                <span :class="row.monthGrowth > 0 ? 'positive' : 'negative'">
                  {{ row.monthGrowth > 0 ? '+' : '' }}{{ row.monthGrowth }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="yearGrowth" label="同比增长" width="100">
              <template #default="{ row }">
                <span :class="row.yearGrowth > 0 ? 'positive' : 'negative'">
                  {{ row.yearGrowth > 0 ? '+' : '' }}{{ row.yearGrowth }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="cumulative" label="累计新增" width="100" />
            <el-table-column prop="target" label="目标完成率" width="120">
              <template #default="{ row }">
                <el-progress 
                  :percentage="row.target" 
                  :color="row.target >= 100 ? '#67c23a' : '#409eff'"
                  :stroke-width="8"
                />
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportChart">导出图表</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  User,
  Top,
  Promotion,
  Download
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 筛选条件
const dateRange = ref(['2024-01-01', '2024-06-30'])
const selectedDimension = ref('month')

// 趋势图表配置
const trendChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  legend: {
    data: ['新增人员', '同比增长率']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: [
    {
      type: 'value',
      name: '人数',
      position: 'left'
    },
    {
      type: 'value',
      name: '增长率(%)',
      position: 'right'
    }
  ],
  series: [
    {
      name: '新增人员',
      type: 'bar',
      data: [68, 80, 57, 101, 64, 87],
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '同比增长率',
      type: 'line',
      yAxisIndex: 1,
      data: [5.2, 8.7, -2.3, 12.5, 3.8, 9.1],
      itemStyle: { color: '#67C23A' }
    }
  ]
})

// 环比增长率图表
const monthOverMonthOptions = ref({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value}%`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: '环比增长率',
      type: 'line',
      data: [17.6, -28.8, 77.2, -36.6, 35.9],
      itemStyle: { color: '#E6A23C' },
      areaStyle: {
        opacity: 0.3
      }
    }
  ]
})

// 季度对比图表
const quarterlyChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' }
  },
  legend: {
    data: ['今年', '去年']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['Q1', 'Q2']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '今年',
      type: 'bar',
      data: [205, 252],
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '去年',
      type: 'bar',
      data: [198, 235],
      itemStyle: { color: '#67C23A' }
    }
  ]
})

// 年度同期对比图表
const yearlyComparisonOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  legend: {
    data: ['2024年', '2023年', '2022年']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '2024年',
      type: 'line',
      data: [68, 80, 57, 101, 64, 87],
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '2023年',
      type: 'line',
      data: [65, 74, 58, 90, 62, 80],
      itemStyle: { color: '#67C23A' }
    },
    {
      name: '2022年',
      type: 'line',
      data: [62, 70, 55, 85, 58, 75],
      itemStyle: { color: '#E6A23C' }
    }
  ]
})

// 表格数据
const tableData = ref([
  {
    month: '1月',
    newPersonnel: 68,
    lastMonth: 58,
    lastYear: 65,
    monthGrowth: 17.2,
    yearGrowth: 5.2,
    cumulative: 68,
    target: 85,
    status: '正常'
  },
  {
    month: '2月',
    newPersonnel: 80,
    lastMonth: 68,
    lastYear: 74,
    monthGrowth: 17.6,
    yearGrowth: 8.7,
    cumulative: 148,
    target: 106,
    status: '超额'
  },
  {
    month: '3月',
    newPersonnel: 57,
    lastMonth: 80,
    lastYear: 58,
    monthGrowth: -28.8,
    yearGrowth: -2.3,
    cumulative: 205,
    target: 76,
    status: '偏低'
  },
  {
    month: '4月',
    newPersonnel: 101,
    lastMonth: 57,
    lastYear: 90,
    monthGrowth: 77.2,
    yearGrowth: 12.5,
    cumulative: 306,
    target: 135,
    status: '超额'
  },
  {
    month: '5月',
    newPersonnel: 64,
    lastMonth: 101,
    lastYear: 62,
    monthGrowth: -36.6,
    yearGrowth: 3.8,
    cumulative: 370,
    target: 85,
    status: '正常'
  },
  {
    month: '6月',
    newPersonnel: 87,
    lastMonth: 64,
    lastYear: 80,
    monthGrowth: 35.9,
    yearGrowth: 9.1,
    cumulative: 457,
    target: 116,
    status: '正常'
  }
])

const handleClose = () => {
  dialogVisible.value = false
}

// 筛选相关方法
const handleDateChange = (dates: string[]) => {
  console.log('日期范围变更:', dates)
}

const handleDimensionChange = (dimension: string) => {
  console.log('统计维度变更:', dimension)
}

const handleSearch = () => {
  ElMessage.success('查询条件已应用')
}

const handleReset = () => {
  dateRange.value = ['2024-01-01', '2024-06-30']
  selectedDimension.value = 'month'
  ElMessage.info('筛选条件已重置')
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '正常': 'success',
    '超额': 'success',
    '偏低': 'warning',
    '异常': 'danger'
  }
  return typeMap[status] || 'info'
}

const exportData = () => {
  ElMessage.success('数据导出成功')
}

const exportChart = () => {
  ElMessage.success('图表导出成功')
}
</script>

<style scoped>
.detail-dialog {
  --el-dialog-padding-primary: 20px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 筛选条件样式 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  border: 1px solid #e4e7ed;
}

/* 关键指标样式 */
.key-metrics {
  margin-bottom: 24px;
}

.metric-card {
  height: 120px;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.metric-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
}

.metric-trends {
  display: flex;
  justify-content: center;
  gap: 8px;
  font-size: 12px;
}

.trend-label {
  color: #909399;
}

.trend-value {
  font-weight: 600;
}

.trend-value.positive {
  color: #67c23a;
}

.trend-value.negative {
  color: #f56c6c;
}

/* 图表样式 */
.analysis-charts,
.quarterly-analysis {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart {
  width: 100%;
  height: 320px;
}

/* 表格样式 */
.data-table {
  margin-bottom: 24px;
}

.table-card {
  min-height: 300px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-content {
    gap: 16px;
  }

  .metric-card {
    height: 100px;
  }

  .chart-card {
    height: 300px;
  }

  .chart {
    height: 220px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
