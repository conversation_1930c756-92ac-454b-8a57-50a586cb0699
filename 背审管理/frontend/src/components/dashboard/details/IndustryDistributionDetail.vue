<template>
  <el-dialog
    v-model="dialogVisible"
    title="各行业人员分布详情分析"
    width="85%"
    :before-close="handleClose"
    class="detail-dialog"
  >
    <div class="detail-content">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-card class="filter-card" shadow="never">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleDateChange"
                />
              </el-form-item>
            </el-col>
           
            <el-col :span="6">
              <el-form-item label="行业">
                <el-select v-model="selectedIndustry" placeholder="请选择行业" @change="handleIndustryChange">
                  <el-option label="全部行业" value="" />
                  <el-option label="政府机关" value="government" />
                  <el-option label="教育系统" value="education" />
                  <el-option label="医疗卫生" value="medical" />
                  <el-option label="交通运输" value="transport" />
                  <el-option label="金融保险" value="finance" />
                  <el-option label="其他行业" value="others" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 简化的行业概览 -->
      <div class="industry-overview">
        <el-row :gutter="16">
          <el-col :span="4" v-for="industry in industryStats" :key="industry.name">
            <el-card class="industry-card" shadow="hover">
              <div class="industry-content">
                <div class="industry-name">{{ industry.name }}</div>
                <div class="industry-count">{{ industry.count }}</div>
                <div class="industry-percentage">{{ industry.percentage }}</div>
                <div class="industry-trends">
                  <span class="trend-label">同比:</span>
                  <span class="trend-value positive">+{{ industry.yearGrowth }}%</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分析图表 -->
      <div class="analysis-charts">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">行业分布占比</span>
              </template>
              <v-chart class="chart" :option="pieChartOptions" autoresize />
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">行业人员数量对比</span>
              </template>
              <v-chart class="chart" :option="barChartOptions" autoresize />
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="chart-title">行业增长趋势</span>
              </template>
              <v-chart class="chart" :option="trendChartOptions" autoresize />
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 同比环比分析 -->
      <div class="comparison-analysis">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span class="chart-title">各行业同比环比增长分析</span>
          </template>
          <v-chart class="chart" :option="comparisonChartOptions" autoresize />
        </el-card>
      </div>

      <!-- 详细数据表格 -->
      <div class="data-table">
        <el-card class="table-card" shadow="never">
          <template #header>
            <div class="table-header">
              <span class="table-title">各行业详细统计数据</span>
              <el-button type="primary" size="small" @click="exportData">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </template>
          <el-table :data="tableData" stripe style="width: 100%">
            <el-table-column prop="industry" label="行业" width="120" />
            <el-table-column prop="current" label="当前人数" width="100" />
            <el-table-column prop="percentage" label="占比" width="80" />
            <el-table-column prop="lastMonth" label="上月人数" width="100" />
            <el-table-column prop="lastYear" label="去年同期" width="100" />
            <el-table-column prop="monthGrowth" label="环比增长" width="100">
              <template #default="{ row }">
                <span :class="row.monthGrowth > 0 ? 'positive' : 'negative'">
                  {{ row.monthGrowth > 0 ? '+' : '' }}{{ row.monthGrowth }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="yearGrowth" label="同比增长" width="100">
              <template #default="{ row }">
                <span :class="row.yearGrowth > 0 ? 'positive' : 'negative'">
                  {{ row.yearGrowth > 0 ? '+' : '' }}{{ row.yearGrowth }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="avgAge" label="平均年龄" width="100" />
            <el-table-column prop="turnoverRate" label="流失率" width="100">
              <template #default="{ row }">
                <span :class="row.turnoverRate > 10 ? 'negative' : 'positive'">
                  {{ row.turnoverRate }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="招聘状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportChart">导出图表</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User,
  UserFilled,
  Warning,
  Clock,
  View,
  MoreFilled,
  Download
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 筛选条件
const dateRange = ref(['2024-01-01', '2024-06-30'])
const selectedIndustry = ref('')

// 行业统计数据
const industryStats = ref([
  {
    name: '政府机关',
    count: 245,
    percentage: '19.6%',
    yearGrowth: 4.2
  },
  {
    name: '教育系统',
    count: 198,
    percentage: '15.9%',
    yearGrowth: 6.8
  },
  {
    name: '医疗卫生',
    count: 167,
    percentage: '13.4%',
    yearGrowth: 2.1
  },
  {
    name: '交通运输',
    count: 234,
    percentage: '18.8%',
    yearGrowth: 8.5
  },
  {
    name: '金融保险',
    count: 189,
    percentage: '15.1%',
    yearGrowth: 3.7
  },
  {
    name: '其他行业',
    count: 215,
    percentage: '17.2%',
    yearGrowth: 5.9
  }
])

// 饼图配置
const pieChartOptions = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '行业分布',
      type: 'pie',
      radius: '60%',
      data: [
        { value: 245, name: '政府机关', itemStyle: { color: '#5470C6' } },
        { value: 234, name: '交通运输', itemStyle: { color: '#EE6666' } },
        { value: 215, name: '其他行业', itemStyle: { color: '#3BA272' } },
        { value: 198, name: '教育系统', itemStyle: { color: '#91CC75' } },
        { value: 189, name: '金融保险', itemStyle: { color: '#73C0DE' } },
        { value: 167, name: '医疗卫生', itemStyle: { color: '#FAC858' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

// 柱状图配置
const barChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['政府机关', '交通运输', '其他行业', '教育系统', '金融保险', '医疗卫生'],
    axisLabel: {
      interval: 0,
      rotate: 45,
      fontSize: 10
    }
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '人员数量',
      type: 'bar',
      data: [
        { value: 245, itemStyle: { color: '#5470C6' } },
        { value: 234, itemStyle: { color: '#EE6666' } },
        { value: 215, itemStyle: { color: '#3BA272' } },
        { value: 198, itemStyle: { color: '#91CC75' } },
        { value: 189, itemStyle: { color: '#73C0DE' } },
        { value: 167, itemStyle: { color: '#FAC858' } }
      ],
      label: {
        show: true,
        position: 'top'
      }
    }
  ]
})

// 趋势图配置
const trendChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  legend: {
    data: ['政府机关', '交通运输', '教育系统'],
    textStyle: {
      fontSize: 10
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '政府机关',
      type: 'line',
      data: [235, 238, 240, 242, 244, 245],
      itemStyle: { color: '#5470C6' }
    },
    {
      name: '交通运输',
      type: 'line',
      data: [225, 228, 230, 232, 233, 234],
      itemStyle: { color: '#EE6666' }
    },
    {
      name: '教育系统',
      type: 'line',
      data: [190, 192, 194, 196, 197, 198],
      itemStyle: { color: '#91CC75' }
    }
  ]
})

// 同比环比分析图配置
const comparisonChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' }
  },
  legend: {
    data: ['同比增长率', '环比增长率']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['政府机关', '教育系统', '医疗卫生', '交通运输', '金融保险', '其他行业'],
    axisLabel: {
      interval: 0,
      rotate: 45
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: '同比增长率',
      type: 'bar',
      data: [4.2, 6.8, 2.1, 8.5, 3.7, 5.9],
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '环比增长率',
      type: 'bar',
      data: [1.8, 2.3, 0.8, 3.2, 1.5, 2.1],
      itemStyle: { color: '#67C23A' }
    }
  ]
})

// 表格数据
const tableData = ref([
  {
    industry: '政府机关',
    current: 245,
    percentage: '19.6%',
    lastMonth: 240,
    lastYear: 235,
    monthGrowth: 1.8,
    yearGrowth: 4.2,
    avgAge: 42,
    turnoverRate: 3.2,
    status: '正常'
  },
  {
    industry: '交通运输',
    current: 234,
    percentage: '18.8%',
    lastMonth: 227,
    lastYear: 216,
    monthGrowth: 3.2,
    yearGrowth: 8.5,
    avgAge: 38,
    turnoverRate: 8.5,
    status: '急需'
  },
  {
    industry: '其他行业',
    current: 215,
    percentage: '17.2%',
    lastMonth: 211,
    lastYear: 203,
    monthGrowth: 2.1,
    yearGrowth: 5.9,
    avgAge: 35,
    turnoverRate: 12.3,
    status: '正常'
  },
  {
    industry: '教育系统',
    current: 198,
    percentage: '15.9%',
    lastMonth: 194,
    lastYear: 185,
    monthGrowth: 2.3,
    yearGrowth: 6.8,
    avgAge: 45,
    turnoverRate: 4.1,
    status: '正常'
  },
  {
    industry: '金融保险',
    current: 189,
    percentage: '15.1%',
    lastMonth: 186,
    lastYear: 182,
    monthGrowth: 1.5,
    yearGrowth: 3.7,
    avgAge: 40,
    turnoverRate: 6.8,
    status: '正常'
  },
  {
    industry: '医疗卫生',
    current: 167,
    percentage: '13.4%',
    lastMonth: 166,
    lastYear: 164,
    monthGrowth: 0.8,
    yearGrowth: 2.1,
    avgAge: 43,
    turnoverRate: 5.2,
    status: '正常'
  }
])

const handleClose = () => {
  dialogVisible.value = false
}

// 筛选相关方法
const handleDateChange = (dates: string[]) => {
  console.log('日期范围变更:', dates)
}

const handleIndustryChange = (industry: string) => {
  console.log('行业变更:', industry)
}

const handleSearch = () => {
  ElMessage.success('查询条件已应用')
}

const handleReset = () => {
  dateRange.value = ['2024-01-01', '2024-06-30']
  selectedIndustry.value = ''
  ElMessage.info('筛选条件已重置')
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '正常': 'success',
    '急需': 'warning',
    '暂停': 'danger'
  }
  return typeMap[status] || 'info'
}

const exportData = () => {
  ElMessage.success('数据导出成功')
}

const exportChart = () => {
  ElMessage.success('图表导出成功')
}
</script>

<style scoped>
.detail-dialog {
  --el-dialog-padding-primary: 20px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 筛选条件样式 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  border: 1px solid #e4e7ed;
}

/* 行业概览样式 */
.industry-overview {
  margin-bottom: 24px;
}

.industry-card {
  height: 120px;
  transition: all 0.3s ease;
}

.industry-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.industry-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 12px;
}

.industry-name {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
}

.industry-count {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.industry-percentage {
  font-size: 11px;
  color: #909399;
  margin-bottom: 8px;
}

.industry-trends {
  font-size: 11px;
}

.trend-label {
  color: #909399;
}

.trend-value {
  font-weight: 600;
  color: #67c23a;
}

/* 图表样式 */
.analysis-charts,
.comparison-analysis {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart {
  width: 100%;
  height: 320px;
}

/* 表格样式 */
.data-table {
  margin-bottom: 24px;
}

.table-card {
  min-height: 300px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-content {
    gap: 16px;
  }

  .industry-card {
    height: 80px;
  }

  .chart-card {
    height: 300px;
  }

  .chart {
    height: 220px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
