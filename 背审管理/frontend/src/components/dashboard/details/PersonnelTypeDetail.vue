<template>
  <el-dialog
    v-model="dialogVisible"
    title="人员类型分布详情分析"
    width="80%"
    :before-close="handleClose"
    class="detail-dialog"
  >
    <div class="detail-content">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-card class="filter-card" shadow="never">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleDateChange"
                />
              </el-form-item>
            </el-col>
           
           
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 简化的概览统计 -->
      <div class="overview-stats">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-title">总人数</div>
                <div class="stat-value">1248</div>
                <div class="stat-trends">
                  <span class="trend-label">同比:</span>
                  <span class="trend-value positive">+5.2%</span>
                  <span class="trend-label">环比:</span>
                  <span class="trend-value positive">+2.1%</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-title">专职保卫</div>
                <div class="stat-value">856</div>
                <div class="stat-trends">
                  <span class="trend-label">同比:</span>
                  <span class="trend-value positive">+3.8%</span>
                  <span class="trend-label">环比:</span>
                  <span class="trend-value positive">+1.2%</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card" shadow="hover">
              <div class="stat-content">
                <div class="stat-title">保安人员</div>
                <div class="stat-value">392</div>
                <div class="stat-trends">
                  <span class="trend-label">同比:</span>
                  <span class="trend-value positive">+7.2%</span>
                  <span class="trend-label">环比:</span>
                  <span class="trend-value positive">+2.8%</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 详细分析图表 -->
      

      <!-- 同比环比分析 -->
      <div class="comparison-analysis">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span class="chart-title">同比环比增长分析</span>
          </template>
          <v-chart class="chart" :option="comparisonChartOptions" autoresize />
        </el-card>
      </div>

      <!-- 详细数据表格 -->
      
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportChart">导出图表</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { User, UserFilled, Download } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 筛选条件
const dateRange = ref(['2024-01-01', '2024-06-30'])
const selectedType = ref('')

// 饼图配置
const pieChartOptions = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '人员类型',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 856, name: '专职保卫', itemStyle: { color: '#409EFF' } },
        { value: 392, name: '保安人员', itemStyle: { color: '#67C23A' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

// 趋势图配置
const trendChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  legend: {
    data: ['专职保卫', '保安人员']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '专职保卫',
      type: 'line',
      data: [820, 832, 845, 850, 855, 856],
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '保安人员',
      type: 'line',
      data: [360, 365, 375, 385, 390, 392],
      itemStyle: { color: '#67C23A' }
    }
  ]
})

// 同比环比分析图配置
const comparisonChartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' }
  },
  legend: {
    data: ['同比增长率', '环比增长率']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['专职保卫', '保安人员']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: '同比增长率',
      type: 'bar',
      data: [3.8, 7.2],
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '环比增长率',
      type: 'bar',
      data: [1.2, 2.8],
      itemStyle: { color: '#67C23A' }
    }
  ]
})

// 表格数据
const tableData = ref([
  {
    type: '专职保卫',
    current: 856,
    percentage: '68.6%',
    lastMonth: 850,
    lastYear: 824,
    monthGrowth: 1.2,
    yearGrowth: 3.8,
    avgSalary: '6800',
    status: '正常招聘'
  },
  {
    type: '保安人员',
    current: 392,
    percentage: '31.4%',
    lastMonth: 385,
    lastYear: 366,
    monthGrowth: 2.8,
    yearGrowth: 7.2,
    avgSalary: '4200',
    status: '急需招聘'
  }
])

const handleClose = () => {
  dialogVisible.value = false
}

// 筛选相关方法
const handleDateChange = (dates: string[]) => {
  console.log('日期范围变更:', dates)
}

const handleTypeChange = (type: string) => {
  console.log('人员类型变更:', type)
}

const handleSearch = () => {
  ElMessage.success('查询条件已应用')
}

const handleReset = () => {
  dateRange.value = ['2024-01-01', '2024-06-30']
  selectedType.value = ''
  ElMessage.info('筛选条件已重置')
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '正常招聘': 'success',
    '急需招聘': 'warning',
    '暂停招聘': 'danger'
  }
  return typeMap[status] || 'info'
}

const exportData = () => {
  ElMessage.success('数据导出成功')
}

const exportChart = () => {
  ElMessage.success('图表导出成功')
}
</script>

<style scoped>
.detail-dialog {
  --el-dialog-padding-primary: 20px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 筛选条件样式 */
.filter-section {
  margin-bottom: 0px;
}

.filter-card {
  border: 1px solid #e4e7ed;
}

/* 概览统计样式 */
.overview-stats {
  margin-bottom: 24px;
}

.stat-card {
  height: 150px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
}

.stat-trends {
  display: flex;
  justify-content: center;
  gap: 12px;
  font-size: 12px;
}

.trend-label {
  color: #909399;
}

.trend-value {
  font-weight: 600;
  margin-right: 8px;
}

.trend-value.positive {
  color: #67c23a;
}

.trend-value.negative {
  color: #f56c6c;
}

/* 图表样式 */
.analysis-charts,
.comparison-analysis {
  margin-bottom: 0px;
}

.chart-card {
  height: 400px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart {
  width: 100%;
  height: 320px;
}

/* 表格样式 */
.data-table {
  margin-bottom: 0px;
}

.table-card {
  min-height: 300px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-content {
    gap: 16px;
  }

  .stat-card {
    height: 100px;
  }

  .chart-card {
    height: 300px;
  }

  .chart {
    height: 220px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
