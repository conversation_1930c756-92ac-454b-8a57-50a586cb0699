<template>
  <BaseChart
    title="异常类型分布"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['刑事犯罪', '吸毒记录', '精神疾病', '信用问题', '政治问题', '暴力倾向'],
    axisLabel: {
      fontSize: 10,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 11
    }
  },
  series: [
    {
      name: '人员数量',
      type: 'bar',
      data: [
        { value: 23, itemStyle: { color: '#F56C6C' } },
        { value: 18, itemStyle: { color: '#E6A23C' } },
        { value: 15, itemStyle: { color: '#F78989' } },
        { value: 32, itemStyle: { color: '#FF7875' } },
        { value: 12, itemStyle: { color: '#FF4D4F' } },
        { value: 27, itemStyle: { color: '#CF1322' } }
      ],
      barWidth: '50%',
      label: {
        show: true,
        position: 'top',
        fontSize: 11
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开异常类型分布详情')
}

onMounted(() => {
  fetchData()
})
</script>
