<template>
  <el-card class="chart-card" shadow="hover" @click="handleClick">
    <template #header>
      <div class="chart-header">
        <span class="chart-title">{{ title }}</span>
        <el-icon v-if="expandable" class="expand-icon">
          <FullScreen />
        </el-icon>
      </div>
    </template>
    
    <div class="chart-container" :style="{ height: height }">
      <div v-if="loading" class="chart-loading">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else-if="error" class="chart-error">
        <el-result icon="error" title="加载失败" :sub-title="error">
          <template #extra>
            <el-button type="primary" size="small" @click="$emit('retry')">重试</el-button>
          </template>
        </el-result>
      </div>
      
      <v-chart v-else ref="chartRef" class="chart-content" :option="props.options" autoresize />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { FullScreen } from '@element-plus/icons-vue'

interface Props {
  title: string
  height?: string
  loading?: boolean
  error?: string
  expandable?: boolean
  options?: any
}

const props = withDefaults(defineProps<Props>(), {
  height: '350px',
  loading: false,
  error: '',
  expandable: true,
  options: () => ({})
})

const emit = defineEmits<{
  click: []
  retry: []
}>()

const chartRef = ref()

const handleClick = () => {
  if (props.expandable) {
    emit('click')
  }
}

// 暴露方法给父组件
defineExpose({
  getChart: () => chartRef.value
})
</script>

<style scoped>
.chart-card {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.expand-icon {
  color: #909399;
  font-size: 16px;
  transition: color 0.3s ease;
}

.chart-card:hover .expand-icon {
  color: #409eff;
}

.chart-container {
  position: relative;
  width: 100%;
}

.chart-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.chart-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.chart-content {
  width: 100%;
  height: 100%;
}

/* 紧凑型设计 */
:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
