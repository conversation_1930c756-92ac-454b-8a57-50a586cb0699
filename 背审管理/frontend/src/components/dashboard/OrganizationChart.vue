<template>
  <BaseChart
    title="各单位人员统计"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '25%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['应急局', '环保局', '水利局', '交通局', '住建局', '教育局'],
    axisLabel: {
      fontSize: 10,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 11
    }
  },
  series: [
    {
      name: '人员数量',
      type: 'bar',
      data: [
        { value: 156, itemStyle: { color: '#FF6B6B' } },
        { value: 134, itemStyle: { color: '#4ECDC4' } },
        { value: 98, itemStyle: { color: '#45B7D1' } },
        { value: 187, itemStyle: { color: '#96CEB4' } },
        { value: 145, itemStyle: { color: '#FFEAA7' } },
        { value: 123, itemStyle: { color: '#DDA0DD' } }
      ],
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 11
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开单位统计详情')
}

onMounted(() => {
  fetchData()
})
</script>
