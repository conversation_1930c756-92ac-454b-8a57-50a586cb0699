<template>
  <BaseChart
    title="人员类型分布"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['专职保卫', '保安人员'],
    axisLabel: {
      fontSize: 11,
      interval: 0,
      rotate: 0
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 11
    }
  },
  series: [
    {
      name: '人员数量',
      type: 'bar',
      data: [
        {
          value: 856,
          itemStyle: { color: '#409EFF' }
        },
        {
          value: 392,
          itemStyle: { color: '#67C23A' }
        }
      ],
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 12,
        fontWeight: 'bold'
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开人员类型分布详情')
}

onMounted(() => {
  fetchData()
})
</script>
