<template>
  <BaseChart
    title="背景审查结果"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '审查结果',
      type: 'pie',
      radius: '65%',
      center: ['50%', '45%'],
      data: [
        { 
          value: 1089, 
          name: '正常',
          itemStyle: { color: '#67C23A' }
        },
        { 
          value: 127, 
          name: '异常',
          itemStyle: { color: '#F56C6C' }
        },
        { 
          value: 32, 
          name: '未审查',
          itemStyle: { color: '#E6A23C' }
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        fontSize: 12,
        formatter: '{b}\n{c}人'
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开背景审查结果详情')
}

onMounted(() => {
  fetchData()
})
</script>
