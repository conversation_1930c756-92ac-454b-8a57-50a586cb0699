<template>
  <BaseChart
    title="人员总览"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')
const chartData = ref({
  total: 1248,
  active: 1156,
  inactive: 92,
  security: 856,
  guard: 392
})

const chartOptions = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '人员状态',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        {
          value: 1156,
          name: '在职人员',
          itemStyle: { color: '#67C23A' }
        },
        {
          value: 92,
          name: '离职人员',
          itemStyle: { color: '#F56C6C' }
        }
      ]
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 200))

    // 数据已经在chartOptions中设置好了，这里不需要再次更新
    console.log('人员总览数据加载完成')
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  // 触发展开详情事件
  console.log('展开人员总览详情')
}

onMounted(() => {
  fetchData()
})
</script>
