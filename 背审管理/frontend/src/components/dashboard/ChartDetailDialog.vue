<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="80%"
    :before-close="handleClose"
    class="chart-detail-dialog"
  >
    <div class="dialog-content">
      <div class="chart-container" :style="{ height: '500px' }">
        <div v-if="loading" class="loading-state">
          <el-skeleton :rows="8" animated />
        </div>
        
        <div v-else-if="error" class="error-state">
          <el-result icon="error" title="加载失败" :sub-title="error">
            <template #extra>
              <el-button type="primary" @click="$emit('retry')">重试</el-button>
            </template>
          </el-result>
        </div>
        
        <div v-else ref="chartRef" class="chart-content"></div>
      </div>
      
      <!-- 数据表格 -->
      <div v-if="tableData && tableData.length > 0" class="data-table">
        <h4>详细数据</h4>
        <el-table :data="tableData" stripe style="width: 100%">
          <el-table-column
            v-for="column in tableColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
          />
        </el-table>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="exportChart">导出图表</el-button>
        <el-button @click="exportData">导出数据</el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

interface Props {
  modelValue: boolean
  title: string
  options?: any
  loading?: boolean
  error?: string
  tableData?: any[]
  tableColumns?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '',
  loading: false,
  error: '',
  options: () => ({}),
  tableData: () => [],
  tableColumns: () => []
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'retry': []
}>()

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const dialogVisible = ref(false)

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

const updateChart = () => {
  if (!chartInstance || !props.options) return
  
  // 为详情页面优化图表配置
  const detailOptions = {
    ...props.options,
    animation: true,
    animationDuration: 1000,
    grid: {
      ...props.options.grid,
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '10%'
    }
  }
  
  chartInstance.setOption(detailOptions, true)
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const handleClose = () => {
  dialogVisible.value = false
  emit('update:modelValue', false)
}

const exportChart = () => {
  if (!chartInstance) return
  
  const url = chartInstance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  })
  
  const link = document.createElement('a')
  link.download = `${props.title}.png`
  link.href = url
  link.click()
  
  ElMessage.success('图表导出成功')
}

const exportData = () => {
  if (!props.tableData || props.tableData.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }
  
  // 简单的CSV导出
  const headers = props.tableColumns.map(col => col.label).join(',')
  const rows = props.tableData.map(row => 
    props.tableColumns.map(col => row[col.prop]).join(',')
  ).join('\n')
  
  const csvContent = `${headers}\n${rows}`
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `${props.title}_数据.csv`
  link.click()
  
  ElMessage.success('数据导出成功')
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue) {
    nextTick(() => {
      if (!props.loading && !props.error) {
        initChart()
      }
    })
  }
})

watch(() => props.options, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chart-detail-dialog {
  --el-dialog-padding-primary: 20px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-container {
  position: relative;
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
}

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.chart-content {
  width: 100%;
  height: 100%;
}

.data-table {
  margin-top: 20px;
}

.data-table h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
