<template>
  <BaseChart
    title="各行业人员分布"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '行业分布',
      type: 'pie',
      radius: ['30%', '70%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outside',
        fontSize: 11,
        formatter: '{b}\n{c}人'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '12',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: true
      },
      data: [
        { 
          value: 245, 
          name: '政府机关',
          itemStyle: { color: '#5470C6' }
        },
        { 
          value: 198, 
          name: '教育系统',
          itemStyle: { color: '#91CC75' }
        },
        { 
          value: 167, 
          name: '医疗卫生',
          itemStyle: { color: '#FAC858' }
        },
        { 
          value: 234, 
          name: '交通运输',
          itemStyle: { color: '#EE6666' }
        },
        { 
          value: 189, 
          name: '金融保险',
          itemStyle: { color: '#73C0DE' }
        },
        { 
          value: 215, 
          name: '其他行业',
          itemStyle: { color: '#3BA272' }
        }
      ]
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    console.log('各行业人员分布数据加载完成')
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开各行业人员分布详情')
}

onMounted(() => {
  fetchData()
})
</script>
