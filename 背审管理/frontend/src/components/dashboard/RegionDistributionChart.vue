<template>
  <BaseChart
    title="各区域人员分布"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '20%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['莲池区', '竞秀区', '满城区', '清苑区', '徐水区', '涞水县'],
    axisLabel: {
      fontSize: 10,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 11
    }
  },
  series: [
    {
      name: '人员数量',
      type: 'bar',
      data: [
        { value: 245, itemStyle: { color: '#5470C6' } },
        { value: 198, itemStyle: { color: '#91CC75' } },
        { value: 167, itemStyle: { color: '#FAC858' } },
        { value: 234, itemStyle: { color: '#EE6666' } },
        { value: 189, itemStyle: { color: '#73C0DE' } },
        { value: 215, itemStyle: { color: '#3BA272' } }
      ],
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 11
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开区域分布详情')
}

onMounted(() => {
  fetchData()
})
</script>
