<template>
  <BaseChart
    title="异常人员趋势"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  legend: {
    data: ['新增异常', '处理完成'],
    bottom: '5%',
    textStyle: {
      fontSize: 12
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLabel: {
        fontSize: 11
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      axisLabel: {
        fontSize: 11
      }
    }
  ],
  series: [
    {
      name: '新增异常',
      type: 'line',
      stack: 'Total',
      smooth: true,
      lineStyle: {
        width: 2
      },
      emphasis: {
        focus: 'series'
      },
      areaStyle: {
        opacity: 0.3
      },
      data: [12, 18, 15, 22, 19, 16],
      itemStyle: {
        color: '#F56C6C'
      }
    },
    {
      name: '处理完成',
      type: 'line',
      stack: 'Total',
      smooth: true,
      lineStyle: {
        width: 2
      },
      emphasis: {
        focus: 'series'
      },
      areaStyle: {
        opacity: 0.3
      },
      data: [8, 15, 12, 20, 17, 14],
      itemStyle: {
        color: '#67C23A'
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开异常趋势详情')
}

onMounted(() => {
  fetchData()
})
</script>
