<template>
  <BaseChart
    title="重点关注人员统计"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '关注级别',
      type: 'pie',
      radius: '65%',
      center: ['50%', '45%'],
      data: [
        { 
          value: 15, 
          name: '一般关注',
          itemStyle: { color: '#409EFF' }
        },
        { 
          value: 18, 
          name: '重点关注',
          itemStyle: { color: '#E6A23C' }
        },
        { 
          value: 5, 
          name: '特别关注',
          itemStyle: { color: '#F56C6C' }
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        fontSize: 12,
        formatter: '{b}\n{c}人'
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开重点关注人员统计详情')
}

onMounted(() => {
  fetchData()
})
</script>
