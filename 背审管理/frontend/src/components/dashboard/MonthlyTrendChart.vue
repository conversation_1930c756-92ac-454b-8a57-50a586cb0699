<template>
  <BaseChart
    title="月度新增人员趋势"
    :loading="loading"
    :error="error"
    :options="chartOptions"
    @click="handleExpand"
    @retry="fetchData"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseChart from './BaseChart.vue'

const loading = ref(false)
const error = ref('')

const chartOptions = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
    formatter: function (params: any) {
      let result = `${params[0].name}<br/>`
      params.forEach((param: any) => {
        if (param.seriesName === '同比增长率') {
          result += `${param.seriesName}: ${param.value}%<br/>`
        } else {
          result += `${param.seriesName}: ${param.value}人<br/>`
        }
      })
      return result
    }
  },
  legend: {
    data: ['新增人员', '同比增长率'],
    bottom: '5%',
    textStyle: {
      fontSize: 12
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLabel: {
        fontSize: 11
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '人数',
      position: 'left',
      axisLabel: {
        fontSize: 11,
        formatter: '{value}人'
      }
    },
    {
      type: 'value',
      name: '同比增长率',
      position: 'right',
      axisLabel: {
        fontSize: 11,
        formatter: '{value}%'
      }
    }
  ],
  series: [
    {
      name: '新增人员',
      type: 'bar',
      data: [68, 80, 57, 101, 64, 87],
      itemStyle: {
        color: '#409EFF'
      },
      barWidth: '50%'
    },
    {
      name: '同比增长率',
      type: 'line',
      yAxisIndex: 1,
      smooth: true,
      lineStyle: {
        width: 3
      },
      emphasis: {
        focus: 'series'
      },
      data: [5.2, 8.7, -2.3, 12.5, 3.8, 9.1],
      itemStyle: {
        color: '#67C23A'
      }
    }
  ]
})

const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在chartOptions中定义
  } catch (err) {
    error.value = '数据加载失败'
  } finally {
    loading.value = false
  }
}

const handleExpand = () => {
  console.log('展开月度新增人员趋势详情')
}

onMounted(() => {
  fetchData()
})
</script>
