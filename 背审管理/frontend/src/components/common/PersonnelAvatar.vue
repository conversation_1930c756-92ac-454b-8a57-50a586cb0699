<!--
  人员头像组件
  用于显示人员照片，支持默认头像、错误处理和点击放大
-->
<template>
  <div class="personnel-avatar">
    <el-avatar
      :size="size"
      :src="avatarSrc"
      :alt="name"
      shape="square"
      class="clickable-avatar"
      @error="handleError"
      @click="handleClick"
    >
      <el-icon v-if="showDefaultIcon">
        <User />
      </el-icon>
      <span v-else-if="name" class="avatar-text">
        {{ getNameInitial(name) }}
      </span>
    </el-avatar>

    <!-- 头像放大预览 -->
    <el-image-viewer
      v-if="showViewer"
      :url-list="[avatarSrc || defaultAvatarUrl]"
      @close="showViewer = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { User } from '@element-plus/icons-vue'

interface Props {
  src?: string
  name?: string
  size?: number | string
  personnelId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  size: 55,
  name: '',
  src: ''
})

const imageError = ref(false)
const showDefaultIcon = ref(false)
const showViewer = ref(false)

// 计算头像源地址
const avatarSrc = computed(() => {
  if (imageError.value) return ''

  // 如果有提供src，直接使用
  if (props.src) return props.src

  // 如果有personnelId，生成默认头像URL
  if (props.personnelId) {
    return `/api/personnel/${props.personnelId}/avatar`
  }

  return ''
})

// 默认头像URL（用于预览）
const defaultAvatarUrl = computed(() => {
  return props.src || `https://api.dicebear.com/7.x/avataaars/svg?seed=${props.personnelId || 'default'}`
})

// 获取姓名首字母
const getNameInitial = (name: string): string => {
  if (!name) return ''
  
  // 如果是中文名，取最后一个字符（通常是名）
  if (/[\u4e00-\u9fa5]/.test(name)) {
    return name.slice(-1)
  }
  
  // 如果是英文名，取首字母
  return name.charAt(0).toUpperCase()
}

// 处理图片加载错误
const handleError = () => {
  imageError.value = true

  // 如果没有姓名，显示默认图标
  if (!props.name) {
    showDefaultIcon.value = true
  }
}

// 处理头像点击
const handleClick = () => {
  if (avatarSrc.value || defaultAvatarUrl.value) {
    showViewer.value = true
  }
}
</script>

<style scoped>
.personnel-avatar {
  display: inline-block;
}

.avatar-text {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
}

:deep(.el-avatar) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.clickable-avatar {
  cursor: pointer;
}

.clickable-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 不同尺寸的字体调整 */
:deep(.el-avatar--large .avatar-text) {
  font-size: 18px;
}

:deep(.el-avatar--small .avatar-text) {
  font-size: 12px;
}
</style>
