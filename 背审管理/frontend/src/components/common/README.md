# SearchForm 通用搜索组件

## 概述

SearchForm 是一个高度可配置的通用搜索表单组件，支持通过 JSON 配置来渲染不同类型的表单项，包括输入框、下拉选择、日期选择器等多种组件类型。

## 特性

- 🎯 **高度可配置**: 通过 JSON 配置即可生成复杂的搜索表单
- 🎨 **样式统一**: 内置美观的样式，支持响应式设计
- 🔧 **类型丰富**: 支持多种表单控件类型
- 🎪 **条件显示**: 支持根据其他字段值动态显示/隐藏字段
- 📱 **响应式**: 自适应不同屏幕尺寸
- 🚀 **易于扩展**: 可轻松添加新的字段类型

## 支持的字段类型

| 类型 | 说明 | 示例 |
|------|------|------|
| `input` | 文本输入框 | 姓名、身份证号 |
| `select` | 单选下拉框 | 区域、状态 |
| `multiSelect` | 多选下拉框 | 行业类型、异常类型 |
| `date` | 日期选择器 | 生日 |
| `daterange` | 日期范围选择器 | 入职时间范围 |
| `number` | 数字输入框 | 年龄、金额 |
| `switch` | 开关 | 启用/禁用 |
| `radio` | 单选按钮组 | 性别 |
| `checkbox` | 复选框组 | 兴趣爱好 |

## 基本用法

```vue
<template>
  <SearchForm
    v-model="searchForm"
    :form-config="searchFormConfig"
    :loading="loading"
    @search="handleSearch"
    @reset="handleReset"
    @field-change="handleFieldChange"
  >
    <template #extra-actions>
      <el-button type="primary" @click="handleExport">
        导出数据
      </el-button>
    </template>
  </SearchForm>
</template>

<script setup>
import { ref, reactive } from 'vue'
import SearchForm from '@/components/common/SearchForm.vue'

// 搜索表单数据
const searchForm = reactive({
  name: '',
  status: '',
  dateRange: []
})

// 搜索表单配置
const searchFormConfig = [
  // 第一行
  [
    {
      key: 'name',
      label: '姓名',
      type: 'input',
      placeholder: '请输入姓名',
      span: 8
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      span: 8,
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    }
  ],
  // 第二行
  [
    {
      key: 'dateRange',
      label: '日期范围',
      type: 'daterange',
      span: 8
    }
  ]
]

const handleSearch = () => {
  console.log('搜索:', searchForm)
}

const handleReset = () => {
  console.log('重置')
}

const handleFieldChange = (field, value, formData) => {
  console.log('字段变化:', field.key, value)
}
</script>
```

## 字段配置详解

### 基础配置

```typescript
interface FieldConfig {
  key: string          // 字段键名，对应表单数据的属性
  label: string        // 字段标签
  type: string         // 字段类型
  placeholder?: string // 占位符文本
  span?: number        // 栅格占位格数（默认8）
  clearable?: boolean  // 是否可清空（默认true）
  disabled?: boolean   // 是否禁用
  hidden?: boolean     // 是否隐藏
}
```

### 选择器配置

```typescript
{
  key: 'industry',
  label: '行业',
  type: 'multiSelect',
  options: [
    { label: '互联网', value: 'internet' },
    { label: '金融', value: 'finance' }
  ]
}
```

### 日期选择器配置

```typescript
{
  key: 'entryDate',
  label: '入职时间',
  type: 'daterange',
  format: 'YYYY-MM-DD',
  valueFormat: 'YYYY-MM-DD',
  shortcuts: [
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    }
  ]
}
```

### 条件显示配置

```typescript
{
  key: 'abnormalTypes',
  label: '异常类型',
  type: 'multiSelect',
  showWhen: {
    field: 'backgroundCheckResult',
    value: '2'  // 当背景审查结果为异常时才显示
  },
  options: [...]
}
```

### 字段变化回调

```typescript
{
  key: 'backgroundCheckResult',
  label: '背审结果',
  type: 'select',
  onChange: (value, formData) => {
    // 当选择非异常时，清空异常类型
    if (value !== '2') {
      formData.abnormalTypes = []
    }
  },
  options: [...]
}
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `Record<string, any>` | - | 表单数据（v-model） |
| `formConfig` | `FieldConfig[][]` | - | 表单配置，二维数组，每个子数组代表一行 |
| `title` | `string` | '搜索筛选' | 表单标题 |
| `loading` | `boolean` | `false` | 搜索按钮加载状态 |
| `labelWidth` | `string` | '100px' | 标签宽度 |
| `gutter` | `number` | `20` | 栅格间隔 |
| `defaultSpan` | `number` | `8` | 默认栅格占位 |
| `showSearchButton` | `boolean` | `true` | 是否显示搜索按钮 |
| `showResetButton` | `boolean` | `true` | 是否显示重置按钮 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `search` | `formData: Record<string, any>` | 点击搜索按钮时触发 |
| `reset` | - | 点击重置按钮时触发 |
| `field-change` | `field: FieldConfig, value: any, formData: Record<string, any>` | 字段值变化时触发 |

## Slots

| 插槽名 | 说明 |
|--------|------|
| `extra-actions` | 额外的操作按钮，显示在搜索和重置按钮后面 |

## 样式定制

组件内置了完整的样式，支持响应式设计。如需定制样式，可以通过以下方式：

```vue
<style>
/* 覆盖组件样式 */
:deep(.search-card) {
  /* 自定义样式 */
}
</style>
```

## 最佳实践

1. **配置分离**: 将表单配置抽取到单独的配置文件中，便于维护
2. **类型安全**: 使用 TypeScript 定义配置接口，确保类型安全
3. **响应式数据**: 使用 `reactive` 或 `ref` 管理表单数据
4. **条件显示**: 合理使用 `showWhen` 配置，提升用户体验
5. **字段验证**: 在 `field-change` 事件中添加必要的字段验证逻辑

## 扩展新字段类型

如需添加新的字段类型，可以在 SearchForm.vue 的模板中添加对应的条件分支：

```vue
<!-- 自定义字段类型 -->
<custom-component
  v-else-if="field.type === 'custom'"
  v-model="formData[field.key]"
  v-bind="field.customProps"
/>
```