<template>
  <div class="placeholder-page">
    <div class="placeholder-content">
      <div class="placeholder-icon">
        <el-icon size="80">
          <Tools />
        </el-icon>
      </div>
      <h2 class="placeholder-title">{{ title || '功能开发中' }}</h2>
      <p class="placeholder-description">
        {{ description || '...' }}
      </p>
      <div class="placeholder-actions" v-if="showActions">
        <el-button type="primary" @click="handleGoBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上一页
        </el-button>
        <el-button @click="handleGoHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tools, Clock, ArrowLeft, HomeFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

interface Props {
  title?: string
  description?: string
  expectedTime?: string
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '功能开发中',
  description: '该功能正在紧张开发中，敬请期待...',
  expectedTime: '待定',
  showActions: true
})

const router = useRouter()

const handleGoBack = () => {
  router.go(-1)
}

const handleGoHome = () => {
  router.push('/dashboard')
}
</script>

<style scoped>
.placeholder-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.placeholder-content {
  text-align: center;
  max-width: 500px;
}

.placeholder-icon {
  margin-bottom: 24px;
  color: #909399;
}

.placeholder-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.placeholder-description {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.placeholder-info {
  margin-bottom: 32px;
}

.placeholder-info .el-tag {
  padding: 12px 20px;
  font-size: 14px;
}

.placeholder-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.placeholder-actions .el-button {
  padding: 12px 24px;
}
</style>
