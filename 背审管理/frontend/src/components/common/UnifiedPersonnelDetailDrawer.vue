<!-- 
  统一人员详情抽屉组件
  支持背审人员和异常人员两种模式
-->
<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    :size="600"
    direction="rtl"
    :before-close="handleClose"
  >
    <div v-if="personnelData" class="drawer-content">
      <!-- 人员基本信息头部 -->
      <div class="personnel-header">
        <div class="avatar-section">
          <PersonnelAvatar
            :src="personnelData.avatar"
            :name="personnelData.name"
            :size="60"
          />
        </div>
        <div class="basic-info">
          <h3 class="personnel-name">{{ personnelData.name }}</h3>
          <div class="personnel-meta">
            <!-- 人员类型标签 -->
            <el-tag 
              v-if="mode === 'background-check'"
              :type="getPersonnelTypeColor(personnelData.personnelType)" 
              size="small"
            >
              {{ getPersonnelTypeText(personnelData.personnelType) }}
            </el-tag>
            
            <!-- 异常等级标签 -->
            <el-tag 
              v-if="mode === 'result-processing'"
              :type="getAbnormalLevelColor(personnelData.abnormalLevel)" 
              size="small"
            >
              {{ getAbnormalLevelText(personnelData.abnormalLevel) }}等级异常
            </el-tag>
            
            <!-- 当前状态标签（醒目显示） -->
            <el-tag 
              :type="getCurrentStatusColor()" 
              size="large"
              class="status-tag-prominent"
              effect="dark"
            >
              {{ getCurrentStatusText() }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- Tab 1: 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <div class="info-section">
            <div class="info-grid">
              <div class="info-item">
                <label>姓名</label>
                <span>{{ personnelData.name }}</span>
              </div>
              <div class="info-item">
                <label>身份证号</label>
                <span>{{ maskIdCard(personnelData.idCard) }}</span>
              </div>
              <div class="info-item">
                <label>联系方式</label>
                <span>{{ maskPhone(personnelData.phone) }}</span>
              </div>
              
              <!-- 背审模式的基本信息 -->
              <template v-if="mode === 'background-check'">
                <div class="info-item">
                  <label>人员类型</label>
                  <span>{{ getPersonnelTypeText(personnelData.personnelType) }}</span>
                </div>
                <div class="info-item">
                  <label>所属部门</label>
                  <span>{{ personnelData.department }}</span>
                </div>
                <div class="info-item">
                  <label>职位</label>
                  <span>{{ personnelData.position }}</span>
                </div>
                <div class="info-item">
                  <label>入职时间</label>
                  <span>{{ personnelData.entryDate }}</span>
                </div>
                <div class="info-item">
                  <label>风险等级</label>
                  <el-tag :type="getRiskLevelColor(personnelData.riskLevel)" size="small">
                    {{ getRiskLevelText(personnelData.riskLevel) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>背审次数</label>
                  <span>{{ personnelData.checkCount || 0 }}次</span>
                </div>
              </template>
              
              <!-- 结果处理模式的基本信息 -->
              <template v-if="mode === 'result-processing'">
                <div class="info-item">
                  <label>所属机构</label>
                  <span>{{ personnelData.organization }}</span>
                </div>
                <div class="info-item">
                  <label>异常类型</label>
                  <el-tag :type="getAbnormalTypeColor(personnelData.abnormalType)" size="small">
                    {{ getAbnormalTypeText(personnelData.abnormalType) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>异常等级</label>
                  <el-tag :type="getAbnormalLevelColor(personnelData.abnormalLevel)" size="small">
                    {{ getAbnormalLevelText(personnelData.abnormalLevel) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>发现时间</label>
                  <span>{{ personnelData.discoveredAt }}</span>
                </div>
                <div class="info-item">
                  <label>背审任务</label>
                  <span>{{ personnelData.backgroundCheckTaskNo }}</span>
                </div>
                <div class="info-item full-width">
                  <label>异常描述</label>
                  <span>{{ personnelData.abnormalDescription }}</span>
                </div>
              </template>
            </div>
          </div>
        </el-tab-pane>

        <!-- Tab 2: 当前任务 -->
        <el-tab-pane :label="currentTaskTabLabel" name="current-task">
          <div class="info-section">
            <!-- 背审模式的当前任务 -->
            <template v-if="mode === 'background-check'">
              <div v-if="personnelData.backgroundCheckStatus === 'in_progress'" class="current-task">
                <h4>当前背审任务</h4>
                <div class="task-info">
                  <div class="info-item">
                    <label>任务编号</label>
                    <span>{{ personnelData.currentTaskId || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <label>任务状态</label>
                    <el-tag type="primary" size="small">背审中</el-tag>
                  </div>
                  <div class="info-item">
                    <label>处理部门</label>
                    <span>莲池分局</span>
                  </div>
                  <div class="info-item">
                    <label>发起时间</label>
                    <span>2024-01-15 09:00</span>
                  </div>
                  <div class="info-item">
                    <label>截止时间</label>
                    <span>2024-02-01 18:00</span>
                  </div>
                </div>
              </div>
              <div v-else class="status-info">
                <div class="info-item">
                  <label>当前状态</label>
                  <el-tag :type="getBackgroundCheckStatusColor(personnelData.backgroundCheckStatus)" size="small">
                    {{ getBackgroundCheckStatusText(personnelData.backgroundCheckStatus) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>最后背审时间</label>
                  <span>{{ personnelData.lastCheckDate || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>下次背审时间</label>
                  <span>{{ personnelData.nextCheckDate || '-' }}</span>
                </div>
              </div>
            </template>

            <!-- 结果处理模式的当前任务 -->
            <template v-if="mode === 'result-processing'">
              <div v-if="personnelData.processingStatus === 'processing'" class="current-task">
                <h4>当前处理任务</h4>
                <div class="task-info">
                  <div class="info-item">
                    <label>任务编号</label>
                    <span>{{ personnelData.processingTaskId || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <label>处理状态</label>
                    <el-tag type="primary" size="small">处理中</el-tag>
                  </div>
                  <div class="info-item">
                    <label>处理人</label>
                    <span>{{ personnelData.assignedToName || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <label>分配时间</label>
                    <span>{{ personnelData.assignedAt || '-' }}</span>
                  </div>
                </div>
              </div>
              <div v-else-if="personnelData.processingStatus === 'completed'" class="completed-task">
                <h4>处理结果</h4>
                <div class="task-info">
                  <div class="info-item">
                    <label>处理状态</label>
                    <el-tag type="success" size="small">已完成</el-tag>
                  </div>
                  <div class="info-item">
                    <label>处理人</label>
                    <span>{{ personnelData.assignedToName || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <label>完成时间</label>
                    <span>{{ personnelData.processedAt || '-' }}</span>
                  </div>
                  <div class="info-item full-width" v-if="personnelData.processingResult">
                    <label>处理结果</label>
                    <span>{{ personnelData.processingResult }}</span>
                  </div>
                  <div class="info-item full-width" v-if="personnelData.processingNote">
                    <label>处理说明</label>
                    <span>{{ personnelData.processingNote }}</span>
                  </div>
                </div>
              </div>
              <div v-else class="pending-task">
                <h4>待处理状态</h4>
                <div class="task-info">
                  <div class="info-item">
                    <label>当前状态</label>
                    <el-tag type="info" size="small">待处理</el-tag>
                  </div>
                  <div class="info-item">
                    <label>发现时间</label>
                    <span>{{ personnelData.discoveredAt }}</span>
                  </div>
                  <div class="info-item">
                    <label>背审结果</label>
                    <span>{{ personnelData.backgroundCheckResult }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </el-tab-pane>

        <!-- Tab 3: 背审历史 -->
        <el-tab-pane label="背审历史" name="background-history">
          <div class="info-section">
            <el-timeline>
              <el-timeline-item
                v-for="(record, index) in backgroundHistory"
                :key="index"
                :timestamp="record.time"
                placement="top"
                :type="getHistoryType(record.type)"
              >
                <div class="history-item">
                  <div class="history-content">{{ record.content }}</div>
                  <div class="history-operator">操作人：{{ record.operator }}</div>
                  <div v-if="record.result" class="history-result">
                    <el-tag :type="record.resultType" size="small">
                      {{ record.result }}
                    </el-tag>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>

        <!-- Tab 4: 处理历史 -->
        <el-tab-pane label="处理历史" name="processing-history">
          <div class="info-section">
            <el-timeline>
              <el-timeline-item
                v-for="(record, index) in processingHistory"
                :key="index"
                :timestamp="record.time"
                placement="top"
                :type="getHistoryType(record.type)"
              >
                <div class="history-item">
                  <div class="history-content">{{ record.content }}</div>
                  <div class="history-operator">操作人：{{ record.operator }}</div>
                  <div v-if="record.note" class="history-note">{{ record.note }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import PersonnelAvatar from '@/components/common/PersonnelAvatar.vue'
import {
  personnelMockData,
  getPersonnelTypeText,
  type PersonnelData
} from '@/data/personnelMockData'
import {
  abnormalPersonnelMockData,
  type AbnormalPersonnel
} from '@/data/resultProcessingMockData'
import {
  abnormalTypeConfig,
  abnormalLevelConfig,
  processingStatusConfig
} from '@/config/resultProcessingConfig'

// Props
interface Props {
  modelValue: boolean
  personnelId?: string
  mode: 'background-check' | 'result-processing'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  personnelId: undefined,
  mode: 'background-check'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'view-task': [taskId: string]
}>()

// 响应式数据
const activeTab = ref('basic')
const personnelData = ref<PersonnelData | AbnormalPersonnel | null>(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const drawerTitle = computed(() => {
  if (props.mode === 'background-check') {
    return '人员背审详情'
  } else {
    return '异常人员详情'
  }
})

const currentTaskTabLabel = computed(() => {
  if (props.mode === 'background-check') {
    return '当前背审'
  } else {
    return '当前处理'
  }
})

// 模拟背审历史数据
const backgroundHistory = computed(() => {
  if (!personnelData.value) return []

  const history = []

  if (props.mode === 'background-check') {
    // 背审人员的背审历史
    history.push({
      time: '2024-01-15 09:00:00',
      type: 'start',
      content: '发起背景调查',
      operator: '张管理员',
      result: '进行中',
      resultType: 'primary'
    })

    if ((personnelData.value as PersonnelData).backgroundCheckStatus === 'completed') {
      history.push({
        time: '2024-01-20 15:30:00',
        type: 'complete',
        content: '背景调查完成',
        operator: '李调查员',
        result: '通过',
        resultType: 'success'
      })
    }
  } else {
    // 异常人员的背审历史
    const abnormalData = personnelData.value as AbnormalPersonnel
    history.push({
      time: abnormalData.discoveredAt,
      type: 'start',
      content: `背景调查任务：${abnormalData.backgroundCheckTaskNo}`,
      operator: '系统自动',
      result: '进行中',
      resultType: 'primary'
    })

    history.push({
      time: abnormalData.discoveredAt,
      type: 'discover',
      content: `发现异常：${abnormalData.abnormalDescription}`,
      operator: '系统检测',
      result: abnormalData.backgroundCheckResult,
      resultType: 'danger'
    })
  }

  return history.reverse()
})

// 模拟处理历史数据
const processingHistory = computed(() => {
  if (!personnelData.value || props.mode === 'background-check') return []

  const abnormalData = personnelData.value as AbnormalPersonnel
  const history = []

  if (abnormalData.assignedAt) {
    history.push({
      time: abnormalData.assignedAt,
      type: 'assign',
      content: `任务已分配给 ${abnormalData.assignedToName}`,
      operator: '张管理员'
    })
  }

  if (abnormalData.processedAt) {
    history.push({
      time: abnormalData.processedAt,
      type: 'complete',
      content: `处理完成：${abnormalData.processingResult}`,
      operator: abnormalData.assignedToName || '处理员',
      note: abnormalData.processingNote
    })
  }

  return history.reverse()
})

// 工具函数
const maskIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const maskPhone = (phone: string) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 获取当前状态的颜色和文本
const getCurrentStatusColor = () => {
  if (props.mode === 'background-check') {
    const data = personnelData.value as PersonnelData
    if (!data) return 'info'

    switch (data.backgroundCheckStatus) {
      case 'in_progress': return 'primary'
      case 'completed': return 'success'
      case 'pending': return 'warning'
      default: return 'info'
    }
  } else {
    const data = personnelData.value as AbnormalPersonnel
    if (!data) return 'info'

    switch (data.processingStatus) {
      case 'pending': return 'warning'
      case 'processing': return 'primary'
      case 'completed': return 'success'
      default: return 'info'
    }
  }
}

const getCurrentStatusText = () => {
  if (props.mode === 'background-check') {
    const data = personnelData.value as PersonnelData
    if (!data) return ''

    switch (data.backgroundCheckStatus) {
      case 'in_progress': return '背审中'
      case 'completed': return '背审完成'
      case 'pending': return '待背审'
      default: return data.backgroundCheckStatus
    }
  } else {
    const data = personnelData.value as AbnormalPersonnel
    if (!data) return ''

    // 如果是异常人员，需要综合判断状态
    if (data.processingStatus === 'pending') {
      return '待处理'
    } else if (data.processingStatus === 'processing') {
      return '处理中'
    } else if (data.processingStatus === 'completed') {
      return '已处理'
    } else {
      return data.processingStatus
    }
  }
}

// 背审人员相关函数
const getPersonnelTypeColor = (type: string) => {
  const colorMap = {
    'new_employee': 'success',
    'contractor': 'warning',
    'intern': 'info',
    'consultant': 'primary'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getRiskLevelColor = (level: string) => {
  const colorMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return colorMap[level as keyof typeof colorMap] || 'info'
}

const getRiskLevelText = (level: string) => {
  const textMap = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return textMap[level as keyof typeof textMap] || level
}

const getBackgroundCheckStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'warning',
    'in_progress': 'primary',
    'completed': 'success',
    'failed': 'danger'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getBackgroundCheckStatusText = (status: string) => {
  const textMap = {
    'pending': '待背审',
    'in_progress': '背审中',
    'completed': '背审完成',
    'failed': '背审失败'
  }
  return textMap[status as keyof typeof textMap] || status
}

// 异常人员相关函数
const getAbnormalTypeText = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.label || type
}

const getAbnormalTypeColor = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.color || 'info'
}

const getAbnormalLevelText = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.label || level
}

const getAbnormalLevelColor = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.color || 'info'
}

const getHistoryType = (type: string) => {
  const typeMap = {
    'start': 'primary',
    'assign': 'success',
    'complete': 'success',
    'discover': 'warning',
    'cancel': 'danger'
  }
  return typeMap[type as keyof typeof typeMap] || 'primary'
}

// 加载人员数据
const loadPersonnelData = () => {
  if (!props.personnelId) return

  if (props.mode === 'background-check') {
    const personnel = personnelMockData.find(p => p.id === parseInt(props.personnelId!))
    if (personnel) {
      personnelData.value = personnel
    }
  } else {
    const personnel = abnormalPersonnelMockData.find(p => p.id === props.personnelId)
    if (personnel) {
      personnelData.value = personnel
    }
  }
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  activeTab.value = 'basic'
}

// 监听personnelId变化
watch(() => props.personnelId, () => {
  if (props.modelValue && props.personnelId) {
    loadPersonnelData()
  }
}, { immediate: true })

// 监听抽屉打开
watch(() => props.modelValue, (visible) => {
  if (visible && props.personnelId) {
    loadPersonnelData()
  }
})

// 监听模式变化
watch(() => props.mode, () => {
  if (props.modelValue && props.personnelId) {
    loadPersonnelData()
  }
})
</script>

<style scoped>
.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.personnel-header {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.avatar-section {
  margin-right: 16px;
}

.basic-info {
  flex: 1;
}

.personnel-name {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.personnel-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

/* 醒目的状态标签 */
.status-tag-prominent {
  font-size: 14px !important;
  font-weight: 600 !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-left: 8px;
}

.detail-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow-y: auto;
}

.info-section {
  padding: 20px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

.current-task,
.completed-task,
.pending-task,
.status-info {
  margin-bottom: 24px;
}

.current-task h4,
.completed-task h4,
.pending-task h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.task-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.history-item {
  padding: 8px 0;
}

.history-content {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: #909399;
}

.history-note {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.history-result {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .personnel-header {
    flex-direction: column;
    text-align: center;
  }

  .avatar-section {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .personnel-meta {
    justify-content: center;
  }

  .info-grid,
  .task-info {
    grid-template-columns: 1fr;
  }

  .info-item.full-width {
    grid-column: 1;
  }
}

/* 时间线样式优化 */
:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 20px;
}

:deep(.el-timeline-item__node) {
  width: 8px;
  height: 8px;
}

/* Tab样式优化 */
:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-tabs__item) {
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}
</style>
