<template>
  <div class="role-switcher">
    <!-- 角色切换下拉菜单 -->
    <el-dropdown @command="handleRoleSwitch" trigger="click" placement="bottom-end">
      <el-button type="primary" size="small" :loading="loading">
        <el-icon><User /></el-icon>
        {{ currentRoleText }}
        <el-icon class="el-icon--right"><ArrowDown /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item 
            command="admin" 
            :disabled="currentRole === 'admin'"
          >
            <el-icon><UserFilled /></el-icon>
            <span style="margin-left: 8px;">管理员模式</span>
          </el-dropdown-item>
          <el-dropdown-item 
            command="unit" 
            :disabled="currentRole === 'unit'"
          >
            <el-icon><OfficeBuilding /></el-icon>
            <span style="margin-left: 8px;">下级单位模式</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 当前用户信息 -->
    <div class="user-info" v-if="user">
      <el-tag :type="isAdmin ? 'danger' : 'success'" size="small">
        {{ user.name }}
      </el-tag>
      <el-tag type="info" size="small" style="margin-left: 8px;">
        {{ user.organizationName }}
      </el-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { User, UserFilled, OfficeBuilding, ArrowDown } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/authStore'
import type { UserRole } from '@/types/auth'

const authStore = useAuthStore()

// 计算属性
const currentRole = computed(() => authStore.currentRole)
const user = computed(() => authStore.user)
const isAdmin = computed(() => authStore.isAdmin)
const loading = computed(() => authStore.loading)

const currentRoleText = computed(() => {
  return currentRole.value === 'admin' ? '管理员' : '下级单位'
})

// 方法
const handleRoleSwitch = async (role: UserRole) => {
  if (role === currentRole.value) {
    return
  }

  try {
    await authStore.switchRole(role)
    ElMessage.success(`已切换到${role === 'admin' ? '管理员' : '下级单位'}模式`)
    
    // 延迟刷新页面以应用新的权限设置
    setTimeout(() => {
      window.location.reload()
    }, 500)
  } catch (error) {
    ElMessage.error('角色切换失败')
    console.error('角色切换失败:', error)
  }
}
</script>

<style scoped>
.role-switcher {
  display: flex;
  align-items: center;
  gap: 12px;
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.el-dropdown {
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-switcher {
    top: 10px;
    right: 10px;
    padding: 8px;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }
  
  .user-info {
    flex-direction: column;
    align-items: flex-end;
  }
}
</style>
