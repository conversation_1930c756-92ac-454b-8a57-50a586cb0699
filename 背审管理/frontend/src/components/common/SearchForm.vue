<template>
  <el-card class="search-card" shadow="never">
    <div class="search-header">
      <h3 class="search-title">
        <el-icon><Search /></el-icon>
        {{ title || '搜索筛选' }}
      </h3>
      <div class="search-actions">
        <el-button 
          type="primary" 
          @click="handleSearch" 
          :loading="loading" 
          class="action-btn"
          v-if="showSearchButton"
        >
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button 
          @click="handleReset" 
          class="action-btn"
          v-if="showResetButton"
        >
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <slot name="extra-actions"></slot>
      </div>
    </div>

    <div class="search-container">
      <el-form :model="formData" class="search-form" :label-width="labelWidth">
        <el-row 
          v-for="(row, rowIndex) in formConfig" 
          :key="rowIndex" 
          :gutter="gutter"
        >
          <el-col 
            v-for="(field, fieldIndex) in row" 
            :key="fieldIndex" 
            :span="field.span || defaultSpan"
            v-show="!field.hidden && checkFieldVisible(field)"
          >
            <el-form-item :label="field.label">
              <!-- 输入框 -->
              <el-input
                v-if="field.type === 'input'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                :clearable="field.clearable !== false"
                :disabled="field.disabled"
              />
              
              <!-- 单选下拉框 -->
              <el-select
                v-else-if="field.type === 'select'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                :clearable="field.clearable !== false"
                :disabled="field.disabled"
                style="width: 100%"
                @change="handleFieldChange(field, $event)"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              
              <!-- 多选下拉框 -->
              <el-select
                v-else-if="field.type === 'multiSelect'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                :clearable="field.clearable !== false"
                :disabled="field.disabled"
                multiple
                collapse-tags
                collapse-tags-tooltip
                style="width: 100%"
                @change="handleFieldChange(field, $event)"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              
              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="field.type === 'date'"
                v-model="formData[field.key]"
                type="date"
                :placeholder="field.placeholder"
                :clearable="field.clearable !== false"
                :disabled="field.disabled"
                style="width: 100%"
                :format="field.format || 'YYYY-MM-DD'"
                :value-format="field.valueFormat || 'YYYY-MM-DD'"
              />
              
              <!-- 日期范围选择器 -->
              <el-date-picker
                v-else-if="field.type === 'daterange'"
                v-model="formData[field.key]"
                type="daterange"
                :range-separator="field.rangeSeparator || '至'"
                :start-placeholder="field.startPlaceholder || '开始日期'"
                :end-placeholder="field.endPlaceholder || '结束日期'"
                :clearable="field.clearable !== false"
                :disabled="field.disabled"
                style="width: 100%"
                :format="field.format || 'YYYY-MM-DD'"
                :value-format="field.valueFormat || 'YYYY-MM-DD'"
                :shortcuts="field.shortcuts"
              />
              
              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="field.type === 'number'"
                v-model="formData[field.key]"
                :placeholder="field.placeholder"
                :disabled="field.disabled"
                :min="field.min"
                :max="field.max"
                :step="field.step"
                style="width: 100%"
              />
              
              <!-- 开关 -->
              <el-switch
                v-else-if="field.type === 'switch'"
                v-model="formData[field.key]"
                :disabled="field.disabled"
                :active-text="field.activeText"
                :inactive-text="field.inactiveText"
              />
              
              <!-- 单选按钮组 -->
              <el-radio-group
                v-else-if="field.type === 'radio'"
                v-model="formData[field.key]"
                :disabled="field.disabled"
              >
                <el-radio
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
              
              <!-- 复选框组 -->
              <el-checkbox-group
                v-else-if="field.type === 'checkbox'"
                v-model="formData[field.key]"
                :disabled="field.disabled"
              >
                <el-checkbox
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { FieldConfig, FieldOption } from '@/types/searchForm'

// Props
interface Props {
  title?: string
  formConfig: FieldConfig[][]
  modelValue: Record<string, any>
  loading?: boolean
  labelWidth?: string
  gutter?: number
  defaultSpan?: number
  showSearchButton?: boolean
  showResetButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '搜索筛选',
  loading: false,
  labelWidth: '100px',
  gutter: 20,
  defaultSpan: 8,
  showSearchButton: true,
  showResetButton: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  'search': [formData: Record<string, any>]
  'reset': []
  'field-change': [field: FieldConfig, value: any, formData: Record<string, any>]
}>()

// 表单数据
const formData = reactive({ ...props.modelValue })

// 监听表单数据变化
watch(
  () => formData,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true }
)

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(formData, newVal)
  },
  { deep: true }
)

// 检查字段是否可见
const checkFieldVisible = (field: FieldConfig): boolean => {
  if (!field.showWhen) return true
  
  const { field: dependentField, value: expectedValue } = field.showWhen
  const currentValue = formData[dependentField]
  
  if (Array.isArray(expectedValue)) {
    return expectedValue.includes(currentValue)
  }
  
  return currentValue === expectedValue
}

// 字段变化处理
const handleFieldChange = (field: FieldConfig, value: any) => {
  if (field.onChange) {
    field.onChange(value, formData)
  }
  emit('field-change', field, value, { ...formData })
}

// 搜索处理
const handleSearch = () => {
  emit('search', { ...formData })
}

// 重置处理
const handleReset = () => {
  // 重置表单数据
  const resetData: Record<string, any> = {}
  
  props.formConfig.flat().forEach(field => {
    if (field.type === 'multiSelect' || field.type === 'checkbox') {
      resetData[field.key] = []
    } else if (field.type === 'daterange') {
      resetData[field.key] = []
    } else if (field.type === 'switch') {
      resetData[field.key] = false
    } else {
      resetData[field.key] = ''
    }
  })
  
  Object.assign(formData, resetData)
  emit('reset')
}
</script>

<style scoped>
/* 搜索卡片样式 */
.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.search-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-title .el-icon {
  color: #409eff;
}

.search-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  min-width: 88px;
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
}

.search-container {
  width: 100%;
}

.search-form {
  width: 100%;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__body) {
  padding: 15px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #337ecc 0%, #5aa3e6 100%);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  :deep(.el-col) {
    width: 100% !important;
  }

  .search-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
  }
}

/* 动画效果 */
.search-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 异常类型选择器特殊样式 */
:deep(.el-select__tags) {
  max-width: calc(100% - 30px);
}
</style>