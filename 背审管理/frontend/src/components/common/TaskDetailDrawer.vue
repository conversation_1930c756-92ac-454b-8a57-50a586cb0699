<!-- 通用任务详情抽屉组件 -->
<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    size="60%"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab" class="detail-tabs">
      <!-- 任务信息 -->
      <el-tab-pane label="任务信息" name="info">
        <div class="task-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务编号">
              {{ taskDetail.taskNo }}
            </el-descriptions-item>
            <el-descriptions-item label="任务标题">
              {{ taskDetail.title }}
            </el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag :type="getStatusColor(taskDetail.status)" size="small">
                {{ getStatusText(taskDetail.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityColor(taskDetail.priority)" size="small">
                {{ getPriorityText(taskDetail.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="人员数量">
              {{ taskDetail.personnelCount }}人
            </el-descriptions-item>
            <el-descriptions-item label="处理人">
              {{ taskDetail.assignedToUserName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ taskDetail.createdAt || taskDetail.createTime }}
            </el-descriptions-item>
            <el-descriptions-item label="截止时间">
              <span :class="{ 'overdue-text': taskDetail.isOverdue }">
                {{ taskDetail.dueDate }}
                <span v-if="taskDetail.isOverdue" class="overdue-badge">
                  逾期{{ taskDetail.overdueBy }}天
                </span>
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="任务描述" :span="2">
              {{ taskDetail.description || '-' }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 进度信息 -->
          <div class="progress-section">
            <h4>任务进度</h4>
            <el-progress 
              :percentage="taskDetail.progress?.percentage || 0" 
              :stroke-width="12"
              :status="getProgressStatus(taskDetail)"
            />
            <div class="progress-detail">
              已完成：{{ taskDetail.progress?.completed || 0 }}人 / 
              总计：{{ taskDetail.progress?.total || 0 }}人
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 历史记录 -->
      <el-tab-pane :label="historyTabLabel" name="history">
        <div class="reminder-history">
          <div v-if="historyLoading" v-loading="true" style="height: 200px;"></div>
          <div v-else-if="historyList.length === 0" class="empty-state">
            <el-empty :description="`暂无${historyTabLabel}`" />
          </div>
          
          <!-- 背审任务：催办历史表格 -->
          <el-table v-if="taskType === 'background-check'" :data="historyList" :loading="historyLoading">
            <el-table-column prop="reminderTime" label="催办时间" width="180" />
            <el-table-column prop="reminderUser" label="催办人" width="120" />
            <el-table-column label="是否已读" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.isRead ? 'success' : 'warning'" size="small">
                  {{ scope.row.isRead ? '已读' : '未读' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="feedback" label="反馈内容" min-width="200">
              <template #default="scope">
                {{ scope.row.feedback || '-' }}
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 处理任务：处理历史时间线 -->
          <el-timeline v-else-if="taskType === 'result-processing'">
            <el-timeline-item
              v-for="(item, index) in historyList"
              :key="index"
              :timestamp="item.time"
              placement="top"
              :type="getHistoryType(item.type)"
            >
              <div class="history-content">
                <p><strong>{{ item.title }}</strong></p>
                <p>{{ item.content }}</p>
                <p v-if="item.operator" class="operator">操作人：{{ item.operator }}</p>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-tab-pane>

      <!-- 关联人员 -->
      <el-tab-pane label="关联人员" name="personnel">
        <div class="personnel-section">
          <!-- 人员搜索 -->
          <div class="personnel-search">
            <el-form :model="personnelSearchForm" inline>
              <el-form-item label="姓名">
                <el-input
                  v-model="personnelSearchForm.name"
                  placeholder="请输入姓名"
                  clearable
                  @keyup.enter="handlePersonnelSearch"
                />
              </el-form-item>
              <el-form-item label="身份证号">
                <el-input
                  v-model="personnelSearchForm.idCard"
                  placeholder="请输入身份证号"
                  clearable
                  @keyup.enter="handlePersonnelSearch"
                />
              </el-form-item>
              <el-form-item label="状态">
                <el-select
                  v-model="personnelSearchForm.status"
                  placeholder="请选择状态"
                  clearable
                  @change="handlePersonnelSearch"
                >
                  <el-option label="未完成" value="incomplete" />
                  <el-option label="已完成" value="completed" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handlePersonnelSearch">搜索</el-button>
                <el-button @click="handlePersonnelReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 人员列表 -->
          <el-table :data="personnelList" :loading="personnelLoading">
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="idCard" label="身份证号" width="180">
              <template #default="scope">
                {{ maskIdCard(scope.row.idCard) }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系方式" width="140" />
            <el-table-column prop="organization" label="所属机构" min-width="150" />
            
            <!-- 背审任务：显示背审状态 -->
            <el-table-column v-if="taskType === 'background-check'" prop="status" label="背审状态" width="120">
              <template #default="scope">
                <el-tag :type="getPersonnelStatusColor(scope.row.status)" size="small">
                  {{ getPersonnelStatusText(scope.row.status) }}
                </el-tag>
                <!-- 异常时显示异常类型 -->
                <div v-if="scope.row.status === 'abnormal' && scope.row.abnormalType" class="abnormal-type">
                  <el-tag :type="getAbnormalTypeColor(scope.row.abnormalType)" size="small" class="mt-1">
                    {{ getAbnormalTypeText(scope.row.abnormalType) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            
            <!-- 处理任务：显示异常类型和处理状态 -->
            <template v-else-if="taskType === 'result-processing'">
              <el-table-column prop="abnormalType" label="异常类型" width="120">
                <template #default="scope">
                  <el-tag :type="getAbnormalTypeColor(scope.row.abnormalType)" size="small">
                    {{ getAbnormalTypeText(scope.row.abnormalType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="processingStatus" label="处理状态" width="120">
                <template #default="scope">
                  <el-tag :type="getProcessingStatusColor(scope.row.processingStatus)" size="small">
                    {{ getProcessingStatusText(scope.row.processingStatus) }}
                  </el-tag>
                  <!-- 已处理时显示处理结果 -->
                  <div v-if="scope.row.processingStatus === 'completed' && scope.row.processingResult" class="processing-result">
                    <el-tag :type="getProcessingResultColor(scope.row.processingResult)" size="small" class="mt-1">
                      {{ getProcessingResultText(scope.row.processingResult) }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
            </template>
          </el-table>

          <!-- 人员分页 -->
          <div class="personnel-pagination">
            <el-pagination
              v-model:current-page="personnelPagination.page"
              v-model:page-size="personnelPagination.size"
              :page-sizes="[10, 20, 50]"
              :total="personnelPagination.total"
              layout="total, sizes, prev, pager, next"
              @size-change="handlePersonnelSizeChange"
              @current-change="handlePersonnelCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getTaskStatusText,
  getTaskStatusColor,
  getPriorityText,
  getPriorityColor,
  getProgressStatus,
  getHistoryType,
  getBackgroundCheckStatusText,
  getBackgroundCheckStatusColor,
  getAbnormalTypeText,
  getAbnormalTypeColor,
  getProcessingStatusText,
  getProcessingStatusColor,
  getProcessingResultText,
  getProcessingResultColor,
  maskIdCard,
  getTaskTypeConfig,
  getCompletionStatusOptions
} from '@/config/taskDetailConfig'

interface Props {
  modelValue: boolean
  taskId?: string
  taskType: 'background-check' | 'result-processing' // 任务类型
  taskDetail?: any
  historyList?: any[]
  personnelList?: any[]
  onFetchTaskDetail?: (taskId: string) => Promise<any>
  onFetchHistory?: (taskId: string) => Promise<any[]>
  onFetchPersonnel?: (taskId: string, searchForm: any, pagination: any) => Promise<{ list: any[], total: number }>
}

const props = withDefaults(defineProps<Props>(), {
  taskType: 'background-check',
  taskDetail: () => ({}),
  historyList: () => [],
  personnelList: () => []
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeTab = ref('info')
const historyLoading = ref(false)
const personnelLoading = ref(false)

// 获取任务类型配置
const taskTypeConfig = computed(() => getTaskTypeConfig(props.taskType))

// 计算属性
const drawerTitle = computed(() => taskTypeConfig.value.drawerTitle)
const historyTabLabel = computed(() => taskTypeConfig.value.historyTabLabel)

// 人员搜索表单
const personnelSearchForm = reactive({
  name: '',
  idCard: '',
  status: ''
})

// 人员分页
const personnelPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 工具函数
const getStatusText = getTaskStatusText
const getStatusColor = getTaskStatusColor
const getPersonnelStatusText = (status: string) => {
  if (props.taskType === 'background-check') {
    return getBackgroundCheckStatusText(status)
  } else {
    return getProcessingStatusText(status)
  }
}
const getPersonnelStatusColor = (status: string) => {
  if (props.taskType === 'background-check') {
    return getBackgroundCheckStatusColor(status)
  } else {
    return getProcessingStatusColor(status)
  }
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
}

const handlePersonnelSearch = () => {
  personnelPagination.page = 1
  // 触发父组件的搜索逻辑
  if (props.onFetchPersonnel) {
    fetchPersonnelList()
  }
}

const handlePersonnelReset = () => {
  personnelSearchForm.name = ''
  personnelSearchForm.idCard = ''
  personnelSearchForm.status = ''
  personnelPagination.page = 1
  if (props.onFetchPersonnel) {
    fetchPersonnelList()
  }
}

const handlePersonnelSizeChange = (size: number) => {
  personnelPagination.size = size
  personnelPagination.page = 1
  if (props.onFetchPersonnel) {
    fetchPersonnelList()
  }
}

const handlePersonnelCurrentChange = (page: number) => {
  personnelPagination.page = page
  if (props.onFetchPersonnel) {
    fetchPersonnelList()
  }
}

// 数据获取
const fetchPersonnelList = async () => {
  if (!props.onFetchPersonnel || !props.taskId) return
  
  personnelLoading.value = true
  try {
    const result = await props.onFetchPersonnel(props.taskId, personnelSearchForm, personnelPagination)
    personnelPagination.total = result.total
  } catch (error) {
    ElMessage.error('获取人员列表失败')
  } finally {
    personnelLoading.value = false
  }
}

// 监听
watch(() => props.taskId, (newTaskId) => {
  if (newTaskId && props.modelValue) {
    if (props.onFetchTaskDetail) {
      props.onFetchTaskDetail(newTaskId)
    }
    if (props.onFetchHistory) {
      historyLoading.value = true
      props.onFetchHistory(newTaskId).finally(() => {
        historyLoading.value = false
      })
    }
    if (props.onFetchPersonnel) {
      fetchPersonnelList()
    }
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && props.taskId) {
    if (props.onFetchTaskDetail) {
      props.onFetchTaskDetail(props.taskId)
    }
    if (props.onFetchHistory) {
      historyLoading.value = true
      props.onFetchHistory(props.taskId).finally(() => {
        historyLoading.value = false
      })
    }
    if (props.onFetchPersonnel) {
      fetchPersonnelList()
    }
  }
})
</script>

<style scoped>
.detail-tabs {
  height: 100%;
}

.task-info {
  padding: 20px 0;
}

.progress-section {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.progress-detail {
  margin-top: 12px;
  color: #606266;
  font-size: 14px;
}

.reminder-history {
  padding: 20px 0;
}

.empty-state {
  padding: 40px 0;
}

.personnel-section {
  padding: 20px 0;
}

.personnel-search {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.personnel-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-badge {
  font-size: 10px;
  color: #f56c6c;
  background: #fef0f0;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 4px;
}

.history-content {
  line-height: 1.6;
}

.history-content p {
  margin: 4px 0;
}

.history-content .operator {
  font-size: 12px;
  color: #909399;
}

.abnormal-type {
  margin-top: 4px;
}

.processing-result {
  margin-top: 4px;
}

.mt-1 {
  margin-top: 4px;
}
</style>
