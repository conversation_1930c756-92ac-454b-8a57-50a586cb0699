<template>
  <el-dialog
    v-model="dialogVisible"
    title="催办任务"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="催办原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="4"
          placeholder="请输入催办原因"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="催办方式" prop="method">
        <el-checkbox-group v-model="formData.method">
          <el-checkbox value="system">系统通知</el-checkbox>
          <el-checkbox value="email">邮件通知</el-checkbox>
          <el-checkbox value="sms">短信通知</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="紧急程度" prop="urgency">
        <el-radio-group v-model="formData.urgency">
          <el-radio value="normal">普通</el-radio>
          <el-radio value="urgent">紧急</el-radio>
          <el-radio value="critical">非常紧急</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定催办
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  taskId?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskId: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  submit: [data: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = ref({
  reason: '',
  method: ['system'],
  urgency: 'normal'
})

// 表单验证规则
const formRules: FormRules = {
  reason: [
    { required: true, message: '请输入催办原因', trigger: 'blur' },
    { min: 5, max: 200, message: '催办原因长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  method: [
    { required: true, message: '请选择催办方式', trigger: 'change' }
  ],
  urgency: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 重置表单
const resetForm = () => {
  formData.value = {
    reason: '',
    method: ['system'],
    urgency: 'normal'
  }
  formRef.value?.clearValidate()
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true

    // 模拟API调用
    setTimeout(() => {
      const submitData = {
        taskId: props.taskId,
        ...formData.value,
        timestamp: new Date().toISOString()
      }

      emit('submit', submitData)
      submitting.value = false
      resetForm()
    }, 1000)

  } catch (error) {
    console.log('表单验证失败:', error)
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
