<!-- 处理任务详情抽屉包装组件 -->
<template>
  <TaskDetailDrawer
    v-model="visible"
    :task-id="taskId"
    task-type="result-processing"
    :task-detail="taskDetail"
    :history-list="processingHistory"
    :personnel-list="personnelList"
    :on-fetch-task-detail="fetchTaskDetail"
    :on-fetch-history="fetchProcessingHistory"
    :on-fetch-personnel="fetchPersonnelList"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import TaskDetailDrawer from '@/components/common/TaskDetailDrawer.vue'

interface Props {
  modelValue: boolean
  taskId?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 数据状态
const taskDetail = ref<any>({})
const processingHistory = ref<any[]>([])
const personnelList = ref<any[]>([])

// 模拟数据
const mockTaskData = {
  id: '1',
  taskNo: 'PR202401001',
  title: '异常人员处理任务',
  status: 'in_progress',
  priority: 'high',
  personnelCount: 15,
  assignedToUserName: '李处理员',
  createdAt: '2024-01-15 09:00:00',
  dueDate: '2024-01-25 18:00:00',
  isOverdue: false,
  overdueBy: 0,
  description: '对背景调查中发现的异常人员进行处理，制定相应的处理方案',
  progress: {
    percentage: 60,
    completed: 9,
    total: 15
  }
}

const mockProcessingHistory = [
  {
    time: '2024-01-15 09:00:00',
    type: 'create',
    title: '任务创建',
    content: '处理任务已创建，等待分配处理人员',
    operator: '张管理员'
  },
  {
    time: '2024-01-15 10:30:00',
    type: 'assign',
    title: '任务分配',
    content: '任务已分配给李处理员',
    operator: '张管理员'
  },
  {
    time: '2024-01-16 14:20:00',
    type: 'process',
    title: '开始处理',
    content: '开始处理异常人员，已完成3人的处理',
    operator: '李处理员'
  }
]

const mockPersonnelList = [
  {
    id: '1',
    name: '张三',
    idCard: '110101199001011234',
    phone: '***********',
    organization: '技术部',
    abnormalType: 'education_fraud',
    processingStatus: 'completed',
    processingResult: 'focus'
  },
  {
    id: '2',
    name: '李四',
    idCard: '110101199002022345',
    phone: '***********',
    organization: '市场部',
    abnormalType: 'criminal_record',
    processingStatus: 'completed',
    processingResult: 'dismiss'
  },
  {
    id: '3',
    name: '王五',
    idCard: '110101199003033456',
    phone: '***********',
    organization: '财务部',
    abnormalType: 'credit_issue',
    processingStatus: 'pending'
  },
  {
    id: '4',
    name: '赵六',
    idCard: '110101199004044567',
    phone: '***********',
    organization: '人事部',
    abnormalType: 'work_experience',
    processingStatus: 'completed',
    processingResult: 'transfer'
  }
]

// 数据获取函数
const fetchTaskDetail = async (taskId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    taskDetail.value = mockTaskData
    return mockTaskData
  } catch (error) {
    ElMessage.error('获取任务详情失败')
    throw error
  }
}

const fetchProcessingHistory = async (taskId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    processingHistory.value = mockProcessingHistory
    return mockProcessingHistory
  } catch (error) {
    ElMessage.error('获取处理历史失败')
    throw error
  }
}

const fetchPersonnelList = async (
  taskId: string, 
  searchForm: any, 
  pagination: any
) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 400))
    
    let filteredList = [...mockPersonnelList]
    
    // 应用搜索条件
    if (searchForm.name) {
      filteredList = filteredList.filter(p => p.name.includes(searchForm.name))
    }
    if (searchForm.idCard) {
      filteredList = filteredList.filter(p => p.idCard.includes(searchForm.idCard))
    }
    if (searchForm.status) {
      if (searchForm.status === 'incomplete') {
        filteredList = filteredList.filter(p => p.processingStatus === 'pending')
      } else if (searchForm.status === 'completed') {
        filteredList = filteredList.filter(p => p.processingStatus === 'completed')
      }
    }
    
    // 分页处理
    const start = (pagination.page - 1) * pagination.size
    const end = start + pagination.size
    const paginatedList = filteredList.slice(start, end)
    
    personnelList.value = paginatedList
    
    return {
      list: paginatedList,
      total: filteredList.length
    }
  } catch (error) {
    ElMessage.error('获取人员列表失败')
    throw error
  }
}
</script>
