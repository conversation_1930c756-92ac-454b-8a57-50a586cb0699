<template>
  <el-dialog
    v-model="dialogVisible"
    title="异常人员详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="personnel" class="personnel-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4>基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <div class="avatar-container">
              <PersonnelAvatar
                :src="personnel.avatar"
                :name="personnel.name"
                size="large"
              />
            </div>
          </div>
          <div class="info-content">
            <div class="info-row">
              <span class="label">姓名：</span>
              <span class="value">{{ personnel.name }}</span>
            </div>
            <div class="info-row">
              <span class="label">身份证号：</span>
              <span class="value">{{ personnel.idCard }}</span>
            </div>
            <div class="info-row">
              <span class="label">联系方式：</span>
              <span class="value">{{ personnel.phone }}</span>
            </div>
            <div class="info-row">
              <span class="label">所属机构：</span>
              <span class="value">{{ personnel.organization }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 异常信息 -->
      <div class="detail-section">
        <h4>异常信息</h4>
        <div class="info-grid">
          <div class="info-row">
            <span class="label">异常类型：</span>
            <el-tag :type="getAbnormalTypeColor(personnel.abnormalType)">
              {{ getAbnormalTypeLabel(personnel.abnormalType) }}
            </el-tag>
          </div>
          <div class="info-row">
            <span class="label">异常等级：</span>
            <el-tag :type="getAbnormalLevelColor(personnel.abnormalLevel)">
              {{ getAbnormalLevelLabel(personnel.abnormalLevel) }}
            </el-tag>
          </div>
          <div class="info-row">
            <span class="label">发现时间：</span>
            <span class="value">{{ personnel.discoveredAt }}</span>
          </div>
          <div class="info-row full-width">
            <span class="label">异常描述：</span>
            <span class="value">{{ personnel.abnormalDescription }}</span>
          </div>
        </div>
      </div>

      <!-- 背审关联信息 -->
      <div class="detail-section">
        <h4>背审关联信息</h4>
        <div class="info-grid">
          <div class="info-row">
            <span class="label">背审任务编号：</span>
            <span class="value">{{ personnel.backgroundCheckTaskNo }}</span>
          </div>
          <div class="info-row">
            <span class="label">背审结果：</span>
            <span class="value">{{ personnel.backgroundCheckResult }}</span>
          </div>
        </div>
      </div>

      <!-- 处理状态 -->
      <div class="detail-section">
        <h4>处理状态</h4>
        <div class="info-grid">
          <div class="info-row">
            <span class="label">当前状态：</span>
            <el-tag :type="getProcessingStatusColor(personnel.processingStatus)">
              {{ getProcessingStatusLabel(personnel.processingStatus) }}
            </el-tag>
          </div>
          <div class="info-row" v-if="personnel.assignedToName">
            <span class="label">处理人：</span>
            <span class="value">{{ personnel.assignedToName }}</span>
          </div>
          <div class="info-row" v-if="personnel.assignedAt">
            <span class="label">分配时间：</span>
            <span class="value">{{ personnel.assignedAt }}</span>
          </div>
          <div class="info-row" v-if="personnel.processedAt">
            <span class="label">处理完成时间：</span>
            <span class="value">{{ personnel.processedAt }}</span>
          </div>
        </div>
      </div>

      <!-- 处理结果（如果已处理） -->
      <div v-if="personnel.processingResult || personnel.processingNote" class="detail-section">
        <h4>处理结果</h4>
        <div class="info-grid">
          <div v-if="personnel.processingResult" class="info-row full-width">
            <span class="label">处理结果：</span>
            <span class="value">{{ personnel.processingResult }}</span>
          </div>
          <div v-if="personnel.processingNote" class="info-row full-width">
            <span class="label">处理说明：</span>
            <span class="value">{{ personnel.processingNote }}</span>
          </div>
        </div>
      </div>

      <!-- 操作历史 -->
      <div class="detail-section">
        <h4>操作历史</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(history, index) in mockHistory"
            :key="index"
            :timestamp="history.time"
            placement="top"
          >
            <div class="history-item">
              <div class="history-content">{{ history.content }}</div>
              <div class="history-operator">操作人：{{ history.operator }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="personnel?.processingStatus === 'pending'"
          type="primary"
          @click="handleStartProcessing"
        >
          发起处理
        </el-button>
        <el-button
          v-if="personnel?.processingStatus === 'completed'"
          type="warning"
          @click="handleReprocess"
        >
          再次处理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PersonnelAvatar from '@/components/common/PersonnelAvatar.vue'
import type { AbnormalPersonnel } from '@/data/resultProcessingMockData'
import {
  abnormalTypeConfig,
  abnormalLevelConfig,
  processingStatusConfig
} from '@/config/resultProcessingConfig'

// Props
interface Props {
  modelValue: boolean
  personnel: AbnormalPersonnel | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  personnel: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'start-processing': [personnel: AbnormalPersonnel]
  'reprocess': [personnel: AbnormalPersonnel]
}>()

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟操作历史数据
const mockHistory = computed(() => {
  if (!props.personnel) return []
  
  const history = [
    {
      time: props.personnel.discoveredAt,
      content: `发现异常：${props.personnel.abnormalDescription}`,
      operator: '系统自动检测'
    }
  ]
  
  if (props.personnel.assignedAt) {
    history.push({
      time: props.personnel.assignedAt,
      content: `任务已分配给 ${props.personnel.assignedToName}`,
      operator: '张管理员'
    })
  }
  
  if (props.personnel.processedAt) {
    history.push({
      time: props.personnel.processedAt,
      content: `处理完成：${props.personnel.processingResult}`,
      operator: props.personnel.assignedToName || '处理员'
    })
  }
  
  return history.reverse() // 最新的在前面
})

// 工具函数
const getAbnormalTypeLabel = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.label || type
}

const getAbnormalTypeColor = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.color || 'info'
}

const getAbnormalLevelLabel = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.label || level
}

const getAbnormalLevelColor = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.color || 'info'
}

const getProcessingStatusLabel = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.label || status
}

const getProcessingStatusColor = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.color || 'info'
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
}

const handleStartProcessing = () => {
  if (props.personnel) {
    emit('start-processing', props.personnel)
    handleClose()
  }
}

const handleReprocess = () => {
  if (!props.personnel) return
  
  ElMessageBox.confirm(
    `确定要对 ${props.personnel.name} 再次下发处理任务吗？`,
    '确认再次处理',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    if (props.personnel) {
      emit('reprocess', props.personnel)
      handleClose()
    }
  }).catch(() => {
    // 用户取消
  })
}
</script>

<style scoped>
.personnel-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  align-items: start;
}

.info-item {
  display: flex;
  align-items: center;
}

.avatar-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-row.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 8px;
}

.value {
  color: #303133;
  word-break: break-all;
}

.history-item {
  padding: 8px 0;
}

.history-content {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-row.full-width {
    grid-column: 1;
  }
}
</style>
