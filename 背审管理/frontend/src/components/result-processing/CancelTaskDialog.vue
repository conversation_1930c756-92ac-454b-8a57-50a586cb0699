<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :before-close="handleClose"
  >
    <div class="cancel-info">
      <el-alert
        :title="alertTitle"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      style="margin-top: 20px;"
    >
      <el-form-item label="撤销原因" prop="reason">
        <el-select
          v-model="formData.reason"
          placeholder="请选择撤销原因"
          style="width: 100%"
        >
          <el-option label="任务重复" value="duplicate" />
          <el-option label="需求变更" value="requirement_change" />
          <el-option label="人员调整" value="personnel_change" />
          <el-option label="优先级调整" value="priority_change" />
          <el-option label="其他原因" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="详细说明" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请详细说明撤销原因"
          maxlength="300"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="通知方式" prop="notifyMethod">
        <el-checkbox-group v-model="formData.notifyMethod">
          <el-checkbox value="system">系统通知</el-checkbox>
          <el-checkbox value="email">邮件通知</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="danger" @click="handleSubmit" :loading="submitting">
          确定撤销
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  taskIds: string[]
  isBatch: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskIds: () => [],
  isBatch: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  submit: [data: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = ref({
  reason: '',
  description: '',
  notifyMethod: ['system']
})

// 表单验证规则
const formRules: FormRules = {
  reason: [
    { required: true, message: '请选择撤销原因', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入详细说明', trigger: 'blur' },
    { min: 10, max: 300, message: '详细说明长度在 10 到 300 个字符', trigger: 'blur' }
  ],
  notifyMethod: [
    { required: true, message: '请选择通知方式', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  return props.isBatch ? '批量撤销任务' : '撤销任务'
})

const alertTitle = computed(() => {
  if (props.isBatch) {
    return `确定要撤销选中的 ${props.taskIds.length} 个任务吗？撤销后将无法恢复！`
  } else {
    return '确定要撤销该任务吗？撤销后将无法恢复！'
  }
})

// 重置表单
const resetForm = () => {
  formData.value = {
    reason: '',
    description: '',
    notifyMethod: ['system']
  }
  formRef.value?.clearValidate()
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true

    // 模拟API调用
    setTimeout(() => {
      const submitData = {
        taskIds: props.taskIds,
        isBatch: props.isBatch,
        ...formData.value,
        timestamp: new Date().toISOString()
      }

      emit('submit', submitData)
      submitting.value = false
      resetForm()
    }, 1000)

  } catch (error) {
    console.log('表单验证失败:', error)
  }
}
</script>

<style scoped>
.cancel-info {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-alert) {
  border-radius: 6px;
}
</style>
