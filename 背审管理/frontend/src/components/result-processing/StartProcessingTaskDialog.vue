<template>
  <el-dialog
    v-model="visible"
    title="发起处理任务"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4>基本信息</h4>
        <el-form-item label="任务标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入任务标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-select v-model="formData.priority" placeholder="请选择优先级">
            <el-option label="普通" value="low" />
            <el-option label="中等" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
      </div>

      <!-- 任务分配 -->
      <div class="form-section">
        <h4>任务分配</h4>
        <el-form-item label="分配方式" prop="assignmentType">
          <el-radio-group v-model="formData.assignmentType">
            <el-radio value="department">部门分配</el-radio>
            <el-radio value="individual">个人分配</el-radio>
          </el-radio-group>
          <div class="form-tip">{{ assignmentTip }}</div>
        </el-form-item>

        <el-form-item
          v-if="formData.assignmentType === 'department'"
          label="分配部门"
          prop="assignedToOrg"
        >
          <el-select
            v-model="formData.assignedToOrg"
            placeholder="请选择分配部门"
            filterable
          >
            <el-option
              v-for="dept in departments"
              :key="dept.value"
              :label="dept.label"
              :value="dept.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="formData.assignmentType === 'individual'"
          label="分配给"
          prop="assignedToUser"
        >
          <el-select
            v-model="formData.assignedToUser"
            placeholder="请选择处理人员"
            filterable
          >
            <el-option
              v-for="user in users"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="截止时间" prop="dueDate">
          <el-date-picker
            v-model="formData.dueDate"
            type="datetime"
            placeholder="请选择截止时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
          />
        </el-form-item>
      </div>

      <!-- 操作范围 -->
      <div class="form-section">
        <h4>操作范围</h4>
        <el-form-item label="操作范围" prop="operationScope">
          <el-radio-group v-model="formData.operationScope">
            <el-radio value="selected">
              选中的人员 ({{ selectedCount }}人)
            </el-radio>
            <el-radio value="all">
              全部筛选结果 ({{ totalCount }}人)
            </el-radio>
          </el-radio-group>
          <div class="scope-tip">{{ scopeTip }}</div>
        </el-form-item>

        <div v-if="selectedPersonnel && selectedPersonnel.length > 0" class="personnel-preview">
          <div class="preview-title">将对以下人员发起处理任务：</div>
          <div class="personnel-tags">
            <el-tag
              v-for="name in selectedPersonnel.slice(0, 10)"
              :key="name"
              class="personnel-tag"
            >
              {{ name }}
            </el-tag>
            <el-tag v-if="selectedPersonnel.length > 10" type="info">
              等{{ selectedPersonnel.length }}人
            </el-tag>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定发起
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
  selectedCount: number
  totalCount: number
  selectedPersonnel?: string[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [formData: any]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive({
  title: '',
  description: '',
  priority: 'medium',
  assignmentType: 'individual',
  assignedToOrg: '',
  assignedToUser: '',
  dueDate: '',
  operationScope: 'selected'
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  assignmentType: [
    { required: true, message: '请选择分配方式', trigger: 'change' }
  ],
  assignedToOrg: [
    { required: true, message: '请选择分配部门', trigger: 'change' }
  ],
  assignedToUser: [
    { required: true, message: '请选择处理人员', trigger: 'change' }
  ],
  dueDate: [
    { required: true, message: '请选择截止时间', trigger: 'change' }
  ],
  operationScope: [
    { required: true, message: '请选择操作范围', trigger: 'change' }
  ]
}

// 计算属性
const assignmentTip = computed(() => {
  if (formData.assignmentType === 'department') {
    return '任务将分配给指定部门，由部门负责人进行二次分配'
  } else {
    return '任务将直接分配给指定的处理人员'
  }
})

const scopeTip = computed(() => {
  if (formData.operationScope === 'selected') {
    return `仅对当前选中的 ${props.selectedCount} 人执行操作`
  } else {
    return `对当前筛选条件下的全部 ${props.totalCount} 人执行操作`
  }
})

// 选项数据
const departments = [
  { label: '莲池分局', value: 'dept-001' },
  { label: '竞秀分局', value: 'dept-002' },
  { label: '满城分局', value: 'dept-003' },
  { label: '清苑分局', value: 'dept-004' }
]

const users = [
  { label: '王处理员', value: 'user-001' },
  { label: '赵处理员', value: 'user-002' },
  { label: '孙处理员', value: 'user-003' },
  { label: '李处理员', value: 'user-004' }
]

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用昨天之前的日期
}

// 监听分配方式变化，清空相关字段
watch(() => formData.assignmentType, (newType) => {
  if (newType === 'department') {
    formData.assignedToUser = ''
  } else {
    formData.assignedToOrg = ''
  }
})

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    description: '',
    priority: 'medium',
    assignmentType: 'individual',
    assignedToOrg: '',
    assignedToUser: '',
    dueDate: '',
    operationScope: 'selected'
  })
  formRef.value?.clearValidate()
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 确认对话框
    const targetCount = formData.operationScope === 'selected' ? props.selectedCount : props.totalCount
    const confirmText = `确定要对 ${targetCount} 人发起处理任务吗？`

    await ElMessageBox.confirm(confirmText, '确认发起处理任务', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    submitting.value = true

    // 模拟API调用
    setTimeout(() => {
      emit('submit', { ...formData })
      submitting.value = false
      resetForm()
    }, 1000)

  } catch (error) {
    console.log('表单验证失败或用户取消:', error)
  }
}

// 监听弹窗打开，设置默认值
watch(() => props.modelValue, (visible) => {
  if (visible) {
    // 设置默认标题
    formData.title = '异常人员处理任务'

    // 设置默认截止时间（7天后）
    const deadline = new Date()
    deadline.setDate(deadline.getDate() + 7)
    deadline.setHours(18, 0, 0, 0)
    formData.dueDate = deadline.toISOString().slice(0, 19).replace('T', ' ')
  }
})
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.scope-tip {
  margin-top: 8px;
}

.personnel-preview {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.preview-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.personnel-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.personnel-tag {
  margin: 0;
}

.dialog-footer {
  text-align: right;
}
</style>
