<template>
  <el-dialog
    v-model="dialogVisible"
    title="发起处理任务"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4>基本信息</h4>
        <el-form-item label="任务标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入任务标题"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-select v-model="formData.priority" placeholder="请选择优先级">
            <el-option
              v-for="option in priorityOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 处理要求 -->
      <div class="form-section">
        <h4>处理要求</h4>
        <el-form-item label="处理要求" prop="processingRequirement">
          <el-input
            v-model="formData.processingRequirement"
            type="textarea"
            :rows="3"
            placeholder="请输入具体的处理要求"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="处理期限" prop="processingDeadline">
          <el-date-picker
            v-model="formData.processingDeadline"
            type="datetime"
            placeholder="请选择处理期限"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
          />
        </el-form-item>
      </div>

      <!-- 任务分配 -->
      <div class="form-section">
        <h4>任务分配</h4>
        <el-form-item label="分配方式" prop="assignmentType">
          <el-radio-group v-model="formData.assignmentType">
            <el-radio value="department">部门分配</el-radio>
            <el-radio value="individual">个人分配</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="formData.assignmentType === 'department'"
          label="分配部门"
          prop="assignedToOrg"
        >
          <el-select
            v-model="formData.assignedToOrg"
            placeholder="请选择分配部门"
            filterable
          >
            <el-option
              v-for="org in organizationOptions"
              :key="org.value"
              :label="org.label"
              :value="org.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="formData.assignmentType === 'individual'"
          label="分配给"
          prop="assignedToUser"
        >
          <el-select
            v-model="formData.assignedToUser"
            placeholder="请选择处理人员"
            filterable
          >
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 影响范围 -->
      <div class="form-section">
        <h4>影响范围</h4>
        <el-form-item label="操作范围">
          <el-radio-group v-model="operationScopeLocal">
            <el-radio value="selected">
              选中的人员 ({{ selectedPersonnel.length }}人)
            </el-radio>
            <el-radio value="all" disabled>
              全部筛选结果 (功能开发中)
            </el-radio>
          </el-radio-group>
          <div class="scope-tip">
            <el-text type="info" size="small">
              仅对当前选中的人员执行操作
            </el-text>
          </div>
        </el-form-item>

        <div class="personnel-preview">
          <div class="preview-title">将对以下人员发起处理任务：</div>
          <div class="personnel-tags">
            <el-tag
              v-for="personnel in selectedPersonnel.slice(0, 10)"
              :key="personnel.id"
              class="personnel-tag"
            >
              {{ personnel.name }}
            </el-tag>
            <el-tag v-if="selectedPersonnel.length > 10" type="info">
              等{{ selectedPersonnel.length }}人
            </el-tag>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitting">
          确定发起
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { AbnormalPersonnel } from '@/data/resultProcessingMockData'
import { getPriorityOptions } from '@/config/resultProcessingConfig'

// Props
interface Props {
  modelValue: boolean
  selectedPersonnel: AbnormalPersonnel[]
  operationScope: 'selected' | 'all'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  selectedPersonnel: () => [],
  operationScope: 'selected'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const operationScopeLocal = ref(props.operationScope)

// 表单数据
const formData = ref({
  title: '',
  description: '',
  priority: 'medium',
  processingRequirement: '',
  processingDeadline: '',
  assignmentType: 'individual',
  assignedToOrg: '',
  assignedToUser: ''
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  processingRequirement: [
    { required: true, message: '请输入处理要求', trigger: 'blur' },
    { min: 10, max: 500, message: '处理要求长度在 10 到 500 个字符', trigger: 'blur' }
  ],
  processingDeadline: [
    { required: true, message: '请选择处理期限', trigger: 'change' }
  ],
  assignmentType: [
    { required: true, message: '请选择分配方式', trigger: 'change' }
  ],
  assignedToOrg: [
    { required: true, message: '请选择分配部门', trigger: 'change' }
  ],
  assignedToUser: [
    { required: true, message: '请选择处理人员', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 选项数据
const priorityOptions = getPriorityOptions()

const organizationOptions = [
  { label: '莲池分局', value: 'org-001' },
  { label: '竞秀分局', value: 'org-002' },
  { label: '满城分局', value: 'org-003' },
  { label: '清苑分局', value: 'org-004' }
]

const userOptions = [
  { label: '王处理员', value: 'user-001' },
  { label: '赵处理员', value: 'user-002' },
  { label: '孙处理员', value: 'user-003' },
  { label: '李处理员', value: 'user-004' }
]

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用昨天之前的日期
}

// 监听分配方式变化，清空相关字段
watch(() => formData.value.assignmentType, (newType) => {
  if (newType === 'department') {
    formData.value.assignedToUser = ''
  } else {
    formData.value.assignedToOrg = ''
  }
})

// 重置表单
const resetForm = () => {
  formData.value = {
    title: '',
    description: '',
    priority: 'medium',
    processingRequirement: '',
    processingDeadline: '',
    assignmentType: 'individual',
    assignedToOrg: '',
    assignedToUser: ''
  }
  formRef.value?.clearValidate()
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 确认对话框
    const confirmText = `确定要对选中的 ${props.selectedPersonnel.length} 人发起处理任务吗？`
    await ElMessageBox.confirm(confirmText, '确认发起处理任务', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    submitting.value = true

    // 模拟API调用
    setTimeout(() => {
      const taskData = {
        ...formData.value,
        personnelIds: props.selectedPersonnel.map(p => p.id),
        personnelCount: props.selectedPersonnel.length,
        operationScope: operationScopeLocal.value
      }

      emit('confirm', taskData)
      submitting.value = false
      resetForm()
    }, 1000)

  } catch (error) {
    console.log('表单验证失败或用户取消:', error)
  }
}

// 监听弹窗打开，设置默认值
watch(() => props.modelValue, (visible) => {
  if (visible && props.selectedPersonnel.length > 0) {
    // 根据选中人员生成默认标题
    const abnormalTypes = [...new Set(props.selectedPersonnel.map(p => p.abnormalType))]
    if (abnormalTypes.length === 1) {
      const typeConfig = {
        'criminal_record': '犯罪记录',
        'credit_issue': '信用问题', 
        'education_fraud': '学历造假',
        'work_experience': '工作经历',
        'identity_issue': '身份信息',
        'reference_problem': '推荐人问题'
      }
      formData.value.title = `${typeConfig[abnormalTypes[0] as keyof typeof typeConfig]}异常处理`
    } else {
      formData.value.title = '异常人员处理任务'
    }
    
    // 设置默认处理期限（7天后）
    const deadline = new Date()
    deadline.setDate(deadline.getDate() + 7)
    deadline.setHours(18, 0, 0, 0)
    formData.value.processingDeadline = deadline.toISOString().slice(0, 19).replace('T', ' ')
  }
})
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.scope-tip {
  margin-top: 8px;
}

.personnel-preview {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.preview-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.personnel-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.personnel-tag {
  margin: 0;
}

.dialog-footer {
  text-align: right;
}
</style>
