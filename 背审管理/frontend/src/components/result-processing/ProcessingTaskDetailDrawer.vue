<!-- 处理任务详情抽屉组件 -->
<template>
  <el-drawer
    v-model="visible"
    title="处理任务详情"
    size="60%"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab" class="detail-tabs">
      <!-- 任务信息 -->
      <el-tab-pane label="任务信息" name="info">
        <div class="task-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务编号">
              {{ taskDetail.taskNo }}
            </el-descriptions-item>
            <el-descriptions-item label="任务标题">
              {{ taskDetail.title }}
            </el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag :type="getStatusColor(taskDetail.status)" size="small">
                {{ getStatusText(taskDetail.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityColor(taskDetail.priority)" size="small">
                {{ getPriorityText(taskDetail.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="人员数量">
              {{ taskDetail.personnelCount }}人
            </el-descriptions-item>
            <el-descriptions-item label="处理人">
              {{ taskDetail.assignedToUserName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ taskDetail.createdAt }}
            </el-descriptions-item>
            <el-descriptions-item label="截止时间">
              <span :class="{ 'overdue-text': taskDetail.isOverdue }">
                {{ taskDetail.dueDate }}
                <span v-if="taskDetail.isOverdue" class="overdue-badge">
                  逾期{{ taskDetail.overdueBy }}天
                </span>
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="任务描述" :span="2">
              {{ taskDetail.description || '-' }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 进度信息 -->
          <div class="progress-section">
            <h4>任务进度</h4>
            <el-progress 
              :percentage="taskDetail.progress?.percentage || 0" 
              :stroke-width="12"
              :status="getProgressStatus(taskDetail)"
            />
            <div class="progress-detail">
              已完成：{{ taskDetail.progress?.completed || 0 }}人 / 
              总计：{{ taskDetail.progress?.total || 0 }}人
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 处理历史 -->
      <el-tab-pane label="处理历史" name="history">
        <div class="reminder-history">
          <div v-if="historyLoading" v-loading="true" style="height: 200px;"></div>
          <div v-else-if="processingHistory.length === 0" class="empty-state">
            <el-empty description="暂无处理记录" />
          </div>
          <el-timeline v-else>
            <el-timeline-item
              v-for="(item, index) in processingHistory"
              :key="index"
              :timestamp="item.time"
              placement="top"
              :type="getHistoryType(item.type)"
            >
              <div class="history-content">
                <p><strong>{{ item.title }}</strong></p>
                <p>{{ item.content }}</p>
                <p v-if="item.operator" class="operator">操作人：{{ item.operator }}</p>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-tab-pane>

      <!-- 人员列表 -->
      <el-tab-pane label="人员列表" name="personnel">
        <div class="personnel-section">
          <!-- 搜索表单 -->
          <div class="personnel-search">
            <el-form :inline="true" :model="personnelSearchForm">
              <el-form-item label="姓名">
                <el-input
                  v-model="personnelSearchForm.name"
                  placeholder="请输入姓名"
                  clearable
                  @keyup.enter="handlePersonnelSearch"
                />
              </el-form-item>
              <el-form-item label="身份证号">
                <el-input
                  v-model="personnelSearchForm.idCard"
                  placeholder="请输入身份证号"
                  clearable
                  @keyup.enter="handlePersonnelSearch"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handlePersonnelSearch">搜索</el-button>
                <el-button @click="handlePersonnelReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 人员列表 -->
          <el-table :data="personnelList" :loading="personnelLoading">
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="idCard" label="身份证号" width="180">
              <template #default="scope">
                {{ maskIdCard(scope.row.idCard) }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系方式" width="140" />
            <el-table-column prop="organization" label="所属机构" min-width="150" />
            <el-table-column prop="abnormalType" label="异常类型" width="120">
              <template #default="scope">
                <el-tag :type="getAbnormalTypeColor(scope.row.abnormalType)" size="small">
                  {{ getAbnormalTypeText(scope.row.abnormalType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="processingStatus" label="处理状态" width="120">
              <template #default="scope">
                <el-tag :type="getProcessingStatusColor(scope.row.processingStatus)" size="small">
                  {{ getProcessingStatusText(scope.row.processingStatus) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <!-- 人员分页 -->
          <div class="personnel-pagination">
            <el-pagination
              v-model:current-page="personnelPagination.page"
              v-model:page-size="personnelPagination.size"
              :page-sizes="[10, 20, 50]"
              :total="personnelPagination.total"
              layout="total, sizes, prev, pager, next"
              @size-change="handlePersonnelSizeChange"
              @current-change="handlePersonnelCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  processingTaskMockData, 
  abnormalPersonnelMockData,
  type ProcessingTask 
} from '@/data/resultProcessingMockData'
import {
  taskStatusConfig,
  priorityConfig,
  abnormalTypeConfig,
  processingStatusConfig
} from '@/config/resultProcessingConfig'

interface Props {
  modelValue: boolean
  taskId?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeTab = ref('info')
const taskDetail = ref<any>({})
const processingHistory = ref<any[]>([])
const historyLoading = ref(false)
const personnelList = ref<any[]>([])
const personnelLoading = ref(false)

// 人员搜索表单
const personnelSearchForm = reactive({
  name: '',
  idCard: ''
})

// 人员分页
const personnelPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 监听taskId变化，获取任务详情
watch(() => props.taskId, (newTaskId) => {
  if (newTaskId && props.modelValue) {
    fetchTaskDetail()
    fetchProcessingHistory()
    fetchPersonnelList()
  }
}, { immediate: true })

// 监听抽屉打开
watch(() => props.modelValue, (visible) => {
  if (visible && props.taskId) {
    fetchTaskDetail()
    fetchProcessingHistory()
    fetchPersonnelList()
  }
})

// 获取任务详情
const fetchTaskDetail = () => {
  if (!props.taskId) return
  
  // 模拟API调用
  const task = processingTaskMockData.find(t => t.id === props.taskId)
  if (task) {
    taskDetail.value = task
  }
}

// 获取处理历史
const fetchProcessingHistory = () => {
  historyLoading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    processingHistory.value = [
      {
        time: '2024-01-15 09:00:00',
        type: 'primary',
        title: '任务创建',
        content: '处理任务已创建，等待分配处理人员',
        operator: '张管理员'
      },
      {
        time: '2024-01-15 10:30:00',
        type: 'success',
        title: '任务分配',
        content: '任务已分配给李处理员',
        operator: '张管理员'
      }
    ]
    historyLoading.value = false
  }, 500)
}

// 获取人员列表
const fetchPersonnelList = () => {
  personnelLoading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    // 根据任务ID获取相关人员
    let list = abnormalPersonnelMockData.slice(0, 20)
    
    // 应用搜索条件
    if (personnelSearchForm.name) {
      list = list.filter(p => p.name.includes(personnelSearchForm.name))
    }
    if (personnelSearchForm.idCard) {
      list = list.filter(p => p.idCard.includes(personnelSearchForm.idCard))
    }
    
    // 分页处理
    const start = (personnelPagination.page - 1) * personnelPagination.size
    const end = start + personnelPagination.size
    
    personnelList.value = list.slice(start, end)
    personnelPagination.total = list.length
    personnelLoading.value = false
  }, 500)
}

// 工具函数
const getStatusText = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.label || status
}

const getStatusColor = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.color || 'info'
}

const getPriorityText = (priority: string) => {
  return priorityConfig[priority as keyof typeof priorityConfig]?.label || priority
}

const getPriorityColor = (priority: string) => {
  return priorityConfig[priority as keyof typeof priorityConfig]?.color || 'info'
}

const getProgressStatus = (task: any) => {
  if (!task.progress) return undefined
  if (task.status === 'completed') return 'success'
  if (task.status === 'cancelled') return 'exception'
  if (task.isOverdue) return 'exception'
  return undefined
}

const getHistoryType = (type: string) => {
  return type
}

const getAbnormalTypeText = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.label || type
}

const getAbnormalTypeColor = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.color || 'info'
}

const getProcessingStatusText = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.label || status
}

const getProcessingStatusColor = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.color || 'info'
}

const maskIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
}

const handlePersonnelSearch = () => {
  personnelPagination.page = 1
  fetchPersonnelList()
}

const handlePersonnelReset = () => {
  personnelSearchForm.name = ''
  personnelSearchForm.idCard = ''
  personnelPagination.page = 1
  fetchPersonnelList()
}

const handlePersonnelSizeChange = (size: number) => {
  personnelPagination.size = size
  personnelPagination.page = 1
  fetchPersonnelList()
}

const handlePersonnelCurrentChange = (page: number) => {
  personnelPagination.page = page
  fetchPersonnelList()
}
</script>

<style scoped>
.detail-tabs {
  height: 100%;
}

.task-info {
  padding: 20px 0;
}

.progress-section {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.progress-detail {
  margin-top: 12px;
  color: #606266;
  font-size: 14px;
}

.reminder-history {
  padding: 20px 0;
}

.empty-state {
  padding: 40px 0;
}

.personnel-section {
  padding: 20px 0;
}

.personnel-search {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.personnel-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-badge {
  font-size: 10px;
  color: #f56c6c;
  background: #fef0f0;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 4px;
}

.history-content {
  line-height: 1.6;
}

.history-content p {
  margin: 4px 0;
}

.history-content .operator {
  font-size: 12px;
  color: #909399;
}
</style>
