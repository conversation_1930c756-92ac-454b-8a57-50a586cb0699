<template>
  <el-drawer
    v-model="drawerVisible"
    title="处理任务详情"
    :size="800"
    direction="rtl"
    :before-close="handleClose"
    class="processing-drawer"
  >
    <div v-loading="loading" class="drawer-content">
      <!-- 内容区域 -->
      <div class="content-section">
        <div v-if="loading" class="loading-state">
          <el-skeleton :rows="8" animated />
        </div>

        <div v-else-if="taskData" class="task-content">
          <!-- 基本信息区域 -->
          <div class="basic-info-section">
            <div class="info-content">
              <div class="info-grid">
                <div class="info-item">
                  <label>任务编号：</label>
                  <span>{{ taskData.taskNo }}</span>
                </div>
                <div class="info-item">
                  <label>任务标题：</label>
                  <span>{{ taskData.title }}</span>
                </div>
                <div class="info-item">
                  <label>人员数量：</label>
                  <span>{{ taskData.personnelCount }}人</span>
                </div>
                <div class="info-item">
                  <label>处理人：</label>
                  <span>{{ taskData.assignedToUserName || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>创建时间：</label>
                  <span>{{ taskData.createdAt }}</span>
                </div>
                <div class="info-item">
                  <label>截止时间：</label>
                  <span :class="{ 'overdue-text': taskData.isOverdue }">
                    {{ taskData.dueDate }}
                    <span v-if="taskData.isOverdue" class="overdue-badge">
                      逾期{{ taskData.overdueBy }}天
                    </span>
                  </span>
                </div>
                <div class="info-item full-width">
                  <label>任务状态：</label>
                  <div class="status-info">
                    <el-tag :type="getStatusColor(taskData.status)" size="large" class="status-tag">
                      {{ getStatusText(taskData.status) }}
                    </el-tag>
                    <el-tag :type="getPriorityColor(taskData.priority)" size="small" class="priority-tag">
                      {{ getPriorityText(taskData.priority) }}优先级
                    </el-tag>
                  </div>
                </div>
                <div v-if="taskData.description" class="info-item full-width">
                  <label>任务描述：</label>
                  <span class="description-text">{{ taskData.description }}</span>
                </div>
              </div>
            </div>
            <div class="progress-section">
              <div class="progress-container">
                <el-progress
                  :percentage="taskData.progress.percentage"
                  :stroke-width="12"
                  :status="getProgressStatus(taskData)"
                />
                <div class="progress-stats">
                  <span>已完成：{{ taskData.progress.completed }}人</span>
                  <span>总计：{{ taskData.progress.total }}人</span>
                  <span>进度：{{ taskData.progress.percentage }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 任务操作 -->
          <div v-if="canOperateTask" class="action-section">
            <h4 class="section-title">任务操作</h4>
            <div class="action-content">
              <!-- 操作方式选择 -->
              <div class="action-options">
                <div class="option-title">选择操作方式</div>
                <el-radio-group
                  v-model="actionForm.actionType"
                  class="action-radios"
                  @change="handleActionTypeChange"
                >
                  <el-radio
                    v-for="option in actionOptions"
                    :key="option.value"
                    :value="option.value"
                    class="action-radio"
                  >
                    <span class="radio-label">{{ option.label }}</span>
                  </el-radio>
                </el-radio-group>
              </div>

              <!-- 操作原因 -->
              <div class="reason-section">
                <div class="reason-title">操作原因</div>
                <el-input
                  v-model="actionForm.reason"
                  type="textarea"
                  :rows="4"
                  placeholder="请详细说明操作原因..."
                  maxlength="500"
                  show-word-limit
                  class="reason-input"
                />
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="large"
                  @click="handleSubmitAction"
                  :loading="submitting"
                  class="submit-btn"
                >
                  <el-icon>
                    <Check />
                  </el-icon>
                  提交操作
                </el-button>
                <el-button
                  size="large"
                  @click="resetActionForm"
                  class="reset-btn"
                >
                  <el-icon>
                    <Refresh />
                  </el-icon>
                  重置
                </el-button>
              </div>
            </div>
          </div>

          <!-- 处理记录 -->
          <div class="history-section">
            <h4 class="section-title">处理记录</h4>
            <div v-if="historyList.length === 0" class="empty-history">
              <el-empty description="暂无处理记录" :image-size="60" />
            </div>
            <div v-else class="history-list">
              <div v-for="record in historyList" :key="record.id" class="history-item">
                <div class="history-header">
                  <div class="status-change">
                    <span class="from-status">{{ getStatusText(record.fromStatus) }}</span>
                    <el-icon class="arrow-icon">
                      <ArrowRight />
                    </el-icon>
                    <span class="to-status">{{ getStatusText(record.toStatus) }}</span>
                  </div>
                  <div class="history-time">{{ formatTime(record.operateTime) }}</div>
                </div>
                <div class="history-content">
                  <p><strong>操作人：</strong>{{ record.operatorName || '系统' }}</p>
                  <p v-if="record.reason"><strong>操作原因：</strong>{{ record.reason }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="error-state">
          <el-empty description="加载失败，请重试" />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowRight, Check, Refresh } from '@element-plus/icons-vue'
import { 
  processingTaskMockData, 
  abnormalPersonnelMockData,
  type ProcessingTask 
} from '@/data/resultProcessingMockData'
import {
  taskStatusConfig,
  taskPriorityConfig
} from '@/config/resultProcessingConfig'

interface Props {
  modelValue: boolean
  taskId?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'task-updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const taskData = ref<ProcessingTask | null>(null)
const submitting = ref(false)
const historyList = ref<any[]>([])

// 计算属性
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否可以操作任务
const canOperateTask = computed(() => {
  return taskData.value && ['pending', 'in_progress'].includes(taskData.value.status)
})

// 处理动作表单
const actionForm = reactive({
  actionType: 1,
  reason: ''
})

// 处理方式选项
const actionOptions = [
  { value: 1, label: '完成任务' },
  { value: 2, label: '暂停任务' },
  { value: 3, label: '取消任务' },
  { value: 4, label: '重新分配' }
]

// 工具函数
const getStatusText = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.label || status
}

const getStatusColor = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.color || 'info'
}

const getPriorityText = (priority: string) => {
  return taskPriorityConfig[priority as keyof typeof taskPriorityConfig]?.label || priority
}

const getPriorityColor = (priority: string) => {
  return taskPriorityConfig[priority as keyof typeof taskPriorityConfig]?.color || 'info'
}

const getProgressStatus = (task: ProcessingTask) => {
  if (task.status === 'completed') return 'success'
  if (task.status === 'cancelled') return 'exception'
  if (task.isOverdue) return 'exception'
  return undefined
}

const formatTime = (time: string) => {
  return time
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
}

const handleActionTypeChange = () => {
  actionForm.reason = ''
}

const handleSubmitAction = async () => {
  if (!actionForm.reason.trim()) {
    ElMessage.warning('请填写操作原因')
    return
  }

  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('操作提交成功')
    emit('task-updated')
    loadTaskData()
    resetActionForm()
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

const resetActionForm = () => {
  actionForm.actionType = 1
  actionForm.reason = ''
}

// 数据加载
const loadTaskData = () => {
  if (!props.taskId) return

  loading.value = true
  try {
    // 模拟API调用
    setTimeout(() => {
      const task = processingTaskMockData.find(t => t.id === props.taskId)
      if (task) {
        taskData.value = task
        loadHistoryData()
      }
      loading.value = false
    }, 500)
  } catch (error) {
    loading.value = false
    ElMessage.error('加载任务详情失败')
  }
}

const loadHistoryData = () => {
  // 模拟历史记录数据
  historyList.value = [
    {
      id: 1,
      fromStatus: 'pending',
      toStatus: 'in_progress',
      operateTime: '2024-01-15 09:00:00',
      operatorName: '张管理员',
      reason: '开始处理任务'
    }
  ]
}

// 监听taskId变化
watch(() => props.taskId, () => {
  if (props.modelValue && props.taskId) {
    loadTaskData()
  }
}, { immediate: true })

// 监听抽屉打开
watch(() => props.modelValue, (visible) => {
  if (visible && props.taskId) {
    loadTaskData()
  }
})
</script>

<style scoped>
.processing-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 20px 24px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-drawer__body) {
    padding: 0;
    overflow: hidden;
  }
}

.drawer-content {
  height: 100%;
  display: flex;
}

.content-section {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.loading-state {
  padding: 20px;
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 基本信息区域 */
.basic-info-section {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.info-content {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.info-item span {
  color: #303133;
  word-break: break-all;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.status-tag {
  font-weight: 600;
}

.priority-tag {
  margin-left: 8px;
}

.description-text {
  line-height: 1.6;
  color: #606266;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-badge {
  font-size: 12px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

/* 进度区域 */
.progress-section {
  padding: 20px;
  background-color: #f5f7fa;
  border-top: 1px solid #ebeef5;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #606266;
}

/* 操作区域 */
.action-section {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.section-title {
  margin: 0;
  padding: 16px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.action-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.action-radios {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-radio {
  margin-right: 0;
  margin-bottom: 0;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s;
}

.action-radio:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.action-radio.is-checked {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.radio-label {
  font-weight: 500;
  color: #303133;
}

.reason-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reason-title {
  font-weight: 500;
  color: #303133;
}

.reason-input {
  width: 100%;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.submit-btn,
.reset-btn {
  min-width: 120px;
}

/* 历史记录区域 */
.history-section {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.empty-history {
  padding: 40px 20px;
  text-align: center;
}

.history-list {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.from-status {
  color: #909399;
}

.to-status {
  color: #409eff;
}

.arrow-icon {
  color: #c0c4cc;
}

.history-time {
  font-size: 12px;
  color: #909399;
}

.history-content {
  font-size: 14px;
  line-height: 1.6;
}

.history-content p {
  margin: 4px 0;
  color: #606266;
}

.history-content strong {
  color: #303133;
}

/* 错误状态 */
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-section {
    padding: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .info-item.full-width {
    grid-column: 1;
  }

  .progress-stats {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .submit-btn,
  .reset-btn {
    width: 100%;
  }

  .history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
