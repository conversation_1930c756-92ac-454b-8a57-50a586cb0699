<template>
  <el-drawer
    v-model="drawerVisible"
    title="处理任务详情"
    :size="800"
    :before-close="handleClose"
  >
    <div v-if="taskData" class="task-detail">
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- Tab 1: 任务信息 -->
        <el-tab-pane label="任务信息" name="info">
          <div class="tab-content">
            <div class="info-section">
              <h4>基本信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">任务编号：</span>
                  <span class="value">{{ taskData.taskNo }}</span>
                </div>
                <div class="info-item">
                  <span class="label">任务标题：</span>
                  <span class="value">{{ taskData.title }}</span>
                </div>
                <div class="info-item">
                  <span class="label">任务状态：</span>
                  <el-tag :type="getStatusColor(taskData.status)">
                    {{ getStatusText(taskData.status) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">优先级：</span>
                  <el-tag :type="getPriorityColor(taskData.priority)">
                    {{ getPriorityText(taskData.priority) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">人员数量：</span>
                  <span class="value">{{ taskData.personnelCount }}人</span>
                </div>
                <div class="info-item">
                  <span class="label">处理人：</span>
                  <span class="value">{{ taskData.assignedToUserName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">创建时间：</span>
                  <span class="value">{{ taskData.createdAt }}</span>
                </div>
                <div class="info-item">
                  <span class="label">截止时间：</span>
                  <span class="value" :class="{ 'overdue-text': taskData.isOverdue }">
                    {{ taskData.dueDate }}
                    <span v-if="taskData.isOverdue" class="overdue-badge">
                      逾期{{ taskData.overdueBy }}天
                    </span>
                  </span>
                </div>
              </div>
              
              <div v-if="taskData.description" class="description-section">
                <h5>任务描述</h5>
                <p class="description-text">{{ taskData.description }}</p>
              </div>
              
              <div v-if="taskData.processingRequirement" class="requirement-section">
                <h5>处理要求</h5>
                <p class="requirement-text">{{ taskData.processingRequirement }}</p>
              </div>
            </div>

            <div class="progress-section">
              <h4>任务进度</h4>
              <div class="progress-container">
                <el-progress
                  :percentage="taskData.progress.percentage"
                  :stroke-width="12"
                  :status="getProgressStatus(taskData)"
                />
                <div class="progress-stats">
                  <span>已完成：{{ taskData.progress.completed }}人</span>
                  <span>总计：{{ taskData.progress.total }}人</span>
                  <span>进度：{{ taskData.progress.percentage }}%</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- Tab 2: 处理历史 -->
        <el-tab-pane label="处理历史" name="history">
          <div class="tab-content">
            <el-timeline>
              <el-timeline-item
                v-for="(history, index) in mockHistory"
                :key="index"
                :timestamp="history.time"
                placement="top"
                :type="getHistoryType(history.type)"
              >
                <div class="history-item">
                  <div class="history-content">{{ history.content }}</div>
                  <div class="history-operator">操作人：{{ history.operator }}</div>
                  <div v-if="history.note" class="history-note">{{ history.note }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>

        <!-- Tab 3: 关联人员 -->
        <el-tab-pane label="关联人员" name="personnel">
          <div class="tab-content">
            <!-- 人员搜索 -->
            <div class="personnel-search">
              <el-form :model="personnelSearchForm" inline>
                <el-form-item label="姓名">
                  <el-input
                    v-model="personnelSearchForm.name"
                    placeholder="请输入姓名"
                    clearable
                    @clear="handlePersonnelSearch"
                    @keyup.enter="handlePersonnelSearch"
                  />
                </el-form-item>
                <el-form-item label="身份证号">
                  <el-input
                    v-model="personnelSearchForm.idCard"
                    placeholder="请输入身份证号"
                    clearable
                    @clear="handlePersonnelSearch"
                    @keyup.enter="handlePersonnelSearch"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handlePersonnelSearch">搜索</el-button>
                  <el-button @click="handlePersonnelReset">重置</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 人员列表 -->
            <el-table :data="filteredPersonnelData" border>
              <el-table-column label="照片" width="80">
                <template #default="scope">
                  <PersonnelAvatar
                    :src="scope.row.avatar"
                    :name="scope.row.name"
                    size="small"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="name" label="姓名" width="120" />
              <el-table-column label="身份证号" width="180">
                <template #default="scope">
                  {{ maskIdCard(scope.row.idCard) }}
                </template>
              </el-table-column>
              <el-table-column prop="phone" label="联系方式" width="140" />
              <el-table-column prop="organization" label="所属机构" width="150" />
              <el-table-column label="异常类型" width="120">
                <template #default="scope">
                  <el-tag :type="getAbnormalTypeColor(scope.row.abnormalType)">
                    {{ getAbnormalTypeLabel(scope.row.abnormalType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="处理状态" width="100">
                <template #default="scope">
                  <el-tag :type="getProcessingStatusColor(scope.row.processingStatus)">
                    {{ getProcessingStatusLabel(scope.row.processingStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="scope">
                  <el-button type="text" size="small" @click="handleViewPersonnelDetail(scope.row)">
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 人员分页 -->
            <div class="personnel-pagination">
              <el-pagination
                v-model:current-page="personnelPagination.page"
                v-model:page-size="personnelPagination.size"
                :page-sizes="[10, 20, 50]"
                :total="personnelPagination.total"
                layout="total, sizes, prev, pager, next"
                @size-change="handlePersonnelSizeChange"
                @current-change="handlePersonnelCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="taskData?.status !== 'cancelled' && taskData?.status !== 'completed'"
          type="danger"
          @click="handleCancelTask"
        >
          撤销任务
        </el-button>
        <el-button
          v-if="taskData?.isOverdue"
          type="warning"
          @click="handleRemindTask"
        >
          催办
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import PersonnelAvatar from '@/components/common/PersonnelAvatar.vue'
import { 
  processingTaskMockData, 
  abnormalPersonnelMockData,
  type ProcessingTask,
  type AbnormalPersonnel
} from '@/data/resultProcessingMockData'
import {
  taskStatusConfig,
  priorityConfig,
  abnormalTypeConfig,
  processingStatusConfig
} from '@/config/resultProcessingConfig'

// Props
interface Props {
  modelValue: boolean
  taskId?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskId: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'cancel-task': [taskId: string]
  'remind-task': [taskId: string]
}>()

// 响应式数据
const activeTab = ref('info')
const taskData = ref<ProcessingTask | null>(null)
const personnelData = ref<AbnormalPersonnel[]>([])
const filteredPersonnelData = ref<AbnormalPersonnel[]>([])

// 人员搜索
const personnelSearchForm = ref({
  name: '',
  idCard: ''
})

// 人员分页
const personnelPagination = ref({
  page: 1,
  size: 10,
  total: 0
})

// 计算属性
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟处理历史数据
const mockHistory = computed(() => {
  if (!taskData.value) return []
  
  const history = [
    {
      time: taskData.value.createdAt,
      type: 'create',
      content: `创建处理任务：${taskData.value.title}`,
      operator: taskData.value.createdByName
    }
  ]
  
  if (taskData.value.assignedAt) {
    history.push({
      time: taskData.value.assignedAt,
      type: 'assign',
      content: `任务已分配给 ${taskData.value.assignedToUserName}`,
      operator: taskData.value.createdByName
    })
  }
  
  if (taskData.value.reminderCount && taskData.value.reminderCount > 0) {
    history.push({
      time: taskData.value.lastReminderAt || taskData.value.createdAt,
      type: 'remind',
      content: `催办任务处理进度（第${taskData.value.reminderCount}次）`,
      operator: '系统管理员'
    })
  }
  
  if (taskData.value.completedAt) {
    history.push({
      time: taskData.value.completedAt,
      type: 'complete',
      content: '任务处理完成',
      operator: taskData.value.assignedToUserName || '处理员'
    })
  }
  
  return history.reverse() // 最新的在前面
})

// 工具函数
const getStatusText = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.label || status
}

const getStatusColor = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.color || 'info'
}

const getPriorityText = (priority: string) => {
  return priorityConfig[priority as keyof typeof priorityConfig]?.label || priority
}

const getPriorityColor = (priority: string) => {
  return priorityConfig[priority as keyof typeof priorityConfig]?.color || 'info'
}

const getProgressStatus = (task: ProcessingTask) => {
  if (task.isOverdue) return 'exception'
  if (task.progress.percentage === 100) return 'success'
  return undefined
}

const getHistoryType = (type: string) => {
  const typeMap = {
    'create': 'primary',
    'assign': 'success',
    'remind': 'warning',
    'complete': 'success',
    'cancel': 'danger'
  }
  return typeMap[type as keyof typeof typeMap] || 'primary'
}

const maskIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const getAbnormalTypeLabel = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.label || type
}

const getAbnormalTypeColor = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.color || 'info'
}

const getProcessingStatusLabel = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.label || status
}

const getProcessingStatusColor = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.color || 'info'
}

// 加载任务数据
const loadTaskData = () => {
  if (!props.taskId) return
  
  const task = processingTaskMockData.find(t => t.id === props.taskId)
  if (task) {
    taskData.value = task
    
    // 加载关联人员数据
    personnelData.value = abnormalPersonnelMockData.filter(p => 
      task.personnelIds.includes(p.id)
    )
    filteredPersonnelData.value = [...personnelData.value]
    personnelPagination.value.total = personnelData.value.length
  }
}

// 人员搜索和分页
const handlePersonnelSearch = () => {
  let filtered = [...personnelData.value]
  
  if (personnelSearchForm.value.name) {
    filtered = filtered.filter(p => 
      p.name.includes(personnelSearchForm.value.name)
    )
  }
  
  if (personnelSearchForm.value.idCard) {
    filtered = filtered.filter(p => 
      p.idCard.includes(personnelSearchForm.value.idCard)
    )
  }
  
  filteredPersonnelData.value = filtered
  personnelPagination.value.total = filtered.length
  personnelPagination.value.page = 1
}

const handlePersonnelReset = () => {
  personnelSearchForm.value = { name: '', idCard: '' }
  filteredPersonnelData.value = [...personnelData.value]
  personnelPagination.value.total = personnelData.value.length
  personnelPagination.value.page = 1
}

const handlePersonnelSizeChange = (size: number) => {
  personnelPagination.value.size = size
}

const handlePersonnelCurrentChange = (page: number) => {
  personnelPagination.value.page = page
}

const handleViewPersonnelDetail = (personnel: AbnormalPersonnel) => {
  ElMessage.info(`查看 ${personnel.name} 的详细信息`)
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  activeTab.value = 'info'
}

const handleCancelTask = () => {
  if (taskData.value) {
    emit('cancel-task', taskData.value.id)
  }
}

const handleRemindTask = () => {
  if (taskData.value) {
    emit('remind-task', taskData.value.id)
  }
}

// 监听taskId变化
watch(() => props.taskId, () => {
  if (props.modelValue && props.taskId) {
    loadTaskData()
  }
}, { immediate: true })

// 监听抽屉打开
watch(() => props.modelValue, (visible) => {
  if (visible && props.taskId) {
    loadTaskData()
  }
})
</script>

<style scoped>
.task-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-tabs {
  flex: 1;
  display: flex;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow-y: auto;
}

.tab-content {
  padding: 20px 0;
}

.info-section {
  margin-bottom: 24px;
}

.info-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.info-section h5 {
  margin: 16px 0 8px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.value {
  color: #303133;
  flex: 1;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-badge {
  font-size: 12px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.description-section,
.requirement-section {
  margin-top: 16px;
}

.description-text,
.requirement-text {
  color: #606266;
  line-height: 1.6;
  margin: 0;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.progress-section {
  margin-top: 24px;
}

.progress-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.progress-container {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  font-size: 14px;
  color: #606266;
}

.history-item {
  padding: 8px 0;
}

.history-content {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: #909399;
}

.history-note {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.personnel-search {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.personnel-pagination {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.drawer-footer {
  text-align: right;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .progress-stats {
    flex-direction: column;
    gap: 4px;
  }

  .personnel-search :deep(.el-form--inline .el-form-item) {
    display: block;
    margin-right: 0;
    margin-bottom: 12px;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: #fafafa;
}

/* 时间线样式优化 */
:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 20px;
}
</style>
