<!-- 
  异常人员详情抽屉组件
  基于PersonnelDetailDrawer设计，用于异常人员详情展示
-->
<template>
  <el-drawer
    v-model="visible"
    title="异常人员详情"
    :size="600"
    direction="rtl"
    :before-close="handleClose"
  >
    <div v-if="personnelData" class="drawer-content">
      <!-- 人员基本信息头部 -->
      <div class="personnel-header">
        <div class="avatar-section">
          <PersonnelAvatar
            :src="personnelData.avatar"
            :name="personnelData.name"
            :size="60"
          />
        </div>
        <div class="basic-info">
          <h3 class="personnel-name">{{ personnelData.name }}</h3>
          <div class="personnel-meta">
            <el-tag :type="getAbnormalLevelColor(personnelData.abnormalLevel)" size="small">
              {{ getAbnormalLevelText(personnelData.abnormalLevel) }}等级异常
            </el-tag>
            <el-tag 
              :type="getProcessingStatusColor(personnelData.processingStatus)" 
              size="small"
              class="status-tag"
            >
              {{ getProcessingStatusText(personnelData.processingStatus) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <div class="info-section">
            <div class="info-grid">
              <div class="info-item">
                <label>姓名</label>
                <span>{{ personnelData.name }}</span>
              </div>
              <div class="info-item">
                <label>身份证号</label>
                <span>{{ maskIdCard(personnelData.idCard) }}</span>
              </div>
              <div class="info-item">
                <label>联系方式</label>
                <span>{{ maskPhone(personnelData.phone) }}</span>
              </div>
              <div class="info-item">
                <label>所属机构</label>
                <span>{{ personnelData.organization }}</span>
              </div>
              <div class="info-item">
                <label>异常类型</label>
                <el-tag :type="getAbnormalTypeColor(personnelData.abnormalType)" size="small">
                  {{ getAbnormalTypeText(personnelData.abnormalType) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>异常等级</label>
                <el-tag :type="getAbnormalLevelColor(personnelData.abnormalLevel)" size="small">
                  {{ getAbnormalLevelText(personnelData.abnormalLevel) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>发现时间</label>
                <span>{{ personnelData.discoveredAt }}</span>
              </div>
              <div class="info-item">
                <label>背审任务</label>
                <span>{{ personnelData.backgroundCheckTaskNo }}</span>
              </div>
              <div class="info-item full-width">
                <label>异常描述</label>
                <span>{{ personnelData.abnormalDescription }}</span>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="处理状态" name="status">
          <div class="info-section">
            <div v-if="personnelData.processingStatus === 'processing'" class="current-task">
              <h4>当前处理任务</h4>
              <div class="task-info">
                <div class="info-item">
                  <label>任务编号</label>
                  <span>{{ personnelData.processingTaskId || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>处理状态</label>
                  <el-tag type="primary" size="small">处理中</el-tag>
                </div>
                <div class="info-item">
                  <label>处理人</label>
                  <span>{{ personnelData.assignedToName || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>分配时间</label>
                  <span>{{ personnelData.assignedAt || '-' }}</span>
                </div>
              </div>
            </div>

            <div v-else-if="personnelData.processingStatus === 'completed'" class="completed-task">
              <h4>处理结果</h4>
              <div class="task-info">
                <div class="info-item">
                  <label>处理状态</label>
                  <el-tag type="success" size="small">已完成</el-tag>
                </div>
                <div class="info-item">
                  <label>处理人</label>
                  <span>{{ personnelData.assignedToName || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>完成时间</label>
                  <span>{{ personnelData.processedAt || '-' }}</span>
                </div>
                <div class="info-item full-width" v-if="personnelData.processingResult">
                  <label>处理结果</label>
                  <span>{{ personnelData.processingResult }}</span>
                </div>
                <div class="info-item full-width" v-if="personnelData.processingNote">
                  <label>处理说明</label>
                  <span>{{ personnelData.processingNote }}</span>
                </div>
              </div>
            </div>

            <div v-else class="pending-task">
              <h4>待处理状态</h4>
              <div class="task-info">
                <div class="info-item">
                  <label>当前状态</label>
                  <el-tag type="info" size="small">待处理</el-tag>
                </div>
                <div class="info-item">
                  <label>发现时间</label>
                  <span>{{ personnelData.discoveredAt }}</span>
                </div>
                <div class="info-item">
                  <label>背审结果</label>
                  <span>{{ personnelData.backgroundCheckResult }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="处理历史" name="history">
          <div class="info-section">
            <el-timeline>
              <el-timeline-item
                v-for="(history, index) in mockHistory"
                :key="index"
                :timestamp="history.time"
                placement="top"
                :type="getHistoryType(history.type)"
              >
                <div class="history-item">
                  <div class="history-content">{{ history.content }}</div>
                  <div class="history-operator">操作人：{{ history.operator }}</div>
                  <div v-if="history.note" class="history-note">{{ history.note }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import PersonnelAvatar from '@/components/common/PersonnelAvatar.vue'
import { 
  abnormalPersonnelMockData,
  type AbnormalPersonnel 
} from '@/data/resultProcessingMockData'
import {
  abnormalTypeConfig,
  abnormalLevelConfig,
  processingStatusConfig
} from '@/config/resultProcessingConfig'

// Props
interface Props {
  modelValue: boolean
  personnelId?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  personnelId: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'view-task': [taskId: string]
}>()

// 响应式数据
const activeTab = ref('basic')
const personnelData = ref<AbnormalPersonnel | null>(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟操作历史数据
const mockHistory = computed(() => {
  if (!personnelData.value) return []
  
  const history = [
    {
      time: personnelData.value.discoveredAt,
      type: 'discover',
      content: `发现异常：${personnelData.value.abnormalDescription}`,
      operator: '系统自动检测'
    }
  ]
  
  if (personnelData.value.assignedAt) {
    history.push({
      time: personnelData.value.assignedAt,
      type: 'assign',
      content: `任务已分配给 ${personnelData.value.assignedToName}`,
      operator: '张管理员'
    })
  }
  
  if (personnelData.value.processedAt) {
    history.push({
      time: personnelData.value.processedAt,
      type: 'complete',
      content: `处理完成：${personnelData.value.processingResult}`,
      operator: personnelData.value.assignedToName || '处理员'
    })
  }
  
  return history.reverse() // 最新的在前面
})

// 工具函数
const maskIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const maskPhone = (phone: string) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

const getAbnormalTypeText = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.label || type
}

const getAbnormalTypeColor = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.color || 'info'
}

const getAbnormalLevelText = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.label || level
}

const getAbnormalLevelColor = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.color || 'info'
}

const getProcessingStatusText = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.label || status
}

const getProcessingStatusColor = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.color || 'info'
}

const getHistoryType = (type: string) => {
  const typeMap = {
    'discover': 'primary',
    'assign': 'success',
    'complete': 'success',
    'cancel': 'danger'
  }
  return typeMap[type as keyof typeof typeMap] || 'primary'
}

// 加载人员数据
const loadPersonnelData = () => {
  if (!props.personnelId) return
  
  const personnel = abnormalPersonnelMockData.find(p => p.id === props.personnelId)
  if (personnel) {
    personnelData.value = personnel
  }
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  activeTab.value = 'basic'
}

// 监听personnelId变化
watch(() => props.personnelId, () => {
  if (props.modelValue && props.personnelId) {
    loadPersonnelData()
  }
}, { immediate: true })

// 监听抽屉打开
watch(() => props.modelValue, (visible) => {
  if (visible && props.personnelId) {
    loadPersonnelData()
  }
})
</script>

<style scoped>
.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.personnel-header {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.avatar-section {
  margin-right: 16px;
}

.basic-info {
  flex: 1;
}

.personnel-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.personnel-meta {
  display: flex;
  gap: 8px;
}

.status-tag {
  margin-left: 8px;
}

.detail-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow-y: auto;
}

.info-section {
  padding: 20px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

.current-task,
.completed-task,
.pending-task {
  margin-bottom: 24px;
}

.current-task h4,
.completed-task h4,
.pending-task h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.task-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.history-item {
  padding: 8px 0;
}

.history-content {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: #909399;
}

.history-note {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .personnel-header {
    flex-direction: column;
    text-align: center;
  }
  
  .avatar-section {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .info-grid,
  .task-info {
    grid-template-columns: 1fr;
  }
  
  .info-item.full-width {
    grid-column: 1;
  }
}

/* 时间线样式优化 */
:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 20px;
}

:deep(.el-timeline-item__node) {
  width: 8px;
  height: 8px;
}
</style>
