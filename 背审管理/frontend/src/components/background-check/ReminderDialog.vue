<!-- 
  催办弹窗组件
  可复用组件：适用于背审结果处理模块
-->
<template>
  <el-dialog
    v-model="visible"
    title="发送催办通知"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="催办类型" prop="reminderType">
        <el-radio-group v-model="formData.reminderType">
          <el-radio label="overdue">逾期催办</el-radio>
          <el-radio label="deadline_approaching">截止提醒</el-radio>
          <el-radio label="manual">手动催办</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="催办内容" prop="content">
        <el-input
          v-model="formData.content"
          type="textarea"
          :rows="4"
          placeholder="请输入催办内容"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="发送给" prop="sentToOrg">
        <el-select
          v-model="formData.sentToOrg"
          placeholder="请选择接收部门"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="dept in departments"
            :key="dept.value"
            :label="dept.label"
            :value="dept.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="指定人员" prop="sentToUser">
        <el-select
          v-model="formData.sentToUser"
          placeholder="请选择具体人员（可选）"
          style="width: 100%"
          filterable
          clearable
        >
          <el-option
            v-for="user in users"
            :key="user.value"
            :label="user.label"
            :value="user.value"
          />
        </el-select>
        <div class="form-tip">
          <el-text size="small" type="info">
            不选择具体人员时，将发送给整个部门
          </el-text>
        </div>
      </el-form-item>
      
      <el-form-item label="紧急程度" prop="urgency">
        <el-radio-group v-model="formData.urgency">
          <el-radio label="normal">普通</el-radio>
          <el-radio label="urgent">紧急</el-radio>
          <el-radio label="critical">非常紧急</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 预览区域 -->
    <div class="preview-section">
      <h4>催办通知预览</h4>
      <div class="preview-content">
        <div class="preview-header">
          <el-tag :type="getUrgencyColor(formData.urgency)" size="small">
            {{ getUrgencyText(formData.urgency) }}
          </el-tag>
          <span class="preview-type">{{ getReminderTypeText(formData.reminderType) }}</span>
        </div>
        <div class="preview-body">
          {{ formData.content || '请输入催办内容...' }}
        </div>
        <div class="preview-footer">
          <div class="preview-meta">
            发送给：{{ getSelectedDeptName() }}{{ formData.sentToUser ? ` - ${getSelectedUserName()}` : '' }}
          </div>
          <div class="preview-time">
            发送时间：{{ new Date().toLocaleString() }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          发送催办
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
  taskId?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [reminderData: any]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive({
  reminderType: 'overdue',
  content: '',
  sentToOrg: '',
  sentToUser: '',
  urgency: 'normal'
})

// 表单验证规则
const formRules: FormRules = {
  reminderType: [
    { required: true, message: '请选择催办类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入催办内容', trigger: 'blur' },
    { min: 10, max: 500, message: '催办内容长度在 10 到 500 个字符', trigger: 'blur' }
  ],
  sentToOrg: [
    { required: true, message: '请选择接收部门', trigger: 'change' }
  ]
}

// Mock数据
const departments = [
  { label: '莲池分局', value: 'org-001' },
  { label: '竞秀分局', value: 'org-002' },
  { label: '满城分局', value: 'org-003' },
  { label: '清苑分局', value: 'org-004' },
  { label: '徐水分局', value: 'org-005' }
]

const users = [
  { label: '李审查员', value: 'user-001' },
  { label: '王审查员', value: 'user-002' },
  { label: '张审查员', value: 'user-003' },
  { label: '刘审查员', value: 'user-004' }
]

// 计算属性
const getSelectedDeptName = () => {
  const dept = departments.find(d => d.value === formData.sentToOrg)
  return dept?.label || '未选择'
}

const getSelectedUserName = () => {
  const user = users.find(u => u.value === formData.sentToUser)
  return user?.label || ''
}

// 工具函数
const getUrgencyColor = (urgency: string) => {
  const colors = {
    normal: 'info',
    urgent: 'warning',
    critical: 'danger'
  }
  return colors[urgency] || 'info'
}

const getUrgencyText = (urgency: string) => {
  const texts = {
    normal: '普通',
    urgent: '紧急',
    critical: '非常紧急'
  }
  return texts[urgency] || '普通'
}

const getReminderTypeText = (type: string) => {
  const texts = {
    overdue: '逾期催办',
    deadline_approaching: '截止提醒',
    manual: '手动催办'
  }
  return texts[type] || '催办通知'
}

// 事件处理
const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    reminderType: 'overdue',
    content: '',
    sentToOrg: '',
    sentToUser: '',
    urgency: 'normal'
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 提交数据
    const reminderData = {
      ...formData,
      taskId: props.taskId,
      sentAt: new Date().toISOString()
    }
    
    emit('submit', reminderData)
    
    ElMessage.success('催办通知发送成功')
    handleClose()
    
  } catch (error) {
    console.error('发送催办通知失败:', error)
    ElMessage.error('发送催办通知失败')
  } finally {
    submitting.value = false
  }
}

// 监听催办类型变化，自动填充内容
const handleReminderTypeChange = () => {
  const templates = {
    overdue: '您好，您负责的背审任务已逾期，请尽快处理。如有问题请及时联系。',
    deadline_approaching: '您好，您负责的背审任务即将到期，请注意及时处理。',
    manual: '您好，请关注您负责的背审任务进度，如需协助请联系管理员。'
  }
  
  if (!formData.content) {
    formData.content = templates[formData.reminderType] || ''
  }
}

// 监听催办类型变化
watch(() => formData.reminderType, handleReminderTypeChange)
</script>

<style scoped>
.form-tip {
  margin-top: 4px;
}

.preview-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.preview-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
}

.preview-content {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background: #fafafa;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.preview-type {
  font-weight: 500;
  color: #303133;
}

.preview-body {
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border-radius: 4px;
  min-height: 60px;
  color: #303133;
  line-height: 1.5;
}

.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

.preview-meta {
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .preview-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
