<template>
  <div class="personnel-table">
    <el-table :data="data" :loading="loading" stripe border>
      <el-table-column prop="region" label="区域" width="150" />
      <el-table-column prop="name" label="姓名" width="100" />
      <el-table-column prop="idCard" label="身份证号" width="200" />
      <el-table-column prop="phone" label="手机号" width="130" />
      <el-table-column prop="organization" label="所属单位" />
      <el-table-column prop="entryDate" label="入职日期" width="120" />

      <!-- 在职状态 -->
      <el-table-column prop="status" label="在职状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getEmploymentStatusType(row.status)">
            {{ getEmploymentStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
  
      <!-- <el-table-column prop="status" label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column> -->
          <el-table-column prop="backgroundCheckResult" label="背审结果" width="150">
        <template #default="{ row }">
          <div class="background-check-result">
            <!-- 未审查或正常状态显示主标签 -->
            <el-tag
              v-if="row.backgroundCheckResult !== 2"
              :type="getBackgroundCheckResultType(row.backgroundCheckResult)"
              class="result-tag"
            >
              {{ getBackgroundCheckResultText(row.backgroundCheckResult) }}
            </el-tag>

            <!-- 异常状态直接显示异常类型标签 -->
            <div v-if="row.backgroundCheckResult === 2 && row.abnormalTypes && row.abnormalTypes.length > 0" class="abnormal-types">
              <el-tag
                v-for="type in row.abnormalTypes"
                :key="type"
                type="danger"
                class="abnormal-tag"
              >
                {{ getSingleAbnormalTypeText(type) }}
              </el-tag>
            </div>

            <!-- 异常但没有具体类型时显示异常标签 -->
            <el-tag
              v-if="row.backgroundCheckResult === 2 && (!row.abnormalTypes || row.abnormalTypes.length === 0)"
              type="danger"
              class="result-tag"
            >
              异常
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 处理状态 -->
      <el-table-column prop="processingStatus" label="处理状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getProcessingStatusType(row.processingStatus || 0)">
            {{ getProcessingStatusText(row.processingStatus || 0) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="medium" @click="$emit('view-detail', row.id)">
            查看
          </el-button>
          <!-- 只有异常人员才显示处理按钮 -->
          <el-button
            v-if="row.backgroundCheckResult === 2"
            type="danger"
            size="medium"
            @click="$emit('add-blacklist', row.id)"
          >
            处理
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import {
  getBackgroundCheckResultText,
  getSingleAbnormalTypeText,
  getEmploymentStatusText,
  getProcessingStatusText
} from '@/data/personnelMockData'

interface Props {
  data: any[]
  loading: boolean
}

interface Emits {
  (e: 'view-detail', id: number): void
  (e: 'add-blacklist', id: number): void
}

defineProps<Props>()
defineEmits<Emits>()

const getBackgroundCheckResultType = (result: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',    // 未审查
    1: 'success', // 正常
    2: 'danger'   // 异常
  }
  return typeMap[result] || 'info'
}

const getEmploymentStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    1: 'success', // 在职
    2: 'info'     // 离职
  }
  return typeMap[status] || 'info'
}

const getProcessingStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: '',        // 未处理
    1: 'info',    // 无需处理
    2: 'warning', // 重点关注
    3: 'danger'   // 调岗/劝退
  }
  return typeMap[status] || ''
}


</script>

<style scoped>
.personnel-table {
  width: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.background-check-result {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.result-tag {
  align-self: flex-start;
  font-weight: 500;
}

.abnormal-types {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}


:deep(.el-table__cell) {
  padding: 12px 0;
}

</style>