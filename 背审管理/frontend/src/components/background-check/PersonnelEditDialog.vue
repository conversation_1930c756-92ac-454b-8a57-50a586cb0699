<template>
  <el-dialog v-model="dialogVisible" title="编辑人员信息" width="800px" :before-close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formData.name" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="idCard">
            <el-input v-model="formData.idCard" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="formData.phone" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="民族" prop="ethnicity">
            <el-input v-model="formData.ethnicity" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="职位" prop="position">
            <el-input v-model="formData.position" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="保安公司" prop="securityCompany">
            <el-input v-model="formData.securityCompany" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学历" prop="education">
            <el-select v-model="formData.education" placeholder="请选择学历">
              <el-option label="小学" value="小学" />
              <el-option label="初中" value="初中" />
              <el-option label="高中" value="高中" />
              <el-option label="大专" value="大专" />
              <el-option label="本科" value="本科" />
              <el-option label="硕士" value="硕士" />
              <el-option label="博士" value="博士" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="区域" prop="region">
            <el-input v-model="formData.region" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="formData.address" type="textarea" :rows="2" />
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="政治面貌" prop="politicalStatus">
            <el-select v-model="formData.politicalStatus" placeholder="请选择政治面貌">
              <el-option label="群众" value="群众" />
              <el-option label="团员" value="团员" />
              <el-option label="党员" value="党员" />
              <el-option label="民主党派" value="民主党派" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="在职状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择在职状态">
              <el-option label="在职" :value="1" />
              <el-option label="离职" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属单位" prop="organization">
            <el-input v-model="formData.organization" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="人员类型" prop="personnelType">
            <el-select v-model="formData.personnelType" placeholder="请选择人员类型">
              <el-option label="专职保卫" :value="1" />
              <el-option label="保安人员" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">

        <el-col :span="12">
          <el-form-item label="背景审查结果" prop="backgroundCheckResult">
            <el-select v-model="formData.backgroundCheckResult" placeholder="请选择背景审查结果"
              @change="handleBackgroundCheckResultChange">
              <el-option label="未审查" :value="0" />
              <el-option label="正常" :value="1" />
              <el-option label="异常" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="formData.backgroundCheckResult === 2" label="异常类型" prop="abnormalTypes">
            <el-select v-model="formData.abnormalTypes" placeholder="请选择异常类型" multiple clearable style="width: 100%"
              collapse-tags collapse-tags-tooltip>
              <el-option v-for="item in abnormalTypeOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 异常类型选择 -->

    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { getPersonnelDetail, updatePersonnel } from '@/api/background-check'
import { backgroundCheckAbnormalTypes } from '@/data/personnelMockData'

interface Props {
  modelValue: boolean
  personnelId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = reactive({
  name: '',
  idCard: '',
  phone: '',
  ethnicity: '',
  position: '',
  securityCompany: '',
  education: '',
  region: '',
  address: '',
  politicalStatus: '',
  organization: '',
  personnelType: 1,
  status: 1,
  backgroundCheckResult: 0,
  processingStatus: 0,
  abnormalTypes: [] as string[]
})

// 异常类型选项
const abnormalTypeOptions = ref(backgroundCheckAbnormalTypes)

const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  organization: [{ required: true, message: '请输入所属单位', trigger: 'blur' }]
}

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(() => props.modelValue, (visible) => {
  if (visible && props.personnelId) {
    fetchPersonnelDetail()
  } else {
    resetForm()
  }
})

const fetchPersonnelDetail = async () => {
  if (!props.personnelId) return

  try {
    loading.value = true
    const response = await getPersonnelDetail(props.personnelId)
    Object.assign(formData, response.data)
  } catch (error) {
    console.error('获取人员详情失败:', error)
    ElMessage.error('获取人员详情失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    idCard: '',
    phone: '',
    ethnicity: '',
    position: '',
    securityCompany: '',
    education: '',
    region: '',
    address: '',
    politicalStatus: '',
    organization: '',
    personnelType: 1,
    status: 1,
    backgroundCheckResult: 0,
    processingStatus: 0,
    abnormalTypes: []
  })
  formRef.value?.clearValidate()
}

// 背景审查结果变化处理
const handleBackgroundCheckResultChange = (value: number) => {
  // 如果不是选择异常，清空异常类型
  if (value !== 2) {
    formData.abnormalTypes = []
  }

  // 如果背审结果为正常，自动设置为无需处理
  if (value === 1) {
    formData.processingStatus = 1 // 无需处理
  }
  // 如果背审结果为未审查，设置为未处理
  else if (value === 0) {
    formData.processingStatus = 0 // 未处理
  }
  // 如果背审结果为异常，保持当前处理状态或设置为未处理
  else if (value === 2) {
    // 如果当前是无需处理，改为未处理，让用户重新选择
    if (formData.processingStatus === 1) {
      formData.processingStatus = 0 // 未处理
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value || !props.personnelId) return

  try {
    await formRef.value.validate()
    loading.value = true
    await updatePersonnel({ id: props.personnelId, ...formData })
    ElMessage.success('保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>