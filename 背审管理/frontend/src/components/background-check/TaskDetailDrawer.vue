<!-- 任务详情抽屉组件 -->
<template>
  <el-drawer
    v-model="visible"
    title="任务详情"
    size="60%"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab" class="detail-tabs">
      <!-- 任务信息 -->
      <el-tab-pane label="任务信息" name="info">
        <div class="task-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务编号">
              {{ taskDetail.taskNo }}
            </el-descriptions-item>
            <el-descriptions-item label="任务标题">
              {{ taskDetail.title }}
            </el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag :type="getStatusColor(taskDetail.status)" size="small">
                {{ getStatusText(taskDetail.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityColor(taskDetail.priority)" size="small">
                {{ getPriorityText(taskDetail.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="人员数量">
              {{ taskDetail.personnelCount }}人
            </el-descriptions-item>
            <el-descriptions-item label="处理人">
              {{ taskDetail.assignedToUserName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ taskDetail.createTime }}
            </el-descriptions-item>
            <el-descriptions-item label="截止时间">
              <span :class="{ 'overdue-text': taskDetail.isOverdue }">
                {{ taskDetail.dueDate }}
                <span v-if="taskDetail.isOverdue" class="overdue-badge">
                  逾期{{ taskDetail.overdueBy }}天
                </span>
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="任务描述" :span="2">
              {{ taskDetail.description || '-' }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 进度信息 -->
          <div class="progress-section">
            <h4>任务进度</h4>
            <el-progress 
              :percentage="taskDetail.progress?.percentage || 0" 
              :stroke-width="12"
              :status="getProgressStatus(taskDetail)"
            />
            <div class="progress-detail">
              已完成：{{ taskDetail.progress?.completed || 0 }}人 / 
              总计：{{ taskDetail.progress?.total || 0 }}人
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 催办历史 -->
      <el-tab-pane label="催办历史" name="reminder">
        <div class="reminder-history">
          <el-table :data="reminderHistory" :loading="reminderLoading">
            <el-table-column prop="reminderTime" label="催办时间" width="180" />
            <el-table-column prop="reminderUser" label="催办人" width="120" />
            <el-table-column label="是否已读" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.isRead ? 'success' : 'warning'" size="small">
                  {{ scope.row.isRead ? '已读' : '未读' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="feedback" label="反馈内容" min-width="200">
              <template #default="scope">
                {{ scope.row.feedback || '-' }}
              </template>
            </el-table-column>
          </el-table>
          
          <div v-if="reminderHistory.length === 0" class="empty-state">
            <el-empty description="暂无催办记录" />
          </div>
        </div>
      </el-tab-pane>

      <!-- 关联人员 -->
      <el-tab-pane label="关联人员" name="personnel">
        <div class="personnel-section">
          <!-- 人员搜索 -->
          <div class="personnel-search">
            <el-form :model="personnelSearchForm" inline>
              <el-form-item label="姓名">
                <el-input
                  v-model="personnelSearchForm.name"
                  placeholder="请输入姓名"
                  clearable
                  @keyup.enter="handlePersonnelSearch"
                />
              </el-form-item>
              <el-form-item label="身份证号">
                <el-input
                  v-model="personnelSearchForm.idCard"
                  placeholder="请输入身份证号"
                  clearable
                  @keyup.enter="handlePersonnelSearch"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handlePersonnelSearch">搜索</el-button>
                <el-button @click="handlePersonnelReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 人员列表 -->
          <el-table :data="personnelList" :loading="personnelLoading">
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="idCard" label="身份证号" width="180">
              <template #default="scope">
                {{ maskIdCard(scope.row.idCard) }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系方式" width="140" />
            <el-table-column prop="organization" label="所属机构" min-width="150" />
            <el-table-column prop="status" label="背审状态" width="120">
              <template #default="scope">
                <el-tag :type="getPersonnelStatusColor(scope.row.status)" size="small">
                  {{ getPersonnelStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <!-- 人员分页 -->
          <div class="personnel-pagination">
            <el-pagination
              v-model:current-page="personnelPagination.page"
              v-model:page-size="personnelPagination.size"
              :page-sizes="[10, 20, 50]"
              :total="personnelPagination.total"
              layout="total, sizes, prev, pager, next"
              @size-change="handlePersonnelSizeChange"
              @current-change="handlePersonnelCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  taskId?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeTab = ref('info')
const taskDetail = ref<any>({})
const reminderHistory = ref<any[]>([])
const reminderLoading = ref(false)
const personnelList = ref<any[]>([])
const personnelLoading = ref(false)

// 人员搜索表单
const personnelSearchForm = reactive({
  name: '',
  idCard: ''
})

// 人员分页
const personnelPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 监听taskId变化，获取任务详情
watch(() => props.taskId, (newTaskId) => {
  if (newTaskId && visible.value) {
    fetchTaskDetail()
    fetchReminderHistory()
    fetchPersonnelList()
  }
}, { immediate: true })

// 监听抽屉打开状态
watch(visible, (newVisible) => {
  if (newVisible && props.taskId) {
    fetchTaskDetail()
    fetchReminderHistory()
    fetchPersonnelList()
  }
})

// 获取任务详情
const fetchTaskDetail = async () => {
  try {
    // 这里应该调用真实的API
    // 暂时使用Mock数据
    taskDetail.value = {
      taskNo: 'TASK-2024-001',
      title: '新员工背景调查',
      status: 'in_progress',
      priority: 'high',
      personnelCount: 15,
      assignedToUserName: '张审查员',
      createTime: '2024-01-15 09:30:00',
      dueDate: '2024-01-25 18:00:00',
      description: '对新入职员工进行全面的背景调查，包括学历验证、工作经历核实等。',
      isOverdue: false,
      overdueBy: 0,
      progress: {
        completed: 8,
        total: 15,
        percentage: 53
      }
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
  }
}

// 获取催办历史
const fetchReminderHistory = async () => {
  try {
    reminderLoading.value = true
    // 这里应该调用真实的API
    // 暂时使用Mock数据
    reminderHistory.value = [
      {
        reminderTime: '2024-01-20 14:30:00',
        reminderUser: '李主管',
        isRead: true,
        feedback: '已收到催办通知，正在加快处理进度'
      },
      {
        reminderTime: '2024-01-18 10:15:00',
        reminderUser: '王经理',
        isRead: false,
        feedback: ''
      }
    ]
  } catch (error) {
    console.error('获取催办历史失败:', error)
    ElMessage.error('获取催办历史失败')
  } finally {
    reminderLoading.value = false
  }
}

// 获取关联人员列表
const fetchPersonnelList = async () => {
  try {
    personnelLoading.value = true
    // 这里应该调用真实的API
    // 暂时使用Mock数据
    const mockData = [
      {
        name: '张三',
        idCard: '110101199001011234',
        phone: '13800138001',
        organization: '技术部',
        status: 'completed'
      },
      {
        name: '李四',
        idCard: '110101199002022345',
        phone: '13800138002',
        organization: '市场部',
        status: 'in_progress'
      }
    ]
    
    // 应用搜索条件
    let filteredData = [...mockData]
    if (personnelSearchForm.name) {
      filteredData = filteredData.filter(item => 
        item.name.includes(personnelSearchForm.name)
      )
    }
    if (personnelSearchForm.idCard) {
      filteredData = filteredData.filter(item => 
        item.idCard.includes(personnelSearchForm.idCard)
      )
    }
    
    personnelPagination.total = filteredData.length
    
    // 分页
    const start = (personnelPagination.page - 1) * personnelPagination.size
    const end = start + personnelPagination.size
    personnelList.value = filteredData.slice(start, end)
    
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
  } finally {
    personnelLoading.value = false
  }
}

// 工具函数
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'info',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待分配',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return colors[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '普通',
    medium: '中等',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || '未知'
}

const getProgressStatus = (task: any) => {
  if (task.isOverdue) return 'exception'
  if (task.progress?.percentage === 100) return 'success'
  return undefined
}

const getPersonnelStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'info',
    in_progress: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return colors[status] || 'info'
}

const getPersonnelStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待背审',
    in_progress: '背审中',
    completed: '已完成',
    failed: '背审失败'
  }
  return texts[status] || '未知'
}

const maskIdCard = (idCard: string) => {
  if (!idCard || idCard.length < 8) return idCard
  return idCard.slice(0, 6) + '****' + idCard.slice(-4)
}

// 事件处理
const handleClose = () => {
  visible.value = false
}

const handlePersonnelSearch = () => {
  personnelPagination.page = 1
  fetchPersonnelList()
}

const handlePersonnelReset = () => {
  personnelSearchForm.name = ''
  personnelSearchForm.idCard = ''
  personnelPagination.page = 1
  fetchPersonnelList()
}

const handlePersonnelSizeChange = (size: number) => {
  personnelPagination.size = size
  personnelPagination.page = 1
  fetchPersonnelList()
}

const handlePersonnelCurrentChange = (page: number) => {
  personnelPagination.page = page
  fetchPersonnelList()
}
</script>

<style scoped>
.detail-tabs {
  height: 100%;
}

.task-info {
  padding: 20px 0;
}

.progress-section {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.progress-detail {
  margin-top: 12px;
  color: #606266;
  font-size: 14px;
}

.reminder-history {
  padding: 20px 0;
}

.empty-state {
  padding: 40px 0;
}

.personnel-section {
  padding: 20px 0;
}

.personnel-search {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.personnel-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-badge {
  font-size: 10px;
  color: #f56c6c;
  background: #fef0f0;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 4px;
}
</style>
