<!-- 
  统计详情弹窗组件
  可复用组件：适用于背审人员管理和背审结果处理模块
-->
<template>
  <el-dialog
    v-model="visible"
    title="背审统计详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="statistics-content">
      <!-- 总体统计 -->
      <div class="stats-section">
        <h4 class="section-title">总体统计</h4>
        <div class="total-stats">
          <div class="stat-card" v-for="item in totalStats" :key="item.key">
            <div class="stat-number" :class="item.type">{{ item.count }}</div>
            <div class="stat-label">{{ item.label }}</div>
            <div class="stat-percentage">{{ item.percentage }}%</div>
          </div>
        </div>
      </div>

      <!-- 按人员类型统计 -->
      <div class="stats-section">
        <h4 class="section-title">按人员类型统计</h4>
        <el-table :data="personnelTypeStats" style="width: 100%">
          <el-table-column prop="type" label="人员类型" width="120" />
          <el-table-column prop="total" label="总数" width="80" align="center" />
          <el-table-column prop="pending" label="待背审" width="80" align="center" />
          <el-table-column prop="inProgress" label="背审中" width="80" align="center" />
          <el-table-column prop="completed" label="已完成" width="80" align="center" />
          <el-table-column label="完成率" width="100" align="center">
            <template #default="scope">
              <el-progress 
                :percentage="scope.row.completionRate" 
                :stroke-width="8"
                :show-text="false"
              />
              <span class="completion-text">{{ scope.row.completionRate }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 按组织统计 -->
      <div class="stats-section">
        <h4 class="section-title">按组织统计</h4>
        <el-table :data="organizationStats" style="width: 100%" max-height="300">
          <el-table-column prop="organization" label="组织名称" width="150" />
          <el-table-column prop="total" label="总数" width="80" align="center" />
          <el-table-column prop="pending" label="待背审" width="80" align="center" />
          <el-table-column prop="inProgress" label="背审中" width="80" align="center" />
          <el-table-column prop="completed" label="已完成" width="80" align="center" />
          <el-table-column label="完成率" width="100" align="center">
            <template #default="scope">
              <el-progress 
                :percentage="scope.row.completionRate" 
                :stroke-width="8"
                :show-text="false"
              />
              <span class="completion-text">{{ scope.row.completionRate }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 风险等级分布 -->
      <div class="stats-section">
        <h4 class="section-title">风险等级分布</h4>
        <div class="risk-stats">
          <div class="risk-grid">
            <div class="risk-item" v-for="item in riskStats" :key="item.level">
              <div class="risk-header">
                <el-tag :type="item.tagType" size="large">{{ item.label }}</el-tag>
                <div class="risk-trend" :class="item.trend.type">
                  <el-icon>
                    <ArrowUp v-if="item.trend.type === 'up'" />
                    <ArrowDown v-if="item.trend.type === 'down'" />
                    <Minus v-if="item.trend.type === 'stable'" />
                  </el-icon>
                  {{ item.trend.value }}
                </div>
              </div>
              <div class="risk-numbers">
                <div class="risk-count">{{ item.count }}人</div>
                <div class="risk-percentage">{{ item.percentage }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间趋势 -->
      <div class="stats-section">
        <h4 class="section-title">背审完成趋势</h4>
        <div class="trend-chart">
          <div class="chart-placeholder">
            <el-icon size="48" color="#c0c4cc">
              <TrendCharts />
            </el-icon>
            <div class="chart-text">趋势图表（此处可集成图表组件）</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleExport" type="primary">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  ArrowUp, 
  ArrowDown, 
  Minus, 
  Download, 
  TrendCharts 
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  statistics: {
    total: number
    pending: number
    inProgress: number
    completed: number
    risk?: number
    overdue?: number
  }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 总体统计数据
const totalStats = computed(() => [
  {
    key: 'total',
    label: '总人数',
    count: props.statistics.total,
    percentage: 100,
    type: 'primary'
  },
  {
    key: 'pending',
    label: '待背审',
    count: props.statistics.pending,
    percentage: Math.round((props.statistics.pending / props.statistics.total) * 100),
    type: 'warning'
  },
  {
    key: 'inProgress',
    label: '背审中',
    count: props.statistics.inProgress,
    percentage: Math.round((props.statistics.inProgress / props.statistics.total) * 100),
    type: 'info'
  },
  {
    key: 'completed',
    label: '已完成',
    count: props.statistics.completed,
    percentage: Math.round((props.statistics.completed / props.statistics.total) * 100),
    type: 'success'
  }
])

// Mock数据 - 按人员类型统计
const personnelTypeStats = [
  {
    type: '专职保卫',
    total: 456,
    pending: 45,
    inProgress: 12,
    completed: 399,
    completionRate: 87
  },
  {
    type: '保安人员',
    total: 678,
    pending: 67,
    inProgress: 28,
    completed: 583,
    completionRate: 86
  },
  {
    type: '物流人员',
    total: 100,
    pending: 11,
    inProgress: 5,
    completed: 84,
    completionRate: 84
  }
]

// Mock数据 - 按组织统计
const organizationStats = [
  {
    organization: '莲池分局',
    total: 234,
    pending: 23,
    inProgress: 8,
    completed: 203,
    completionRate: 87
  },
  {
    organization: '竞秀分局',
    total: 345,
    pending: 34,
    inProgress: 15,
    completed: 296,
    completionRate: 86
  },
  {
    organization: '满城分局',
    total: 123,
    pending: 12,
    inProgress: 5,
    completed: 106,
    completionRate: 86
  },
  {
    organization: '清苑分局',
    total: 189,
    pending: 19,
    inProgress: 8,
    completed: 162,
    completionRate: 86
  },
  {
    organization: '徐水分局',
    total: 143,
    pending: 15,
    inProgress: 9,
    completed: 119,
    completionRate: 83
  }
]

// Mock数据 - 风险等级分布
const riskStats = [
  {
    level: 'low',
    label: '低风险',
    count: 1156,
    percentage: 93.7,
    tagType: 'success',
    trend: {
      type: 'down',
      value: '2%'
    }
  },
  {
    level: 'medium',
    label: '中风险',
    count: 70,
    percentage: 5.7,
    tagType: 'warning',
    trend: {
      type: 'up',
      value: '1%'
    }
  },
  {
    level: 'high',
    label: '高风险',
    count: 8,
    percentage: 0.6,
    tagType: 'danger',
    trend: {
      type: 'up',
      value: '1%'
    }
  }
]

const handleClose = () => {
  visible.value = false
}

const handleExport = () => {
  ElMessage.success('统计报表导出成功')
  // 这里实现导出逻辑
}
</script>

<style scoped>
.statistics-content {
  max-height: 70vh;
  overflow-y: auto;
}

.stats-section {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.total-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-number.primary { color: #409eff; }
.stat-number.warning { color: #e6a23c; }
.stat-number.success { color: #67c23a; }
.stat-number.info { color: #909399; }

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-percentage {
  font-size: 12px;
  color: #909399;
}

.completion-text {
  font-size: 12px;
  color: #606266;
  margin-left: 8px;
}

.risk-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.risk-item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.risk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.risk-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.risk-trend.up { color: #f56c6c; }
.risk-trend.down { color: #67c23a; }
.risk-trend.stable { color: #909399; }

.risk-numbers {
  text-align: center;
}

.risk-count {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.risk-percentage {
  font-size: 14px;
  color: #606266;
}

.trend-chart {
  height: 200px;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.chart-placeholder {
  text-align: center;
}

.chart-text {
  margin-top: 12px;
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .total-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .risk-grid {
    grid-template-columns: 1fr;
  }
}
</style>
