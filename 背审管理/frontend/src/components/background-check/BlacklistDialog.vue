<template>
  <el-dialog v-model="dialogVisible" title="加入黑名单" width="600px" :before-close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="人员姓名">
        <el-input v-model="personnelName" disabled />
      </el-form-item>
      <el-form-item label="身份证号">
        <el-input v-model="personnelIdCard" disabled />
      </el-form-item>
      <el-form-item label="黑名单类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择黑名单类型">
          <el-option label="违法犯罪" value="违法犯罪" />
          <el-option label="精神疾病" value="精神疾病" />
          <el-option label="吸毒记录" value="吸毒记录" />
          <el-option label="其他" value="其他" />
        </el-select>
      </el-form-item>
      <el-form-item label="加入原因" prop="reason">
        <el-input v-model="formData.reason" type="textarea" :rows="4" placeholder="请详细说明加入黑名单的原因" />
      </el-form-item>
      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker v-model="formData.effectiveDate" type="date" placeholder="选择生效日期" style="width: 100%" />
      </el-form-item>
      <el-form-item label="失效日期" prop="expiryDate">
        <el-date-picker v-model="formData.expiryDate" type="date" placeholder="选择失效日期（可选）" style="width: 100%" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="danger" :loading="loading" @click="handleSubmit">确认加入黑名单</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { addToBlacklist } from '@/api/background-check'

interface Props {
  modelValue: boolean
  personnelId: number | null
  personnelName: string
  personnelIdCard: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = reactive({
  type: '',
  reason: '',
  effectiveDate: '',
  expiryDate: ''
})

const rules = {
  type: [{ required: true, message: '请选择黑名单类型', trigger: 'change' }],
  reason: [{ required: true, message: '请输入加入原因', trigger: 'blur' }],
  effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }]
}

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(() => props.modelValue, (visible) => {
  if (!visible) {
    resetForm()
  } else {
    // 设置默认生效日期为今天
    formData.effectiveDate = new Date().toISOString().split('T')[0]
  }
})

const resetForm = () => {
  Object.assign(formData, {
    type: '',
    reason: '',
    effectiveDate: '',
    expiryDate: ''
  })
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value || !props.personnelId) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    await addToBlacklist({
      personnelId: props.personnelId,
      blacklistType: 1, // 根据type字符串映射为数字
      reason: formData.reason,
      effectiveDate: formData.effectiveDate,
      expiryDate: formData.expiryDate || undefined
    })
    ElMessage.success('已成功加入黑名单')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('加入黑名单失败:', error)
    ElMessage.error('加入黑名单失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>