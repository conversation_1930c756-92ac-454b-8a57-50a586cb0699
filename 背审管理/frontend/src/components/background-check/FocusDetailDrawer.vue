<template>
  <el-drawer
    v-model="drawerVisible"
    :title="drawerTitle"
    :size="900"
    direction="rtl"
    :before-close="handleClose"
    class="focus-detail-drawer"
  >
    <div v-loading="loading" class="drawer-content">
      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="8" animated />
      </div>

      <div v-else-if="personnelData" class="personnel-content">
        <!-- 人员基本信息区域 -->
        <div class="basic-info-section">
          <div class="info-content">
            <div class="info-grid">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ personnelData.name }}</span>
              </div>
              <div class="info-item">
                <label>性别：</label>
                <span>{{ personnelData.gender === 1 ? '男' : '女' }}</span>
              </div>
              <div class="info-item">
                <label>手机号：</label>
                <span>{{ personnelData.phone || '未填写' }}</span>
              </div>
              <div class="info-item">
                <label>身份证：</label>
                <span>{{ personnelData.idCard }}</span>
              </div>
              <div class="info-item">
                <label>所属单位：</label>
                <span>{{ personnelData.organization }}</span>
              </div>
              <div class="info-item">
                <label>职位：</label>
                <span>{{ personnelData.position }}</span>
              </div>
              <div class="info-item">
                <label>人员类型：</label>
                <span>{{ getPersonnelTypeText(personnelData.personnelType) }}</span>
              </div>
              <div class="info-item">
                <label>在职状态：</label>
                <span>{{ personnelData.status === 1 ? '在职' : '离职' }}</span>
              </div>
              <div class="info-item">
                <label>入职日期：</label>
                <span>{{ personnelData.entryDate }}</span>
              </div>
              <div class="info-item">
                <label>区域：</label>
                <span>{{ personnelData.region }}</span>
              </div>
              <div class="info-item full-width">
                <label>异常类型：</label>
                <div class="abnormal-types">
                  <el-tag 
                    v-for="type in personnelData.abnormalTypes" 
                    :key="type" 
                    type="danger" 
                    size="small"
                    class="type-tag"
                  >
                    {{ getSingleAbnormalTypeText(type) }}
                  </el-tag>
                </div>
              </div>
              <div class="info-item full-width">
                <label>处理状态：</label>
                <div class="status-info">
                  <el-tag :type="getStatusTagType(personnelData.processingStatus)" size="large" class="status-tag">
                    {{ getProcessingStatusText(personnelData.processingStatus) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          <div class="avatar-section">
            <el-avatar :size="80" :src="personnelData.avatar" :icon="User" class="personnel-avatar" />
          </div>
        </div>

        <!-- 功能标签页 -->
        <el-card class="tabs-section" shadow="never">
          <el-tabs v-model="activeTab" class="detail-tabs">
            <!-- 关注信息标签页 -->
            <el-tab-pane label="关注信息" name="focus">
              <div class="tab-content">
                <!-- 基本关注信息 -->
                <div class="focus-info-section">
                  <h4 class="section-title">关注信息</h4>
                  <div class="info-grid">
                    <div class="info-item">
                      <label>关注时间：</label>
                      <span>{{ personnelData.focusDate || '未设置' }}</span>
                    </div>
                    <div class="info-item">
                      <label>关注级别：</label>
                      <el-tag :type="getFocusLevelType(personnelData.focusLevel)" size="small">
                        {{ getFocusLevelText(personnelData.focusLevel) }}
                      </el-tag>
                    </div>
                    <div class="info-item full-width">
                      <label>关注原因：</label>
                      <span>{{ personnelData.focusReason || '未填写' }}</span>
                    </div>
                  </div>
                </div>

                <!-- 跟踪记录时间线 -->
                <div class="tracking-section">
                  <div class="section-header">
                    <h4 class="section-title">跟踪记录</h4>
                    <el-button type="primary" size="small" @click="showTrackingForm = true">
                      <el-icon><Plus /></el-icon>
                      添加跟踪记录
                    </el-button>
                  </div>
                  
                  <el-timeline v-if="trackingRecords.length > 0" class="tracking-timeline">
                    <el-timeline-item
                      v-for="record in trackingRecords"
                      :key="record.id"
                      :timestamp="record.createTime"
                      placement="top"
                      :type="getTimelineType(record.type)"
                    >
                      <el-card class="timeline-card" shadow="hover">
                        <div class="record-header">
                          <span class="record-type">{{ getTrackingTypeText(record.type) }}</span>
                          <span class="record-creator">{{ record.creator }}</span>
                        </div>
                        <div class="record-content">{{ record.content }}</div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                  
                  <el-empty v-else description="暂无跟踪记录" />
                </div>

                <!-- 添加跟踪记录表单 -->
                <el-dialog
                  v-model="showTrackingForm"
                  title="添加跟踪记录"
                  width="500px"
                  :before-close="handleTrackingFormClose"
                >
                  <el-form :model="trackingForm" :rules="trackingRules" ref="trackingFormRef" label-width="80px">
                    <el-form-item label="记录类型" prop="type">
                      <el-select v-model="trackingForm.type" placeholder="请选择记录类型" style="width: 100%">
                        <el-option label="日常观察" value="observation" />
                        <el-option label="谈话记录" value="interview" />
                        <el-option label="行为异常" value="abnormal" />
                        <el-option label="其他" value="other" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="记录内容" prop="content">
                      <el-input
                        v-model="trackingForm.content"
                        type="textarea"
                        :rows="4"
                        placeholder="请详细描述跟踪记录内容..."
                        maxlength="500"
                        show-word-limit
                      />
                    </el-form-item>
                  </el-form>
                  <template #footer>
                    <el-button @click="handleTrackingFormClose">取消</el-button>
                    <el-button type="primary" @click="handleSubmitTracking" :loading="submittingTracking">
                      提交
                    </el-button>
                  </template>
                </el-dialog>
              </div>
            </el-tab-pane>

            <!-- 单位上报记录标签页 -->
            <el-tab-pane label="单位上报记录" name="reports">
              <div class="tab-content">
                <div class="reports-section">
                  <h4 class="section-title">上报记录</h4>
                  <el-table :data="reportRecords" stripe style="width: 100%">
                    <el-table-column prop="reportTime" label="上报时间" width="150" />
                    <el-table-column prop="reportUnit" label="上报单位" width="200" />
                    <el-table-column prop="reportType" label="上报类型" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getReportTypeTagType(row.reportType)" size="small">
                          {{ getReportTypeText(row.reportType) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="reportContent" label="上报内容" show-overflow-tooltip />
                    <el-table-column prop="reporter" label="上报人" width="100" />
                  </el-table>
                  <el-empty v-if="reportRecords.length === 0" description="暂无上报记录" />
                </div>
              </div>
            </el-tab-pane>

            <!-- 状态修改标签页 -->
            <el-tab-pane label="状态修改" name="status">
              <div class="tab-content">
                <div class="status-modify-section">
                  <h4 class="section-title">修改处理状态</h4>
                  <el-form :model="statusForm" :rules="statusRules" ref="statusFormRef" label-width="100px">
                    <el-form-item label="当前状态">
                      <el-tag :type="getStatusTagType(personnelData.processingStatus)" size="large">
                        {{ getProcessingStatusText(personnelData.processingStatus) }}
                      </el-tag>
                    </el-form-item>
                    <el-form-item label="新状态" prop="newStatus">
                      <el-select v-model="statusForm.newStatus" placeholder="请选择新状态" style="width: 100%">
                        <el-option label="重点关注" value="2" />
                        <el-option label="调岗/劝退" value="3" />
                        <el-option label="无需处理" value="1" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="修改原因" prop="reason">
                      <el-input
                        v-model="statusForm.reason"
                        type="textarea"
                        :rows="4"
                        placeholder="请详细说明状态修改原因..."
                        maxlength="500"
                        show-word-limit
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" @click="handleStatusChange" :loading="submittingStatus">
                        保存修改
                      </el-button>
                      <el-button @click="resetStatusForm">重置</el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>

      <div v-else class="error-state">
        <el-result icon="warning" title="数据加载失败" sub-title="请稍后重试">
          <template #extra>
            <el-button type="primary" @click="fetchPersonnelData">重新加载</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Plus } from '@element-plus/icons-vue'
import { getPersonnelList } from '@/api/background-check'
import { backgroundCheckAbnormalTypes, industryTypes } from '@/data/personnelMockData'

// Props
interface Props {
  modelValue: boolean
  personnelId: number | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  personnelId: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'status-changed': []
}>()

// 响应式数据
const loading = ref(false)
const personnelData = ref<any>(null)
const activeTab = ref('focus')
const showTrackingForm = ref(false)
const submittingTracking = ref(false)
const submittingStatus = ref(false)

// 跟踪记录数据
const trackingRecords = ref([
  {
    id: 1,
    type: 'observation',
    content: '该人员工作表现正常，与同事关系良好，未发现异常行为。',
    creator: '张三',
    createTime: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    type: 'interview',
    content: '与该人员进行了深入谈话，了解其思想动态，目前状态稳定。',
    creator: '李四',
    createTime: '2024-01-10 09:15:00'
  }
])

// 上报记录数据
const reportRecords = ref([
  {
    id: 1,
    reportTime: '2024-01-20',
    reportUnit: '保定市应急局',
    reportType: 'abnormal',
    reportContent: '该人员近期行为异常，需要重点关注',
    reporter: '王五'
  },
  {
    id: 2,
    reportTime: '2024-01-18',
    reportUnit: '保定市环保局',
    reportType: 'normal',
    reportContent: '定期上报，该人员表现正常',
    reporter: '赵六'
  }
])

// 表单数据
const trackingForm = reactive({
  type: '',
  content: ''
})

const statusForm = reactive({
  newStatus: '',
  reason: ''
})

// 表单验证规则
const trackingRules = {
  type: [{ required: true, message: '请选择记录类型', trigger: 'change' }],
  content: [{ required: true, message: '请输入记录内容', trigger: 'blur' }]
}

const statusRules = {
  newStatus: [{ required: true, message: '请选择新状态', trigger: 'change' }],
  reason: [{ required: true, message: '请输入修改原因', trigger: 'blur' }]
}

// 计算属性
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const drawerTitle = computed(() => {
  return personnelData.value ? `${personnelData.value.name} - 详情` : '重点关注人员详情'
})

// 工具函数
const getPersonnelTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '专职保卫',
    2: '保安人员'
  }
  return typeMap[type] || '未知'
}

const getSingleAbnormalTypeText = (type: string) => {
  const typeItem = backgroundCheckAbnormalTypes.find(item => item.value === type)
  return typeItem ? typeItem.label : type
}

const getProcessingStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '未处理',
    1: '无需处理',
    2: '重点关注',
    3: '调岗/劝退'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'warning',
    1: 'success',
    2: 'danger',
    3: 'info'
  }
  return typeMap[status] || 'info'
}

const getFocusLevelText = (level: number) => {
  const levelMap: Record<number, string> = {
    1: '一般关注',
    2: '重点关注',
    3: '特别关注'
  }
  return levelMap[level] || '一般关注'
}

const getFocusLevelType = (level: number) => {
  const typeMap: Record<number, string> = {
    1: 'info',
    2: 'warning',
    3: 'danger'
  }
  return typeMap[level] || 'info'
}

const getTrackingTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    observation: '日常观察',
    interview: '谈话记录',
    abnormal: '行为异常',
    other: '其他'
  }
  return typeMap[type] || type
}

const getTimelineType = (type: string) => {
  const typeMap: Record<string, string> = {
    observation: 'primary',
    interview: 'success',
    abnormal: 'danger',
    other: 'info'
  }
  return typeMap[type] || 'primary'
}

const getReportTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    normal: '正常上报',
    abnormal: '异常上报',
    urgent: '紧急上报'
  }
  return typeMap[type] || type
}

const getReportTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    normal: 'success',
    abnormal: 'warning',
    urgent: 'danger'
  }
  return typeMap[type] || 'info'
}

// 事件处理函数
const handleClose = () => {
  drawerVisible.value = false
}

const handleTrackingFormClose = () => {
  showTrackingForm.value = false
  resetTrackingForm()
}

const resetTrackingForm = () => {
  trackingForm.type = ''
  trackingForm.content = ''
}

const resetStatusForm = () => {
  statusForm.newStatus = ''
  statusForm.reason = ''
}

const handleSubmitTracking = async () => {
  // 这里应该调用API提交跟踪记录
  try {
    submittingTracking.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 添加到本地记录
    const newRecord = {
      id: Date.now(),
      type: trackingForm.type,
      content: trackingForm.content,
      creator: '当前用户',
      createTime: new Date().toLocaleString()
    }
    trackingRecords.value.unshift(newRecord)

    ElMessage.success('跟踪记录添加成功')
    handleTrackingFormClose()
  } catch (error) {
    ElMessage.error('添加失败，请重试')
  } finally {
    submittingTracking.value = false
  }
}

const handleStatusChange = async () => {
  try {
    submittingStatus.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('状态修改成功')
    emit('status-changed')
    resetStatusForm()
  } catch (error) {
    ElMessage.error('修改失败，请重试')
  } finally {
    submittingStatus.value = false
  }
}

const fetchPersonnelData = async () => {
  if (!props.personnelId) return

  try {
    loading.value = true
    // 这里应该调用API获取人员详情
    // const response = await getPersonnelById(props.personnelId)

    // 模拟API调用，从mock数据中获取
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟数据
    personnelData.value = {
      id: props.personnelId,
      name: '张三',
      gender: 1,
      phone: '***********',
      idCard: '130123199001011234',
      organization: '保定市应急局',
      position: '安全员',
      personnelType: 1,
      status: 1,
      entryDate: '2020-01-01',
      region: '保定市',
      abnormalTypes: ['political_issues'],
      processingStatus: 2,
      focusDate: '2024-01-01',
      focusLevel: 2,
      focusReason: '政治问题需要重点关注'
    }
  } catch (error) {
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.personnelId, (newId) => {
  if (newId && props.modelValue) {
    fetchPersonnelData()
  }
}, { immediate: true })

watch(() => props.modelValue, (newValue) => {
  if (newValue && props.personnelId) {
    fetchPersonnelData()
  }
})
</script>

<style scoped>
.focus-detail-drawer {
  --el-drawer-padding-primary: 0;
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state {
  padding: 40px 20px;
}

.personnel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 基本信息区域 */
.basic-info-section {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-content {
  flex: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px 24px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
}

.abnormal-types {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.type-tag {
  margin: 0;
}

.avatar-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.personnel-avatar {
  border: 3px solid #f0f0f0;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.status-tag {
  font-size: 14px;
  padding: 8px 16px;
}

/* 标签页区域 */
.tabs-section {
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-tabs {
  min-height: 400px;
}

.tab-content {
  padding: 20px 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 关注信息区域 */
.focus-info-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

/* 跟踪记录区域 */
.tracking-section {
  margin-bottom: 24px;
}

.tracking-timeline {
  margin-top: 16px;
}

.timeline-card {
  margin-bottom: 8px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-type {
  font-weight: 600;
  color: #409eff;
}

.record-creator {
  font-size: 12px;
  color: #909399;
}

.record-content {
  color: #606266;
  line-height: 1.5;
}

/* 上报记录区域 */
.reports-section {
  margin-bottom: 24px;
}

/* 状态修改区域 */
.status-modify-section {
  margin-bottom: 24px;
}

/* 错误状态 */
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .basic-info-section {
    flex-direction: column;
    gap: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
