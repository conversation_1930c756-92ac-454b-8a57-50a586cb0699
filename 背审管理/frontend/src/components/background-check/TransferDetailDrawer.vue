<template>
  <el-drawer
    v-model="drawerVisible"
    :title="drawerTitle"
    :size="900"
    direction="rtl"
    :before-close="handleClose"
    class="transfer-detail-drawer"
  >
    <div v-loading="loading" class="drawer-content">
      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="8" animated />
      </div>

      <div v-else-if="personnelData" class="personnel-content">
        <!-- 人员基本信息区域 -->
        <div class="basic-info-section">
          <div class="info-content">
            <div class="info-grid">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ personnelData.name }}</span>
              </div>
              <div class="info-item">
                <label>性别：</label>
                <span>{{ personnelData.gender === 1 ? '男' : '女' }}</span>
              </div>
              <div class="info-item">
                <label>手机号：</label>
                <span>{{ personnelData.phone || '未填写' }}</span>
              </div>
              <div class="info-item">
                <label>身份证：</label>
                <span>{{ personnelData.idCard }}</span>
              </div>
              <div class="info-item">
                <label>所属单位：</label>
                <span>{{ personnelData.organization }}</span>
              </div>
              <div class="info-item">
                <label>职位：</label>
                <span>{{ personnelData.position }}</span>
              </div>
              <div class="info-item">
                <label>人员类型：</label>
                <span>{{ getPersonnelTypeText(personnelData.personnelType) }}</span>
              </div>
              <div class="info-item">
                <label>在职状态：</label>
                <span>{{ personnelData.status === 1 ? '在职' : '离职' }}</span>
              </div>
              <div class="info-item">
                <label>入职日期：</label>
                <span>{{ personnelData.entryDate }}</span>
              </div>
              <div class="info-item">
                <label>区域：</label>
                <span>{{ personnelData.region }}</span>
              </div>
              <div class="info-item full-width">
                <label>异常类型：</label>
                <div class="abnormal-types">
                  <el-tag 
                    v-for="type in personnelData.abnormalTypes" 
                    :key="type" 
                    type="danger" 
                    size="small"
                    class="type-tag"
                  >
                    {{ getSingleAbnormalTypeText(type) }}
                  </el-tag>
                </div>
              </div>
              <div class="info-item full-width">
                <label>处理状态：</label>
                <div class="status-info">
                  <el-tag :type="getStatusTagType(personnelData.processingStatus)" size="large" class="status-tag">
                    {{ getProcessingStatusText(personnelData.processingStatus) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          <div class="avatar-section">
            <el-avatar :size="80" :src="personnelData.avatar" :icon="User" class="personnel-avatar" />
          </div>
        </div>

        <!-- 功能标签页 -->
        <el-card class="tabs-section" shadow="never">
          <el-tabs v-model="activeTab" class="detail-tabs">
            <!-- 调岗/劝退信息标签页 -->
            <el-tab-pane label="调岗/劝退信息" name="transfer">
              <div class="tab-content">
                <!-- 基本调岗/劝退信息 -->
                <div class="transfer-info-section">
                  <h4 class="section-title">调岗/劝退信息</h4>
                  <div class="info-grid">
                    <div class="info-item">
                      <label>下发时间：</label>
                      <span>{{ personnelData.transferDate || '未设置' }}</span>
                    </div>
                    <div class="info-item">
                      <label>计划时间：</label>
                      <span>{{ personnelData.plannedDate || '未设置' }}</span>
                    </div>
                    <div class="info-item">
                      <label>处理人：</label>
                      <span>{{ personnelData.handler || '未指定' }}</span>
                    </div>
                    <div class="info-item">
                      <label>处理类型：</label>
                      <el-tag :type="getTransferTypeTagType(personnelData.transferType)" size="small">
                        {{ getTransferTypeText(personnelData.transferType) }}
                      </el-tag>
                    </div>
                    <div class="info-item">
                      <label>跟踪民警：</label>
                      <span>{{ personnelData.trackingOfficer || '未指定' }}</span>
                    </div>
                    <div class="info-item">
                      <label>执行状态：</label>
                      <el-tag :type="getExecutionStatusTagType(personnelData.executionStatus)" size="small">
                        {{ getExecutionStatusText(personnelData.executionStatus) }}
                      </el-tag>
                    </div>
                    <div class="info-item full-width">
                      <label>处理原因：</label>
                      <span>{{ personnelData.transferReason || '未填写' }}</span>
                    </div>
                    <div class="info-item full-width">
                      <label>备注说明：</label>
                      <span>{{ personnelData.transferNote || '无' }}</span>
                    </div>
                  </div>
                </div>

                <!-- 指定跟踪民警 -->
                <div class="assign-officer-section">
                  <div class="section-header">
                    <h4 class="section-title">指定跟踪民警</h4>
                    <el-button type="primary" size="small" @click="showAssignForm = true">
                      <el-icon><UserFilled /></el-icon>
                      指定民警
                    </el-button>
                  </div>
                  
                  <div v-if="personnelData.trackingOfficer" class="current-officer">
                    <el-card class="officer-card" shadow="hover">
                      <div class="officer-info">
                        <div class="officer-header">
                          <span class="officer-name">{{ personnelData.trackingOfficer }}</span>
                          <span class="officer-badge">跟踪民警</span>
                        </div>
                        <div class="officer-details">
                          <span>联系电话：{{ personnelData.officerPhone || '未填写' }}</span>
                          <span>所属部门：{{ personnelData.officerDepartment || '未填写' }}</span>
                        </div>
                        <div class="assign-time">
                          指定时间：{{ personnelData.assignTime || '未记录' }}
                        </div>
                      </div>
                    </el-card>
                  </div>
                  
                  <el-empty v-else description="暂未指定跟踪民警" />
                </div>

                <!-- 指定民警表单 -->
                <el-dialog
                  v-model="showAssignForm"
                  title="指定跟踪民警"
                  width="500px"
                  :before-close="handleAssignFormClose"
                >
                  <el-form :model="assignForm" :rules="assignRules" ref="assignFormRef" label-width="80px">
                    <el-form-item label="民警姓名" prop="officerName">
                      <el-input v-model="assignForm.officerName" placeholder="请输入民警姓名" />
                    </el-form-item>
                    <el-form-item label="联系电话" prop="officerPhone">
                      <el-input v-model="assignForm.officerPhone" placeholder="请输入联系电话" />
                    </el-form-item>
                    <el-form-item label="所属部门" prop="officerDepartment">
                      <el-input v-model="assignForm.officerDepartment" placeholder="请输入所属部门" />
                    </el-form-item>
                    <el-form-item label="备注说明">
                      <el-input
                        v-model="assignForm.note"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入备注说明..."
                        maxlength="200"
                        show-word-limit
                      />
                    </el-form-item>
                  </el-form>
                  <template #footer>
                    <el-button @click="handleAssignFormClose">取消</el-button>
                    <el-button type="primary" @click="handleAssignOfficer" :loading="submittingAssign">
                      确定指定
                    </el-button>
                  </template>
                </el-dialog>
              </div>
            </el-tab-pane>

            <!-- 状态修改标签页 -->
            <el-tab-pane label="状态修改" name="status">
              <div class="tab-content">
                <div class="status-modify-section">
                  <h4 class="section-title">修改处理状态</h4>
                  <el-form :model="statusForm" :rules="statusRules" ref="statusFormRef" label-width="100px">
                    <el-form-item label="当前状态">
                      <el-tag :type="getStatusTagType(personnelData.processingStatus)" size="large">
                        {{ getProcessingStatusText(personnelData.processingStatus) }}
                      </el-tag>
                    </el-form-item>
                    <el-form-item label="新状态" prop="newStatus">
                      <el-select v-model="statusForm.newStatus" placeholder="请选择新状态" style="width: 100%">
                        <el-option label="重点关注" value="2" />
                        <el-option label="调岗/劝退" value="3" />
                        <el-option label="无需处理" value="1" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="修改原因" prop="reason">
                      <el-input
                        v-model="statusForm.reason"
                        type="textarea"
                        :rows="4"
                        placeholder="请详细说明状态修改原因..."
                        maxlength="500"
                        show-word-limit
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" @click="handleStatusChange" :loading="submittingStatus">
                        保存修改
                      </el-button>
                      <el-button @click="resetStatusForm">重置</el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>

      <div v-else class="error-state">
        <el-result icon="warning" title="数据加载失败" sub-title="请稍后重试">
          <template #extra>
            <el-button type="primary" @click="fetchPersonnelData">重新加载</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { User, UserFilled } from '@element-plus/icons-vue'
import { getPersonnelList } from '@/api/background-check'
import { backgroundCheckAbnormalTypes } from '@/data/personnelMockData'

// Props
interface Props {
  modelValue: boolean
  personnelId: number | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  personnelId: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'status-changed': []
}>()

// 响应式数据
const loading = ref(false)
const personnelData = ref<any>(null)
const activeTab = ref('transfer')
const showAssignForm = ref(false)
const submittingAssign = ref(false)
const submittingStatus = ref(false)

// 表单数据
const assignForm = reactive({
  officerName: '',
  officerPhone: '',
  officerDepartment: '',
  note: ''
})

const statusForm = reactive({
  newStatus: '',
  reason: ''
})

// 表单验证规则
const assignRules = {
  officerName: [{ required: true, message: '请输入民警姓名', trigger: 'blur' }],
  officerPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  officerDepartment: [{ required: true, message: '请输入所属部门', trigger: 'blur' }]
}

const statusRules = {
  newStatus: [{ required: true, message: '请选择新状态', trigger: 'change' }],
  reason: [{ required: true, message: '请输入修改原因', trigger: 'blur' }]
}

// 计算属性
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const drawerTitle = computed(() => {
  return personnelData.value ? `${personnelData.value.name} - 详情` : '调岗/劝退人员详情'
})

// 工具函数
const getPersonnelTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '专职保卫',
    2: '保安人员'
  }
  return typeMap[type] || '未知'
}

const getSingleAbnormalTypeText = (type: string) => {
  const typeItem = backgroundCheckAbnormalTypes.find(item => item.value === type)
  return typeItem ? typeItem.label : type
}

const getProcessingStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '未处理',
    1: '无需处理',
    2: '重点关注',
    3: '调岗/劝退'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'warning',
    1: 'success',
    2: 'danger',
    3: 'info'
  }
  return typeMap[status] || 'info'
}

const getTransferTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    transfer: '调岗',
    resign: '劝退',
    dismiss: '辞退'
  }
  return typeMap[type] || '未知'
}

const getTransferTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    transfer: 'warning',
    resign: 'info',
    dismiss: 'danger'
  }
  return typeMap[type] || 'info'
}

const getExecutionStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待执行',
    executing: '执行中',
    completed: '已完成',
    failed: '执行失败'
  }
  return statusMap[status] || '未知'
}

const getExecutionStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    executing: 'primary',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

// 事件处理函数
const handleClose = () => {
  drawerVisible.value = false
}

const handleAssignFormClose = () => {
  showAssignForm.value = false
  resetAssignForm()
}

const resetAssignForm = () => {
  assignForm.officerName = ''
  assignForm.officerPhone = ''
  assignForm.officerDepartment = ''
  assignForm.note = ''
}

const resetStatusForm = () => {
  statusForm.newStatus = ''
  statusForm.reason = ''
}

const handleAssignOfficer = async () => {
  try {
    submittingAssign.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新人员数据
    if (personnelData.value) {
      personnelData.value.trackingOfficer = assignForm.officerName
      personnelData.value.officerPhone = assignForm.officerPhone
      personnelData.value.officerDepartment = assignForm.officerDepartment
      personnelData.value.assignTime = new Date().toLocaleString()
    }

    ElMessage.success('跟踪民警指定成功')
    handleAssignFormClose()
  } catch (error) {
    ElMessage.error('指定失败，请重试')
  } finally {
    submittingAssign.value = false
  }
}

const handleStatusChange = async () => {
  try {
    submittingStatus.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('状态修改成功')
    emit('status-changed')
    resetStatusForm()
  } catch (error) {
    ElMessage.error('修改失败，请重试')
  } finally {
    submittingStatus.value = false
  }
}

const fetchPersonnelData = async () => {
  if (!props.personnelId) return

  try {
    loading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟数据
    personnelData.value = {
      id: props.personnelId,
      name: '李四',
      gender: 1,
      phone: '***********',
      idCard: '130123199002021234',
      organization: '保定市环保局',
      position: '保安员',
      personnelType: 2,
      status: 1,
      entryDate: '2020-07-01',
      region: '保定市唐县',
      abnormalTypes: ['credit_issues', 'violence'],
      processingStatus: 3,
      transferDate: '2024-01-15',
      plannedDate: '2024-02-15',
      handler: '张处长',
      transferType: 'transfer',
      trackingOfficer: '王警官',
      officerPhone: '***********',
      officerDepartment: '治安大队',
      assignTime: '2024-01-16 10:30:00',
      executionStatus: 'executing',
      transferReason: '信用问题和暴力倾向，需要调离当前岗位',
      transferNote: '已与本人沟通，配合调岗安排'
    }
  } catch (error) {
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.personnelId, (newId) => {
  if (newId && props.modelValue) {
    fetchPersonnelData()
  }
}, { immediate: true })

watch(() => props.modelValue, (newValue) => {
  if (newValue && props.personnelId) {
    fetchPersonnelData()
  }
})
</script>

<style scoped>
.transfer-detail-drawer {
  --el-drawer-padding-primary: 0;
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state {
  padding: 40px 20px;
}

.personnel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 基本信息区域 */
.basic-info-section {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-content {
  flex: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px 24px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
}

.abnormal-types {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.type-tag {
  margin: 0;
}

.avatar-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.personnel-avatar {
  border: 3px solid #f0f0f0;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.status-tag {
  font-size: 14px;
  padding: 8px 16px;
}

/* 标签页区域 */
.tabs-section {
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-tabs {
  min-height: 400px;
}

.tab-content {
  padding: 20px 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 调岗/劝退信息区域 */
.transfer-info-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

/* 指定跟踪民警区域 */
.assign-officer-section {
  margin-bottom: 24px;
}

.current-officer {
  margin-top: 16px;
}

.officer-card {
  margin-bottom: 8px;
}

.officer-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.officer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.officer-name {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.officer-badge {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.officer-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: #606266;
  font-size: 14px;
}

.assign-time {
  font-size: 12px;
  color: #909399;
}

/* 状态修改区域 */
.status-modify-section {
  margin-bottom: 24px;
}

/* 错误状态 */
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .basic-info-section {
    flex-direction: column;
    gap: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .officer-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
</style>
