<template>
  <el-dialog v-model="dialogVisible" title="人员详情" width="800px" :before-close="handleClose">
    <div v-loading="loading" class="detail-content">
      <el-descriptions v-if="personnelData" :column="2" border>
        <el-descriptions-item label="姓名">{{ personnelData.name }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ personnelData.idCard }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ personnelData.phone }}</el-descriptions-item>
        <el-descriptions-item label="民族">{{ personnelData.ethnicity }}</el-descriptions-item>
        <el-descriptions-item label="职位">{{ personnelData.position }}</el-descriptions-item>
        <el-descriptions-item label="保安公司">{{ personnelData.securityCompany }}</el-descriptions-item>
        <el-descriptions-item label="学历">{{ personnelData.education }}</el-descriptions-item>
        <el-descriptions-item label="区域">{{ personnelData.region }}</el-descriptions-item>
        <el-descriptions-item label="详细地址" :span="2">{{ personnelData.address }}</el-descriptions-item>
        <el-descriptions-item label="政治面貌">{{ personnelData.politicalStatus }}</el-descriptions-item>
        <el-descriptions-item label="所属单位">{{ personnelData.organization }}</el-descriptions-item>
        <el-descriptions-item label="人员类型">
          <el-tag>{{ getPersonnelTypeText(personnelData.personnelType) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="背景审查结果" :span="2">
          <div class="background-check-result">
            <!-- 未审查或正常状态显示主标签 -->
            <el-tag
              v-if="personnelData.backgroundCheckResult !== 2"
              :type="getBackgroundCheckResultType(personnelData.backgroundCheckResult)"
              class="result-tag"
            >
              {{ getBackgroundCheckResultText(personnelData.backgroundCheckResult) }}
            </el-tag>

            <!-- 异常状态直接显示异常类型标签 -->
            <div v-if="personnelData.backgroundCheckResult === 2 && personnelData.abnormalTypes && personnelData.abnormalTypes.length > 0" class="abnormal-types">
              <el-tag
                v-for="type in personnelData.abnormalTypes"
                :key="type"
                type="danger"
                class="abnormal-tag"
              >
                {{ getSingleAbnormalTypeText(type) }}
              </el-tag>
            </div>

            <!-- 异常但没有具体类型时显示异常标签 -->
            <el-tag
              v-if="personnelData.backgroundCheckResult === 2 && (!personnelData.abnormalTypes || personnelData.abnormalTypes.length === 0)"
              type="danger"
              class="result-tag"
            >
              异常
            </el-tag>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag :type="getProcessingStatusType(personnelData.processingStatus || 0)">
            {{ getProcessingStatusText(personnelData.processingStatus || 0) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="在职状态">
          <el-tag :type="getEmploymentStatusType(personnelData.status)">
            {{ getEmploymentStatusText(personnelData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="人脸照片" :span="2">
          <el-image v-if="personnelData.photo" :src="personnelData.photo" style="width: 100px; height: 100px" fit="cover" />
          <span v-else>暂无照片</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="personnelId && $emit('edit', personnelId)">编辑</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { getPersonnelDetail } from '@/api/background-check'
import {
  getBackgroundCheckResultText,
  getSingleAbnormalTypeText,
  getProcessingStatusText,
  getEmploymentStatusText
} from '@/data/personnelMockData'

interface Props {
  modelValue: boolean
  personnelId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', id: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const personnelData = ref<any>(null)

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(() => props.personnelId, (newId) => {
  if (newId && props.modelValue) {
    fetchPersonnelDetail()
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && props.personnelId) {
    fetchPersonnelDetail()
  } else {
    personnelData.value = null
  }
})

const fetchPersonnelDetail = async () => {
  if (!props.personnelId) return
  
  try {
    loading.value = true
    const response = await getPersonnelDetail(props.personnelId)
    personnelData.value = response.data
  } catch (error) {
    console.error('获取人员详情失败:', error)
  } finally {
    loading.value = false
  }
}

const getPersonnelTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '有编制人员',
    2: '无编制人员', 
    3: '外包人员'
  }
  return typeMap[type] || '未知'
}

const getBackgroundCheckResultType = (result: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',    // 未审查
    1: 'success', // 正常
    2: 'danger'   // 异常
  }
  return typeMap[result] || 'info'
}

const getProcessingStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: '',        // 未处理
    1: 'info',    // 无需处理
    2: 'warning', // 重点关注
    3: 'danger'   // 调岗/劝退
  }
  return typeMap[status] || ''
}

const getEmploymentStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    1: 'success', // 在职
    2: 'info'     // 离职
  }
  return typeMap[status] || 'info'
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.detail-content {
  min-height: 200px;
}

.background-check-result {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-tag {
  align-self: flex-start;
  font-weight: 500;
}

.abnormal-types {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.abnormal-tag {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}
</style>