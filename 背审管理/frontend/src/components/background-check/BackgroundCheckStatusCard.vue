<!-- 
  背审状态统计卡片组件
  可复用组件：适用于背审人员管理和背审结果处理模块
-->
<template>
  <div class="status-cards-container">
    <div class="status-cards">
      <el-card 
        v-for="card in statusCards" 
        :key="card.key"
        class="status-card"
        :class="card.type"
        shadow="hover"
        @click="handleCardClick(card.key)"
      >
        <div class="card-content">
          <div class="card-number">{{ card.count }}</div>
          <div class="card-label">{{ card.label }}</div>
          <div v-if="card.trend" class="card-trend" :class="card.trend.type">
            <el-icon>
              <ArrowUp v-if="card.trend.type === 'up'" />
              <ArrowDown v-if="card.trend.type === 'down'" />
            </el-icon>
            {{ card.trend.value }}
          </div>
        </div>
      </el-card>
    </div>
    
    <el-button 
      type="primary" 
      class="detail-button"
      @click="showStatisticsDetail"
    >
      <el-icon><DataAnalysis /></el-icon>
      统计详情
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, DataAnalysis } from '@element-plus/icons-vue'

interface StatusCard {
  key: string
  label: string
  count: number
  type: 'primary' | 'warning' | 'success' | 'danger' | 'info'
  trend?: {
    type: 'up' | 'down'
    value: string
  }
}

interface Props {
  statistics: {
    total: number
    pending: number
    inProgress: number
    completed: number
    risk?: number
    overdue?: number
  }
  showRisk?: boolean
  showOverdue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showRisk: false,
  showOverdue: false
})

const emit = defineEmits<{
  cardClick: [key: string]
  showDetail: []
}>()

const statusCards = computed((): StatusCard[] => {
  const cards: StatusCard[] = [
    {
      key: 'total',
      label: '总人数',
      count: props.statistics.total,
      type: 'primary'
    },
    {
      key: 'pending',
      label: '待背审',
      count: props.statistics.pending,
      type: 'warning'
    },
    {
      key: 'in_progress',
      label: '背审中',
      count: props.statistics.inProgress,
      type: 'info'
    },
    {
      key: 'completed',
      label: '已完成',
      count: props.statistics.completed,
      type: 'success'
    }
  ]

  if (props.showRisk && props.statistics.risk !== undefined) {
    cards.push({
      key: 'risk',
      label: '风险人员',
      count: props.statistics.risk,
      type: 'danger'
    })
  }

  if (props.showOverdue && props.statistics.overdue !== undefined) {
    cards.push({
      key: 'overdue',
      label: '逾期任务',
      count: props.statistics.overdue,
      type: 'danger'
    })
  }

  return cards
})

const handleCardClick = (key: string) => {
  emit('cardClick', key)
}

const showStatisticsDetail = () => {
  emit('showDetail')
}
</script>

<style scoped>
.status-cards-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.status-cards {
  display: flex;
  gap: 16px;
  flex: 1;
}

.status-card {
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-card.primary {
  border-left: 4px solid #409eff;
}

.status-card.warning {
  border-left: 4px solid #e6a23c;
}

.status-card.success {
  border-left: 4px solid #67c23a;
}

.status-card.danger {
  border-left: 4px solid #f56c6c;
}

.status-card.info {
  border-left: 4px solid #909399;
}

.card-content {
  text-align: center;
  padding: 8px 0;
}

.card-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.card-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.card-trend.up {
  color: #f56c6c;
}

.card-trend.down {
  color: #67c23a;
}

.detail-button {
  height: 80px;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  white-space: nowrap;
}

.detail-button .el-icon {
  font-size: 18px;
}

@media (max-width: 1200px) {
  .status-cards-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .detail-button {
    height: auto;
    padding: 12px 20px;
    flex-direction: row;
  }
}
</style>
