<template>
  <el-dialog v-model="dialogVisible" title="编辑黑名单" width="600px" :before-close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="姓名">
        <el-input v-model="personnelName" disabled />
      </el-form-item>
      <el-form-item label="身份证号">
        <el-input v-model="personnelIdCard" disabled />
      </el-form-item>
      <el-form-item label="黑名单类型" prop="blacklistType">
        <el-select v-model="formData.blacklistType" placeholder="请选择黑名单类型">
          <el-option label="违法犯罪" :value="1" />
          <el-option label="精神疾病" :value="2" />
          <el-option label="吸毒记录" :value="3" />
          <el-option label="其他" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="加入原因" prop="reason">
        <el-input v-model="formData.reason" type="textarea" :rows="4" placeholder="请详细说明加入黑名单的原因" />
      </el-form-item>
      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker v-model="formData.effectiveDate" type="date" placeholder="选择生效日期" style="width: 100%" />
      </el-form-item>
      <el-form-item label="失效日期" prop="expiryDate">
        <el-date-picker v-model="formData.expiryDate" type="date" placeholder="选择失效日期（可选）" style="width: 100%" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { getBlacklistDetail, updateBlacklist } from '@/api/background-check'

interface Props {
  modelValue: boolean
  blacklistId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const personnelName = ref('')
const personnelIdCard = ref('')

const formData = reactive({
  blacklistType: 1,
  reason: '',
  effectiveDate: '',
  expiryDate: ''
})

const rules = {
  blacklistType: [{ required: true, message: '请选择黑名单类型', trigger: 'change' }],
  reason: [{ required: true, message: '请输入加入原因', trigger: 'blur' }],
  effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }]
}

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(() => props.modelValue, (visible) => {
  if (visible && props.blacklistId) {
    fetchBlacklistDetail()
  } else {
    resetForm()
  }
})

const fetchBlacklistDetail = async () => {
  if (!props.blacklistId) return
  
  try {
    loading.value = true
    const response = await getBlacklistDetail(props.blacklistId)
    const data = response.data
    personnelName.value = data.name
    personnelIdCard.value = data.idCard
    Object.assign(formData, {
      blacklistType: data.blacklistType,
      reason: data.reason,
      effectiveDate: data.effectiveDate,
      expiryDate: data.expiryDate
    })
  } catch (error) {
    console.error('获取黑名单详情失败:', error)
    ElMessage.error('获取黑名单详情失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  personnelName.value = ''
  personnelIdCard.value = ''
  Object.assign(formData, {
    blacklistType: 1,
    reason: '',
    effectiveDate: '',
    expiryDate: ''
  })
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value || !props.blacklistId) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    await updateBlacklist({ id: props.blacklistId, ...formData })
    ElMessage.success('保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>