<!-- 背审任务详情抽屉包装组件 -->
<template>
  <TaskDetailDrawer
    v-model="visible"
    :task-id="taskId"
    task-type="background-check"
    :task-detail="taskDetail"
    :history-list="reminderHistory"
    :personnel-list="personnelList"
    :on-fetch-task-detail="fetchTaskDetail"
    :on-fetch-history="fetchReminderHistory"
    :on-fetch-personnel="fetchPersonnelList"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import TaskDetailDrawer from '@/components/common/TaskDetailDrawer.vue'

interface Props {
  modelValue: boolean
  taskId?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 数据状态
const taskDetail = ref<any>({})
const reminderHistory = ref<any[]>([])
const personnelList = ref<any[]>([])

// 模拟数据
const mockTaskData = {
  id: '1',
  taskNo: 'BG202401001',
  title: '2024年第一批背景调查',
  status: 'in_progress',
  priority: 'high',
  personnelCount: 25,
  assignedToUserName: '张处理员',
  createTime: '2024-01-15 09:00:00',
  dueDate: '2024-01-25 18:00:00',
  isOverdue: false,
  overdueBy: 0,
  description: '对新入职员工进行背景调查，重点关注学历、工作经历和信用记录',
  progress: {
    percentage: 68,
    completed: 17,
    total: 25
  }
}

const mockReminderHistory = [
  {
    reminderTime: '2024-01-20 10:30:00',
    reminderUser: '李主管',
    isRead: true,
    feedback: '已收到催办，正在加快处理进度'
  },
  {
    reminderTime: '2024-01-18 14:20:00',
    reminderUser: '王经理',
    isRead: true,
    feedback: '了解，会尽快完成剩余人员的背调'
  }
]

const mockPersonnelList = [
  {
    id: '1',
    name: '张三',
    idCard: '110101199001011234',
    phone: '***********',
    organization: '技术部',
    status: 'normal'
  },
  {
    id: '2',
    name: '李四',
    idCard: '110101199002022345',
    phone: '***********',
    organization: '市场部',
    status: 'abnormal',
    abnormalType: 'education_fraud'
  },
  {
    id: '3',
    name: '王五',
    idCard: '110101199003033456',
    phone: '***********',
    organization: '财务部',
    status: 'pending'
  }
]

// 数据获取函数
const fetchTaskDetail = async (taskId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    taskDetail.value = mockTaskData
    return mockTaskData
  } catch (error) {
    ElMessage.error('获取任务详情失败')
    throw error
  }
}

const fetchReminderHistory = async (taskId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    reminderHistory.value = mockReminderHistory
    return mockReminderHistory
  } catch (error) {
    ElMessage.error('获取催办历史失败')
    throw error
  }
}

const fetchPersonnelList = async (
  taskId: string, 
  searchForm: any, 
  pagination: any
) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 400))
    
    let filteredList = [...mockPersonnelList]
    
    // 应用搜索条件
    if (searchForm.name) {
      filteredList = filteredList.filter(p => p.name.includes(searchForm.name))
    }
    if (searchForm.idCard) {
      filteredList = filteredList.filter(p => p.idCard.includes(searchForm.idCard))
    }
    if (searchForm.status) {
      if (searchForm.status === 'incomplete') {
        filteredList = filteredList.filter(p => p.status === 'pending')
      } else if (searchForm.status === 'completed') {
        filteredList = filteredList.filter(p => ['normal', 'abnormal'].includes(p.status))
      }
    }
    
    // 分页处理
    const start = (pagination.page - 1) * pagination.size
    const end = start + pagination.size
    const paginatedList = filteredList.slice(start, end)
    
    personnelList.value = paginatedList
    
    return {
      list: paginatedList,
      total: filteredList.length
    }
  } catch (error) {
    ElMessage.error('获取人员列表失败')
    throw error
  }
}
</script>
