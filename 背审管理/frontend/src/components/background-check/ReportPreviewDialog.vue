<template>
  <el-dialog
    v-model="dialogVisible"
    title="报表预览"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="preview-container" v-loading="loading">
      <div v-if="!loading && previewData" class="preview-content">
        <!-- PDF预览 -->
        <div v-if="previewData.format === 'pdf'" class="pdf-preview">
          <iframe 
            :src="previewData.previewUrl" 
            width="100%" 
            height="600px"
            frameborder="0"
          ></iframe>
        </div>
        
        <!-- Excel/Word预览 -->
        <div v-else-if="previewData.format === 'excel' || previewData.format === 'word'" class="office-preview">
          <div class="preview-info">
            <el-alert
              title="文件预览"
              :description="`文件格式：${previewData.format.toUpperCase()}，文件大小：${formatFileSize(previewData.fileSize)}`"
              type="info"
              show-icon
              :closable="false"
            />
          </div>
          
          <!-- 如果有预览图片 -->
          <div v-if="previewData.previewImages && previewData.previewImages.length > 0" class="image-preview">
            <div class="preview-pages">
              <div 
                v-for="(image, index) in previewData.previewImages" 
                :key="index"
                class="preview-page"
              >
                <div class="page-header">
                  <span>第 {{ index + 1 }} 页</span>
                </div>
                <img :src="image" :alt="`第${index + 1}页预览`" />
              </div>
            </div>
          </div>
          
          <!-- 如果没有预览图片，显示下载提示 -->
          <div v-else class="no-preview">
            <el-empty description="该格式文件无法在线预览，请下载后查看">
              <el-button type="primary" @click="handleDownload">
                <el-icon><Download /></el-icon>
                下载文件
              </el-button>
            </el-empty>
          </div>
        </div>
        
        <!-- 其他格式 -->
        <div v-else class="unsupported-preview">
          <el-empty description="不支持的文件格式预览">
            <el-button type="primary" @click="handleDownload">
              <el-icon><Download /></el-icon>
              下载文件
            </el-button>
          </el-empty>
        </div>
      </div>
      
      <!-- 加载失败 -->
      <div v-if="!loading && error" class="error-content">
        <el-result
          icon="error"
          title="预览失败"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="handleRetry">
              <el-icon><Refresh /></el-icon>
              重试
            </el-button>
            <el-button @click="handleDownload">
              <el-icon><Download /></el-icon>
              下载文件
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleDownload">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Refresh } from '@element-plus/icons-vue'
import { getReportPreview, downloadReport } from '@/api/background-check'

interface Props {
  modelValue: boolean
  reportId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const previewData = ref<any>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听报表ID变化
watch(
  () => props.reportId,
  (newId) => {
    if (newId && props.modelValue) {
      fetchPreviewData()
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible && props.reportId) {
      fetchPreviewData()
    } else {
      // 重置数据
      previewData.value = null
      error.value = ''
    }
  }
)

// 获取预览数据
const fetchPreviewData = async () => {
  if (!props.reportId) return
  
  try {
    loading.value = true
    error.value = ''
    
    const response = await getReportPreview(props.reportId)
    previewData.value = response.data
  } catch (err: any) {
    console.error('获取预览数据失败:', err)
    error.value = err.message || '获取预览数据失败'
  } finally {
    loading.value = false
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 下载文件
const handleDownload = async () => {
  if (!props.reportId) return
  
  try {
    await downloadReport(props.reportId)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 重试
const handleRetry = () => {
  fetchPreviewData()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.preview-container {
  min-height: 400px;
}

.preview-content {
  width: 100%;
}

.pdf-preview {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.office-preview {
  width: 100%;
}

.preview-info {
  margin-bottom: 16px;
}

.image-preview {
  width: 100%;
}

.preview-pages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-page {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.page-header {
  padding: 8px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-size: 14px;
  color: #606266;
}

.preview-page img {
  width: 100%;
  height: auto;
  display: block;
}

.no-preview,
.unsupported-preview {
  padding: 40px 20px;
}

.error-content {
  padding: 40px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-result) {
  padding: 40px 0;
}

:deep(.el-alert) {
  margin-bottom: 0;
}
</style>