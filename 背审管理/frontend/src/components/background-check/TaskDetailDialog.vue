<!-- 
  任务详情弹窗组件
  可复用组件：适用于背审结果处理模块
-->
<template>
  <el-dialog
    v-model="visible"
    :title="`任务详情 - ${taskData?.taskNo}`"
    width="900px"
    :before-close="handleClose"
  >
    <div v-if="taskData" class="task-detail-content">
      <!-- 任务基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>任务编号</label>
            <span>{{ taskData.taskNo }}</span>
          </div>
          <div class="info-item">
            <label>任务标题</label>
            <span>{{ taskData.title }}</span>
          </div>
          <div class="info-item">
            <label>任务状态</label>
            <el-tag :type="getStatusColor(taskData.status)" size="small">
              {{ getStatusText(taskData.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>优先级</label>
            <el-tag :type="getPriorityColor(taskData.priority)" size="small">
              {{ getPriorityText(taskData.priority) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>创建人</label>
            <span>{{ taskData.createdByName }}</span>
          </div>
          <div class="info-item">
            <label>创建时间</label>
            <span>{{ taskData.createdAt }}</span>
          </div>
          <div class="info-item">
            <label>截止时间</label>
            <span :class="{ 'overdue-text': taskData.isOverdue }">
              {{ taskData.dueDate }}
              <el-tag v-if="taskData.isOverdue" type="danger" size="small" class="overdue-tag">
                逾期{{ taskData.overdueBy }}天
              </el-tag>
            </span>
          </div>
          <div class="info-item">
            <label>完成时间</label>
            <span>{{ taskData.completedAt || '-' }}</span>
          </div>
        </div>
        
        <div v-if="taskData.description" class="description-section">
          <label>任务描述</label>
          <div class="description-content">{{ taskData.description }}</div>
        </div>
      </div>

      <!-- 分配信息 -->
      <div class="detail-section">
        <h4 class="section-title">分配信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>分配方式</label>
            <span>{{ taskData.assignmentType === 'department' ? '部门分配' : '个人分配' }}</span>
          </div>
          <div class="info-item">
            <label>处理部门</label>
            <span>{{ taskData.assignedToOrgName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>处理人员</label>
            <span>{{ taskData.assignedToUserName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>分配时间</label>
            <span>{{ taskData.assignedAt || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 进度统计 -->
      <div class="detail-section">
        <h4 class="section-title">进度统计</h4>
        <div class="progress-section">
          <div class="progress-overview">
            <div class="progress-item">
              <div class="progress-label">总人数</div>
              <div class="progress-value">{{ taskData.progress.total }}</div>
            </div>
            <div class="progress-item">
              <div class="progress-label">已完成</div>
              <div class="progress-value success">{{ taskData.progress.completed }}</div>
            </div>
            <div class="progress-item">
              <div class="progress-label">待处理</div>
              <div class="progress-value warning">{{ taskData.progress.pending }}</div>
            </div>
            <div class="progress-item">
              <div class="progress-label">完成率</div>
              <div class="progress-value">{{ taskData.progress.percentage }}%</div>
            </div>
          </div>
          
          <div class="progress-bar-section">
            <el-progress 
              :percentage="taskData.progress.percentage" 
              :stroke-width="12"
              :status="getProgressStatus(taskData)"
            />
          </div>
        </div>
      </div>

      <!-- 关联人员列表 -->
      <div class="detail-section">
        <h4 class="section-title">关联人员</h4>
        <el-table :data="personnelList" style="width: 100%" max-height="300">
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="idCard" label="身份证号" width="150">
            <template #default="scope">
              {{ maskIdCard(scope.row.idCard) }}
            </template>
          </el-table-column>
          <el-table-column prop="department" label="部门" width="120" />
          <el-table-column prop="status" label="处理状态" width="100">
            <template #default="scope">
              <el-tag :type="getPersonnelStatusColor(scope.row.status)" size="small">
                {{ getPersonnelStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="result" label="背审结果" width="100">
            <template #default="scope">
              <el-tag 
                v-if="scope.row.result" 
                :type="getResultColor(scope.row.result)" 
                size="small"
              >
                {{ getResultText(scope.row.result) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="processedAt" label="处理时间" width="120">
            <template #default="scope">
              {{ scope.row.processedAt ? formatDate(scope.row.processedAt) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="processedByName" label="处理人" width="100">
            <template #default="scope">
              {{ scope.row.processedByName || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 催办记录 -->
      <div v-if="reminderRecords.length > 0" class="detail-section">
        <h4 class="section-title">催办记录</h4>
        <el-timeline>
          <el-timeline-item
            v-for="record in reminderRecords"
            :key="record.id"
            :timestamp="record.sentAt"
            type="warning"
          >
            <div class="reminder-item">
              <div class="reminder-content">{{ record.content }}</div>
              <div class="reminder-meta">
                发送人：{{ record.sentByName }} | 
                发送给：{{ record.sentToOrg }}
                <el-tag v-if="record.readAt" type="success" size="small">已读</el-tag>
                <el-tag v-else type="info" size="small">未读</el-tag>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button 
          v-if="taskData?.isOverdue" 
          type="warning" 
          @click="handleRemind"
        >
          <el-icon><Bell /></el-icon>
          发送催办
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出详情
        </el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Bell, Download } from '@element-plus/icons-vue'
import { backgroundCheckTasksMockData, taskPersonnelRelationsMockData, reminderRecordsMockData } from '@/data/backgroundCheckMockData'
import type { BackgroundCheckTask } from '@/data/backgroundCheckMockData'

interface Props {
  modelValue: boolean
  taskId?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'remind': [taskId: string]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const taskData = ref<BackgroundCheckTask | null>(null)
const personnelList = ref([])
const reminderRecords = ref([])

// 工具函数
const maskIdCard = (idCard: string) => {
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const formatDate = (dateStr: string) => {
  return dateStr.split(' ')[0]
}

const getStatusColor = (status: string) => {
  const colors = {
    pending: 'info',
    assigned: 'primary',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '待分配',
    assigned: '已分配',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getPriorityColor = (priority: string) => {
  const colors = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return colors[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts = {
    low: '普通',
    medium: '中等',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || '未知'
}

const getProgressStatus = (task: BackgroundCheckTask) => {
  if (task.isOverdue) return 'exception'
  if (task.progress.percentage === 100) return 'success'
  return undefined
}

const getPersonnelStatusColor = (status: string) => {
  const colors = {
    pending: 'info',
    processing: 'primary',
    completed: 'success'
  }
  return colors[status] || 'info'
}

const getPersonnelStatusText = (status: string) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成'
  }
  return texts[status] || '未知'
}

const getResultColor = (result: string) => {
  const colors = {
    normal: 'success',
    abnormal: 'warning',
    risk: 'danger'
  }
  return colors[result] || 'info'
}

const getResultText = (result: string) => {
  const texts = {
    normal: '正常',
    abnormal: '异常',
    risk: '风险'
  }
  return texts[result] || '未知'
}

const handleClose = () => {
  visible.value = false
}

const handleRemind = () => {
  if (taskData.value) {
    emit('remind', taskData.value.id)
  }
}

const handleExport = () => {
  ElMessage.success('任务详情导出成功')
}

// 监听任务ID变化，加载数据
watch(() => props.taskId, async (newId) => {
  if (newId) {
    // 这里应该调用API获取任务详情
    // 暂时使用Mock数据
    taskData.value = backgroundCheckTasksMockData.find(task => task.id === newId) || null
    
    // 加载关联人员数据
    const relations = taskPersonnelRelationsMockData.filter(rel => rel.taskId === newId)
    personnelList.value = relations.map(rel => ({
      id: rel.personnelId,
      name: `人员${rel.personnelId}`,
      idCard: `13060219850115${String(rel.personnelId).padStart(4, '0')}`,
      department: '测试部门',
      status: rel.status,
      result: rel.result,
      processedAt: rel.processedAt,
      processedByName: rel.processedByName
    }))
    
    // 加载催办记录
    reminderRecords.value = reminderRecordsMockData.filter(record => record.taskId === newId)
  }
}, { immediate: true })
</script>

<style scoped>
.task-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #303133;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-tag {
  margin-left: 8px;
}

.description-section {
  margin-top: 16px;
}

.description-section label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  display: block;
  margin-bottom: 8px;
}

.description-content {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #303133;
  line-height: 1.5;
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.progress-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.progress-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.progress-value.success {
  color: #67c23a;
}

.progress-value.warning {
  color: #e6a23c;
}

.reminder-item {
  margin-bottom: 8px;
}

.reminder-content {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.reminder-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .progress-overview {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
