<template>
  <el-dialog v-model="dialogVisible" title="黑名单详情" width="600px" :before-close="handleClose">
    <div v-loading="loading" class="detail-content">
      <el-descriptions v-if="blacklistData" :column="2" border>
        <el-descriptions-item label="姓名">{{ blacklistData.name }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ blacklistData.idCard }}</el-descriptions-item>
        <el-descriptions-item label="所属单位">{{ blacklistData.organization }}</el-descriptions-item>
        <el-descriptions-item label="黑名单类型">
          <el-tag type="danger">{{ getBlacklistTypeText(blacklistData.blacklistType) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="加入原因" :span="2">
          {{ blacklistData.reason }}
        </el-descriptions-item>
        <el-descriptions-item label="操作人">{{ blacklistData.operatorName }}</el-descriptions-item>
        <el-descriptions-item label="生效日期">{{ blacklistData.effectiveDate }}</el-descriptions-item>
        <el-descriptions-item label="失效日期">
          {{ blacklistData.expiryDate || '永久有效' }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(blacklistData.status)">
            {{ getStatusText(blacklistData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">
          {{ blacklistData.createdAt }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button v-if="blacklistData?.status === 1" type="warning" @click="blacklistId && $emit('edit', blacklistId)">编辑</el-button>
      <el-button v-if="blacklistData?.status === 1" type="danger" @click="blacklistId && $emit('remove', blacklistId)">解除</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { getBlacklistDetail } from '@/api/background-check'

interface Props {
  modelValue: boolean
  blacklistId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', id: number): void
  (e: 'remove', id: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const blacklistData = ref<any>(null)

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(() => props.blacklistId, (newId) => {
  if (newId && props.modelValue) {
    fetchBlacklistDetail()
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && props.blacklistId) {
    fetchBlacklistDetail()
  } else {
    blacklistData.value = null
  }
})

const fetchBlacklistDetail = async () => {
  if (!props.blacklistId) return
  
  try {
    loading.value = true
    const response = await getBlacklistDetail(props.blacklistId)
    blacklistData.value = response.data
  } catch (error) {
    console.error('获取黑名单详情失败:', error)
  } finally {
    loading.value = false
  }
}

const getBlacklistTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '违法犯罪',
    2: '精神疾病',
    3: '吸毒记录',
    4: '其他'
  }
  return typeMap[type] || '未知'
}

const getStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    1: 'danger',
    2: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    1: '生效中',
    2: '已解除'
  }
  return textMap[status] || '未知'
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.detail-content {
  min-height: 200px;
}
</style>