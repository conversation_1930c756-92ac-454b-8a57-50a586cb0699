<!-- 
  发起背审任务弹窗组件
  可复用组件：适用于背审人员管理和背审结果处理模块
-->
<template>
  <el-dialog
    v-model="visible"
    title="发起背审任务"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        <el-form-item label="任务标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入任务标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="formData.priority">
            <el-radio label="low">普通</el-radio>
            <el-radio label="medium">中等</el-radio>
            <el-radio label="high">高</el-radio>
            <el-radio label="urgent">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 分配信息 -->
      <div class="form-section">
        <h4 class="section-title">分配信息</h4>
        <el-form-item label="分配方式" prop="assignmentType">
          <el-radio-group v-model="formData.assignmentType" @change="handleAssignmentTypeChange">
            <el-radio label="department">分配给部门</el-radio>
            <el-radio label="individual">分配给个人</el-radio>
          </el-radio-group>
          <div class="assignment-tip">
            <el-text size="small" type="info">
              {{ assignmentTip }}
            </el-text>
          </div>
        </el-form-item>
        
        <el-form-item 
          v-if="formData.assignmentType === 'department'"
          label="处理部门" 
          prop="assignedToOrg"
        >
          <el-select
            v-model="formData.assignedToOrg"
            placeholder="请选择处理部门"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="dept in departments"
              :key="dept.value"
              :label="dept.label"
              :value="dept.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item 
          v-if="formData.assignmentType === 'individual'"
          label="处理人员" 
          prop="assignedToUser"
        >
          <el-select
            v-model="formData.assignedToUser"
            placeholder="请选择处理人员"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in users"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="截止时间" prop="dueDate">
          <el-date-picker
            v-model="formData.dueDate"
            type="datetime"
            placeholder="请选择截止时间"
            style="width: 100%"
            :disabled-date="disabledDate"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </div>

      <!-- 影响范围 -->
      <div class="form-section">
        <h4 class="section-title">影响范围</h4>

        <el-form-item label="操作范围" prop="operationScope">
          <el-radio-group v-model="formData.operationScope" @change="handleScopeChange">
            <el-radio
              label="selected"
              :disabled="selectedCount === 0"
            >
              选中的人员 ({{ selectedCount }}人)
            </el-radio>
            <el-radio label="filtered">
              全部筛选结果 ({{ totalCount }}人)
            </el-radio>
          </el-radio-group>
          <div class="scope-tip">
            <el-text size="small" type="info">
              {{ scopeTip }}
            </el-text>
          </div>
        </el-form-item>

        <div class="preview-section">
          <el-text size="small" type="info">将对以下人员发起背审任务：</el-text>
          <div class="personnel-preview">
            <el-tag
              v-for="(name, index) in selectedPersonnelPreview"
              :key="index"
              size="small"
              class="preview-tag"
            >
              {{ name }}
            </el-tag>
            <span v-if="currentOperationCount > 5" class="more-indicator">
              等{{ currentOperationCount }}人
            </span>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确认发起
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
  selectedCount: number
  totalCount: number
  selectedPersonnel?: string[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [formData: any]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive({
  title: '',
  description: '',
  priority: 'medium',
  assignmentType: 'department',
  assignedToOrg: '',
  assignedToUser: '',
  dueDate: '',
  operationScope: 'selected' // 默认选择已选中的人员
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  assignedToOrg: [
    { 
      required: true, 
      message: '请选择处理部门', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (formData.assignmentType === 'department' && !value) {
          callback(new Error('请选择处理部门'))
        } else {
          callback()
        }
      }
    }
  ],
  assignedToUser: [
    { 
      required: true, 
      message: '请选择处理人员', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (formData.assignmentType === 'individual' && !value) {
          callback(new Error('请选择处理人员'))
        } else {
          callback()
        }
      }
    }
  ],
  dueDate: [
    { required: true, message: '请选择截止时间', trigger: 'change' }
  ]
}

// Mock数据
const departments = [
  { label: '莲池分局', value: 'org-001' },
  { label: '竞秀分局', value: 'org-002' },
  { label: '满城分局', value: 'org-003' },
  { label: '清苑分局', value: 'org-004' },
  { label: '徐水分局', value: 'org-005' }
]

const users = [
  { label: '李审查员', value: 'user-001' },
  { label: '王审查员', value: 'user-002' },
  { label: '张审查员', value: 'user-003' },
  { label: '刘审查员', value: 'user-004' }
]

// 计算属性
const assignmentTip = computed(() => {
  return formData.assignmentType === 'department'
    ? '部门模式下该部门所有人员都可处理任务'
    : '个人模式下仅指定人员可以处理任务'
})

const scopeTip = computed(() => {
  return formData.operationScope === 'selected'
    ? '仅对当前选中的人员执行操作'
    : '对当前筛选条件下的所有人员执行操作'
})

const currentOperationCount = computed(() => {
  return formData.operationScope === 'selected' ? props.selectedCount : props.totalCount
})

const selectedPersonnelPreview = computed(() => {
  // 这里应该从props中获取选中人员的姓名
  // 暂时使用Mock数据
  const mockNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const count = currentOperationCount.value
  return mockNames.slice(0, Math.min(5, count))
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 弹窗打开时，根据选中人员数量设置默认操作范围
    formData.operationScope = props.selectedCount > 0 ? 'selected' : 'filtered'
  }
})

// 方法
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
}

const handleAssignmentTypeChange = () => {
  // 切换分配方式时清空相关字段
  formData.assignedToOrg = ''
  formData.assignedToUser = ''
}

const handleScopeChange = () => {
  // 操作范围变化时可以做一些处理
  console.log('操作范围变更为:', formData.operationScope)
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    title: '',
    description: '',
    priority: 'medium',
    assignmentType: 'department',
    assignedToOrg: '',
    assignedToUser: '',
    dueDate: '',
    operationScope: props.selectedCount > 0 ? 'selected' : 'filtered'
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 确认对话框
    const operationCount = currentOperationCount.value
    const scopeText = formData.operationScope === 'selected' ? '选中' : '筛选结果'
    const confirmMessage = `确定要对${scopeText}的 ${operationCount} 人发起背审任务吗？`

    await ElMessageBox.confirm(confirmMessage, '确认发起', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    submitting.value = true

    // 提交数据
    emit('submit', {
      ...formData,
      operationCount,
      isSelectedScope: formData.operationScope === 'selected'
    })
    
    ElMessage.success('背审任务发起成功')
    handleClose()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发起背审任务失败:', error)
      ElMessage.error('发起背审任务失败')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.assignment-tip,
.scope-tip {
  margin-top: 4px;
}

.preview-section {
  margin-top: 12px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.personnel-preview {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.preview-tag {
  margin: 0;
}

.more-indicator {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
