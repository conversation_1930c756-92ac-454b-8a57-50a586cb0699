<template>
  <div class="processing-operation">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="operation-form">
      <div class="form-section">
        <h4 class="section-title">处理状态设置</h4>
        
        <el-form-item label="处理状态" prop="processingStatus">
          <el-radio-group v-model="formData.processingStatus" @change="handleStatusChange">
            <el-radio :value="0" class="status-radio">
              <div class="radio-content">
                <span class="radio-label">无需处理</span>
                <span class="radio-desc">该人员异常情况不需要特殊处理</span>
              </div>
            </el-radio>
            <el-radio :value="1" class="status-radio">
              <div class="radio-content">
                <span class="radio-label">加入重点关注列表</span>
                <span class="radio-desc">需要定期关注该人员的工作状态</span>
              </div>
            </el-radio>
            <el-radio :value="2" class="status-radio">
              <div class="radio-content">
                <span class="radio-label">加入黑名单列表</span>
                <span class="radio-desc">禁止该人员从事相关工作</span>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          :label="getReasonLabel()" 
          :prop="formData.processingStatus === 0 ? 'optionalReason' : 'reason'"
        >
          <el-input
            v-model="formData.reason"
            type="textarea"
            :rows="4"
            :placeholder="getReasonPlaceholder()"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </div>

      <div class="form-section">
        <h4 class="section-title">当前处理状态</h4>
        <div class="current-status">
          <el-tag 
            :type="getProcessingStatusType(currentStatus)" 
            size="large"
            class="current-status-tag"
          >
            {{ getProcessingStatusText(currentStatus) }}
          </el-tag>
          <span class="status-desc">
            {{ getStatusDescription(currentStatus) }}
          </span>
        </div>
      </div>

      <div class="form-actions">
        <el-button @click="handleReset">重置</el-button>
        <el-button 
          type="primary" 
          :loading="loading" 
          @click="handleSubmit"
          :disabled="!hasChanges"
        >
          {{ getSubmitButtonText() }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { getProcessingStatusText } from '@/data/personnelMockData'
import { updateProcessingStatus } from '@/api/background-check'

interface Props {
  personnelData: any
}

interface Emits {
  (e: 'processing-updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = reactive({
  processingStatus: 0,
  reason: ''
})

const currentStatus = computed(() => props.personnelData?.processingStatus || 0)

const hasChanges = computed(() => {
  return formData.processingStatus !== currentStatus.value || 
         (formData.reason.trim() !== '')
})

// 监听人员数据变化，更新表单
watch(() => props.personnelData, (newData) => {
  if (newData) {
    formData.processingStatus = newData.processingStatus || 0
    formData.reason = ''
  }
}, { immediate: true })

const rules = {
  reason: [
    { required: true, message: '请填写处理原因', trigger: 'blur' },
    { min: 10, message: '处理原因至少需要10个字符', trigger: 'blur' }
  ],
  optionalReason: [
    { min: 5, message: '原因至少需要5个字符', trigger: 'blur' }
  ]
}

const getReasonLabel = () => {
  return formData.processingStatus === 0 ? '处理说明（可选）' : '处理原因'
}

const getReasonPlaceholder = () => {
  const placeholders = {
    0: '请说明为什么不需要处理（可选）',
    1: '请详细说明加入重点关注的原因',
    2: '请详细说明加入黑名单的原因'
  }
  return placeholders[formData.processingStatus as keyof typeof placeholders] || ''
}

const getProcessingStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',
    1: 'warning',
    2: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusDescription = (status: number) => {
  const descriptions = {
    0: '该人员当前无需特殊处理',
    1: '该人员已被列入重点关注名单',
    2: '该人员已被列入黑名单'
  }
  return descriptions[status as keyof typeof descriptions] || ''
}

const getSubmitButtonText = () => {
  if (formData.processingStatus === currentStatus.value) {
    return '更新处理记录'
  }
  return '确认处理'
}

const handleStatusChange = () => {
  formData.reason = ''
  formRef.value?.clearValidate()
}

const handleReset = () => {
  formData.processingStatus = currentStatus.value
  formData.reason = ''
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value || !props.personnelData) return

  try {
    // 验证表单
    if (formData.processingStatus !== 0) {
      await formRef.value.validateField('reason')
    } else if (formData.reason.trim()) {
      await formRef.value.validateField('optionalReason')
    }

    loading.value = true
    
    await updateProcessingStatus({
      personnelId: props.personnelData.id,
      fromStatus: currentStatus.value,
      toStatus: formData.processingStatus,
      reason: formData.reason.trim()
    })

    ElMessage.success('处理状态更新成功')
    emit('processing-updated')
    
  } catch (error) {
    console.error('更新处理状态失败:', error)
    ElMessage.error('更新处理状态失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.processing-operation {
  height: 100%;
  overflow-y: auto;
}

.operation-form {
  max-width: 600px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.status-radio {
  display: block;
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.status-radio:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.status-radio.is-checked {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.radio-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.radio-label {
  font-weight: 500;
  color: #303133;
}

.radio-desc {
  font-size: 12px;
  color: #909399;
}

.current-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-status-tag {
  font-weight: 500;
}

.status-desc {
  color: #606266;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-radio__label) {
  padding-left: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
