<template>
  <el-dialog v-model="dialogVisible" title="发送通知" width="600px" :before-close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="接收单位" prop="recipient">
        <el-input v-model="formData.recipient" placeholder="请输入接收单位" />
      </el-form-item>
      <el-form-item label="通知类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择通知类型">
          <el-option label="风险提醒" :value="1" />
          <el-option label="整改建议" :value="2" />
          <el-option label="人员调整" :value="3" />
          <el-option label="其他" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="通知标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入通知标题" />
      </el-form-item>
      <el-form-item label="通知内容" prop="content">
        <el-input v-model="formData.content" type="textarea" :rows="6" placeholder="请输入通知内容" />
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="formData.priority" placeholder="请选择优先级">
          <el-option label="普通" :value="1" />
          <el-option label="重要" :value="2" />
          <el-option label="紧急" :value="3" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">发送通知</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { sendNotification } from '@/api/background-check'

interface Props {
  modelValue: boolean
  personnelId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = reactive({
  recipient: '',
  type: 1,
  title: '',
  content: '',
  priority: 1
})

const rules = {
  recipient: [{ required: true, message: '请输入接收单位', trigger: 'blur' }],
  type: [{ required: true, message: '请选择通知类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入通知标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入通知内容', trigger: 'blur' }]
}

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(() => props.modelValue, (visible) => {
  if (!visible) {
    resetForm()
  }
})

const resetForm = () => {
  Object.assign(formData, {
    recipient: '',
    type: 1,
    title: '',
    content: '',
    priority: 1
  })
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value || !props.personnelId) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    await sendNotification({
      personnelIds: [props.personnelId],
      organizationId: 1, // 默认组织ID
      title: formData.title,
      content: formData.content,
      type: formData.type
    })
    ElMessage.success('通知发送成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('发送通知失败:', error)
    ElMessage.error('发送通知失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>