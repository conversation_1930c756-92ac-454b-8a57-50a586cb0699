<!-- 
  批量操作工具栏组件
  可复用组件：适用于背审人员管理和背审结果处理模块
-->
<template>
  <el-card class="batch-toolbar" shadow="never">
    <div class="toolbar-content">
      <!-- 操作模式选择 -->
      <div class="operation-mode">
        <el-radio-group v-model="operationMode" @change="handleModeChange">
          <el-radio label="selected">
            选中人员 ({{ selectedCount }})
          </el-radio>
          <el-radio label="filtered">
            当前筛选结果 ({{ filteredCount }})
          </el-radio>
        </el-radio-group>
      </div>
      
      <!-- 操作按钮 -->
      <div class="operation-buttons">
        <template v-for="action in availableActions" :key="action.key">
          <el-button 
            :type="action.type"
            :disabled="action.disabled"
            @click="handleAction(action.key)"
          >
            <el-icon v-if="action.icon">
              <component :is="action.icon" />
            </el-icon>
            {{ action.label }}
          </el-button>
        </template>
        
        <!-- 更多操作下拉菜单 -->
        <el-dropdown v-if="moreActions.length > 0" @command="handleAction">
          <el-button>
            更多操作
            <el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item 
                v-for="action in moreActions" 
                :key="action.key"
                :command="action.key"
                :disabled="action.disabled"
              >
                <el-icon v-if="action.icon">
                  <component :is="action.icon" />
                </el-icon>
                {{ action.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  ArrowDown, 
  Plus, 
  Delete, 
  Download, 
  Refresh,
  Warning,
  Check
} from '@element-plus/icons-vue'

interface ActionItem {
  key: string
  label: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'
  icon?: any
  disabled?: boolean
  show?: boolean
}

interface Props {
  selectedCount: number
  filteredCount: number
  currentStatus: 'pending' | 'in_progress' | 'completed' | 'all'
  actions?: ActionItem[]
  permissions?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  actions: () => [],
  permissions: () => []
})

const emit = defineEmits<{
  modeChange: [mode: 'selected' | 'filtered']
  action: [actionKey: string, mode: 'selected' | 'filtered']
}>()

const operationMode = ref<'selected' | 'filtered'>('selected')

// 根据当前状态获取可用操作
const getActionsForStatus = (status: string): ActionItem[] => {
  const baseActions = [
    {
      key: 'export',
      label: '导出Excel',
      type: 'default' as const,
      icon: Download,
      show: true
    }
  ]

  switch (status) {
    case 'pending':
      return [
        {
          key: 'start_check',
          label: '发起背审',
          type: 'primary' as const,
          icon: Plus,
          show: true
        },
        ...baseActions
      ]
    case 'in_progress':
      return [
        {
          key: 'cancel_task',
          label: '撤销任务',
          type: 'warning' as const,
          icon: Warning,
          show: true
        },
        ...baseActions
      ]
    case 'completed':
      return [
        {
          key: 'restart_check',
          label: '重新发起',
          type: 'success' as const,
          icon: Refresh,
          show: true
        },
        ...baseActions
      ]
    default:
      return baseActions
  }
}

// 可用操作列表
const availableActions = computed((): ActionItem[] => {
  const statusActions = getActionsForStatus(props.currentStatus)
  const customActions = props.actions || []
  
  // 合并自定义操作和状态操作
  const allActions = [...statusActions, ...customActions]
  
  return allActions
    .filter(action => action.show !== false)
    .filter(action => {
      // 权限检查
      if (props.permissions.length > 0) {
        // 这里可以添加具体的权限检查逻辑
        return true
      }
      return true
    })
    .map(action => ({
      ...action,
      disabled: action.disabled || (
        operationMode.value === 'selected' ? props.selectedCount === 0 : props.filteredCount === 0
      )
    }))
    .slice(0, 3) // 主要操作最多显示3个
})

// 更多操作列表
const moreActions = computed((): ActionItem[] => {
  const statusActions = getActionsForStatus(props.currentStatus)
  const customActions = props.actions || []
  const allActions = [...statusActions, ...customActions]
  
  return allActions
    .filter(action => action.show !== false)
    .slice(3) // 超过3个的放到更多操作中
    .map(action => ({
      ...action,
      disabled: action.disabled || (
        operationMode.value === 'selected' ? props.selectedCount === 0 : props.filteredCount === 0
      )
    }))
})

const handleModeChange = (mode: 'selected' | 'filtered') => {
  emit('modeChange', mode)
}

const handleAction = (actionKey: string) => {
  emit('action', actionKey, operationMode.value)
}
</script>

<style scoped>
.batch-toolbar {
  margin-bottom: 16px;
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.operation-mode {
  flex: 1;
}

.operation-mode .el-radio-group {
  display: flex;
  gap: 20px;
}

.operation-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.operation-buttons .el-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

@media (max-width: 768px) {
  .toolbar-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .operation-mode .el-radio-group {
    justify-content: center;
  }
  
  .operation-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
