<!-- 
  人员详情抽屉组件
  可复用组件：适用于背审人员管理和背审结果处理模块
-->
<template>
  <el-drawer
    v-model="visible"
    title="人员背审详情"
    :size="600"
    direction="rtl"
    :before-close="handleClose"
  >
    <div v-if="personnelData" class="drawer-content">
      <!-- 人员基本信息头部 -->
      <div class="personnel-header">
        <div class="avatar-section">
          <el-avatar :size="60" class="personnel-avatar">
            {{ personnelData.name.charAt(0) }}
          </el-avatar>
        </div>
        <div class="basic-info">
          <h3 class="personnel-name">{{ personnelData.name }}</h3>
          <div class="personnel-meta">
            <el-tag :type="getPersonnelTypeColor(personnelData.personnelType)" size="small">
              {{ getPersonnelTypeText(personnelData.personnelType) }}
            </el-tag>
            <el-tag 
              :type="getStatusColor(personnelData.backgroundCheckStatus)" 
              size="small"
              class="status-tag"
            >
              {{ getStatusText(personnelData.backgroundCheckStatus) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <div class="info-section">
            <div class="info-grid">
              <div class="info-item">
                <label>姓名</label>
                <span>{{ personnelData.name }}</span>
              </div>
              <div class="info-item">
                <label>身份证号</label>
                <span>{{ maskIdCard(personnelData.idCard) }}</span>
              </div>
              <div class="info-item">
                <label>人员类型</label>
                <span>{{ getPersonnelTypeText(personnelData.personnelType) }}</span>
              </div>
              <div class="info-item">
                <label>所属部门</label>
                <span>{{ personnelData.department }}</span>
              </div>
              <div class="info-item">
                <label>职位</label>
                <span>{{ personnelData.position }}</span>
              </div>
              <div class="info-item">
                <label>入职时间</label>
                <span>{{ personnelData.entryDate }}</span>
              </div>
              <div class="info-item">
                <label>联系电话</label>
                <span>{{ maskPhone(personnelData.phone) }}</span>
              </div>
              <div class="info-item">
                <label>风险等级</label>
                <el-tag :type="getRiskLevelColor(personnelData.riskLevel)" size="small">
                  {{ getRiskLevelText(personnelData.riskLevel) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>背审次数</label>
                <span>{{ personnelData.checkCount || 0 }}次</span>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="当前状态" name="status">
          <div class="info-section">
            <div v-if="personnelData.backgroundCheckStatus === 'in_progress'" class="current-task">
              <h4>当前背审任务</h4>
              <div class="task-info">
                <div class="info-item">
                  <label>任务编号</label>
                  <span>{{ personnelData.currentTaskId || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>任务状态</label>
                  <el-tag type="primary" size="small">进行中</el-tag>
                </div>
                <div class="info-item">
                  <label>处理部门</label>
                  <span>莲池分局</span>
                </div>
                <div class="info-item">
                  <label>发起时间</label>
                  <span>2024-01-15 09:00</span>
                </div>
                <div class="info-item">
                  <label>截止时间</label>
                  <span>2024-02-01 18:00</span>
                </div>
                <div class="info-item">
                  <label>剩余时间</label>
                  <span class="time-remaining">15天</span>
                </div>
              </div>
            </div>
            <div v-else class="status-info">
              <div class="info-item">
                <label>当前状态</label>
                <el-tag :type="getStatusColor(personnelData.backgroundCheckStatus)" size="small">
                  {{ getStatusText(personnelData.backgroundCheckStatus) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>最后背审时间</label>
                <span>{{ personnelData.lastCheckDate || '-' }}</span>
              </div>
              <div class="info-item">
                <label>下次背审时间</label>
                <span>{{ personnelData.nextCheckDate || '-' }}</span>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="背审历史" name="history">
          <div class="info-section">
            <el-timeline>
              <el-timeline-item
                v-for="record in mockHistoryRecords"
                :key="record.id"
                :timestamp="record.date"
                :type="record.type"
              >
                <div class="history-item">
                  <div class="history-title">{{ record.title }}</div>
                  <div class="history-desc">{{ record.description }}</div>
                  <div v-if="record.result" class="history-result">
                    <el-tag :type="record.resultType" size="small">
                      {{ record.result }}
                    </el-tag>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>

        <el-tab-pane label="操作记录" name="operations">
          <div class="info-section">
            <el-table :data="mockOperationRecords" style="width: 100%">
              <el-table-column prop="date" label="操作时间" width="150" />
              <el-table-column prop="operator" label="操作人" width="100" />
              <el-table-column prop="action" label="操作类型" width="120" />
              <el-table-column prop="description" label="操作描述" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 快速操作按钮 -->
      <div class="quick-actions">
        <el-button type="primary" @click="viewTaskDetail">
          查看任务详情
        </el-button>
        <el-button @click="exportRecord">
          导出记录
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { getPersonnelTypeText } from '@/data/personnelMockData'
import type { PersonnelData } from '@/data/personnelMockData'

interface Props {
  modelValue: boolean
  personnelId?: number
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'view-task': [taskId: string]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeTab = ref('basic')
const personnelData = ref<PersonnelData | null>(null)

// Mock数据
const mockHistoryRecords = [
  {
    id: 1,
    date: '2024-01-15',
    title: '发起背审任务',
    description: '新员工入职背审 (BG20240115001)',
    type: 'primary',
    result: '进行中',
    resultType: 'primary'
  },
  {
    id: 2,
    date: '2023-06-01',
    title: '完成背审',
    description: '定期背景审查',
    type: 'success',
    result: '正常',
    resultType: 'success'
  },
  {
    id: 3,
    date: '2023-01-01',
    title: '入职背审',
    description: '新员工入职背景审查',
    type: 'success',
    result: '正常',
    resultType: 'success'
  }
]

const mockOperationRecords = [
  {
    date: '2024-01-15 09:00',
    operator: '张管理员',
    action: '发起背审',
    description: '发起新员工入职背审任务'
  },
  {
    date: '2023-12-01 14:30',
    operator: '李审查员',
    action: '状态更新',
    description: '更新风险等级为低风险'
  }
]

// 工具函数
const maskIdCard = (idCard: string) => {
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const maskPhone = (phone: string) => {
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

const getPersonnelTypeColor = (type: number) => {
  const colors = { 1: 'primary', 2: 'success', 3: 'warning' }
  return colors[type] || 'info'
}

const getStatusColor = (status?: string) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success'
  }
  return colors[status] || 'info'
}

const getStatusText = (status?: string) => {
  const texts = {
    pending: '待背审',
    in_progress: '背审中',
    completed: '已完成'
  }
  return texts[status] || '未知'
}

const getRiskLevelColor = (level?: string) => {
  const colors = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return colors[level] || 'info'
}

const getRiskLevelText = (level?: string) => {
  const texts = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return texts[level] || '未评估'
}

const handleClose = () => {
  visible.value = false
}

const viewTaskDetail = () => {
  if (personnelData.value?.currentTaskId) {
    emit('view-task', personnelData.value.currentTaskId)
  }
}

const exportRecord = () => {
  // 导出记录逻辑
  console.log('导出记录')
}

// 监听人员ID变化，加载数据
watch(() => props.personnelId, async (newId) => {
  if (newId) {
    // 这里应该调用API获取人员详情
    // 暂时使用Mock数据
    personnelData.value = {
      id: newId,
      name: '张建国',
      idCard: '130602198501151234',
      phone: '13803128001',
      email: '<EMAIL>',
      department: '保定市公安局',
      position: '安保队长',
      personnelType: 1,
      backgroundCheckResult: 1,
      status: 1,
      entryDate: '2020-03-15',
      region: '保定市竞秀区',
      createTime: '2020-03-15 09:00:00',
      updateTime: '2023-12-01 10:30:00',
      backgroundCheckStatus: 'in_progress',
      currentTaskId: 'task-001',
      lastCheckDate: '2023-06-01',
      nextCheckDate: '2024-06-01',
      checkCount: 3,
      riskLevel: 'low'
    }
  }
}, { immediate: true })
</script>

<style scoped>
.drawer-content {
  padding: 0 4px;
}

.personnel-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.personnel-avatar {
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  font-weight: bold;
}

.personnel-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.personnel-meta {
  display: flex;
  gap: 8px;
}

.status-tag {
  margin-left: 8px;
}

.detail-tabs {
  margin-bottom: 20px;
}

.info-section {
  padding: 16px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #303133;
}

.current-task h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.task-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.time-remaining {
  color: #e6a23c;
  font-weight: 500;
}

.history-item {
  margin-bottom: 8px;
}

.history-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.history-desc {
  color: #606266;
  font-size: 13px;
  margin-bottom: 4px;
}

.quick-actions {
  display: flex;
  gap: 12px;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

@media (max-width: 768px) {
  .info-grid,
  .task-info {
    grid-template-columns: 1fr;
  }
  
  .personnel-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
