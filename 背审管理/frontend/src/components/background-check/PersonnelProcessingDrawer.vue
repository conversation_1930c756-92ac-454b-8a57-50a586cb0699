<template>
  <el-drawer v-model="drawerVisible" title="异常人员处理" :size="800" direction="rtl" :before-close="handleClose"
    class="processing-drawer">
    <div v-loading="loading" class="drawer-content">


      <!-- 内容区域 -->
      <div class="content-section">
        <div v-if="loading" class="loading-state">
          <el-skeleton :rows="8" animated />
        </div>

        <div v-else-if="personnelData" class="personnel-content">
          <!-- 基本信息区域 -->
          <div class="basic-info-section">
            <div class="info-content">
              <div class="info-grid">
                <div class="info-item">
                  <label>姓名：</label>
                  <span>{{ personnelData.name }}</span>
                </div>
                <div class="info-item">
                  <label>性别：</label>
                  <span>{{ personnelData.gender === 1 ? '男' : '女' }}</span>
                </div>
                <div class="info-item">
                  <label>手机号：</label>
                  <span>{{ personnelData.phone || '未填写' }}</span>
                </div>
                <div class="info-item">
                  <label>身份证：</label>
                  <span>{{ personnelData.idCard }}</span>
                </div>
                <div class="info-item">
                  <label>所属单位：</label>
                  <span>{{ personnelData.organization }}</span>
                </div>
                <div class="info-item">
                  <label>职位：</label>
                  <span>{{ personnelData.position }}</span>
                </div>
                <div class="info-item full-width">
                  <label>异常类型：</label>
                  <div class="abnormal-types">
                    <el-tag v-for="type in personnelData.abnormalTypes" :key="type" type="danger" size="small"
                      class="type-tag">
                      {{ getSingleAbnormalTypeText(type) }}
                    </el-tag>
                  </div>
                </div>
                <div class="info-item full-width">
                  <label>处理状态：</label>
                  <div class="status-info">
                    <el-tag :type="getStatusTagType(personnelData.processingStatus)" size="large" class="status-tag">
                      {{ getProcessingStatusText(personnelData.processingStatus) }}
                    </el-tag>
                    <span v-if="currentStatusReason" class="status-reason">{{ currentStatusReason }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="avatar-section">
              <el-avatar :size="80" :src="personnelData.avatar" :icon="User" class="personnel-avatar" />
            </div>
          </div>



          <!-- 处理动作 -->
          <div class="action-section">
            <h4 class="section-title">处理动作</h4>
            <div class="action-content">
              <!-- 处理方式选择 -->
              <div class="action-options">
                <div class="option-title">选择处理方式</div>
                <el-radio-group
                  v-model="actionForm.actionType"
                  class="action-radios"
                  @change="handleActionTypeChange"
                >
                  <el-radio
                    v-for="option in actionOptions"
                    :key="option.value"
                    :value="option.value"
                    class="action-radio"
                  >
                    <span class="radio-label">{{ option.label }}</span>
                    <!-- <span class="radio-desc">{{ option.desc }}</span> -->
                  </el-radio>
                </el-radio-group>
              </div>

              <!-- 处理原因 -->
              <div class="reason-section">
                <div class="reason-title">处理原因</div>
                <el-input v-model="actionForm.reason" type="textarea" :rows="4" placeholder="请详细说明处理原因..."
                  maxlength="500" show-word-limit class="reason-input" />
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <el-button type="primary" size="large" @click="handleSubmitAction" :loading="submitting"
                  class="submit-btn">
                  <el-icon>
                    <Check />
                  </el-icon>
                  提交处理
                </el-button>
                <el-button size="large" @click="resetActionForm" class="reset-btn">
                  <el-icon>
                    <Refresh />
                  </el-icon>
                  重置
                </el-button>
              </div>
            </div>
          </div>

          <!-- 处理记录 -->
          <div class="history-section">
            <h4 class="section-title">处理记录</h4>
            <div v-if="historyList.length === 0" class="empty-history">
              <el-empty description="暂无处理记录" :image-size="60" />
            </div>
            <div v-else class="history-list">
              <div v-for="record in historyList" :key="record.id" class="history-item">
                <div class="history-header">
                  <div class="status-change">
                    <span class="from-status">{{ getProcessingStatusText(record.fromStatus) }}</span>
                    <el-icon class="arrow-icon">
                      <ArrowRight />
                    </el-icon>
                    <span class="to-status">{{ getProcessingStatusText(record.toStatus) }}</span>
                  </div>
                  <div class="history-time">{{ formatTime(record.operateTime) }}</div>
                </div>
                <div class="history-content">
                  <p><strong>操作人：</strong>{{ record.operatorName || '系统' }}</p>
                  <p v-if="record.reason"><strong>处理原因：</strong>{{ record.reason }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="error-state">
          <el-empty description="加载失败，请重试" />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive } from 'vue'
import { User, ArrowRight, Check, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getPersonnelDetail, updateProcessingStatus, getProcessingHistory } from '@/api/background-check'
import {
  getSingleAbnormalTypeText,
  getProcessingStatusText
} from '@/data/personnelMockData'
// 移除了子组件导入，使用单页面设计

interface Props {
  modelValue: boolean
  personnelId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'processing-completed'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const personnelData = ref<any>(null)
const submitting = ref(false)
const historyList = ref<any[]>([])

// 当前状态的处理原因
const currentStatusReason = computed(() => {
  if (historyList.value.length > 0) {
    // 获取最新的处理记录
    const latestRecord = historyList.value[0]
    return latestRecord.reason || ''
  }
  return ''
})

// 处理动作表单
const actionForm = reactive({
  actionType: 1,
  reason: ''
})

// 处理方式选项
const actionOptions = [
  {
    value: 1,
    label: '无需处理',
    desc: '该人员无风险，正常工作',
    defaultReason: '经评估该人员无风险，可正常工作。'
  },
  {
    value: 2,
    label: '重点关注',
    desc: '加强监管，定期跟踪',
    defaultReason: '该人员存在潜在风险，需要加强监管和定期跟踪。'
  },
  {
    value: 3,
    label: '调岗/劝退',
    desc: '建议调整岗位或劝退',
    defaultReason: '该人员风险较高，建议调整岗位或劝退处理。'
  }
]

// 处理方式变更时的智能处理
const handleActionTypeChange = (value: number) => {
  const selectedOption = actionOptions.find(option => option.value === value)
  if (selectedOption) {
    actionForm.reason = selectedOption.defaultReason
  }
}

const drawerVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(() => props.modelValue, async (visible) => {
  if (visible && props.personnelId) {
    await fetchPersonnelDetail()
    await fetchProcessingHistory()
    // 获取数据后重置表单为当前状态
    resetActionForm()
  } else {
    personnelData.value = null
    historyList.value = []
    // 清空表单
    actionForm.actionType = 1
    actionForm.reason = ''
  }
})

const fetchPersonnelDetail = async () => {
  if (!props.personnelId) return

  try {
    loading.value = true
    const response = await getPersonnelDetail(props.personnelId)
    personnelData.value = response.data
  } catch (error) {
    console.error('获取人员详情失败:', error)
  } finally {
    loading.value = false
  }
}

const getBackgroundCheckResultType = (result: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',    // 未审查
    1: 'success', // 正常
    2: 'danger'   // 异常
  }
  return typeMap[result] || 'info'
}

const getProcessingStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',    // 无需处理
    1: 'warning', // 重点关注
    2: 'danger'   // 黑名单
  }
  return typeMap[status] || 'info'
}

const handleProcessingUpdated = () => {
  // 处理状态更新后，刷新人员信息和通知父组件
  fetchPersonnelDetail()
  emit('processing-completed')
}

// 获取处理历史
const fetchProcessingHistory = async () => {
  if (!props.personnelId) return

  try {
    const response = await getProcessingHistory(props.personnelId)
    historyList.value = response.data || []
  } catch (error) {
    console.error('获取处理历史失败:', error)
    historyList.value = []
  }
}

// 提交处理动作
const handleSubmitAction = async () => {
  if (!actionForm.reason.trim()) {
    ElMessage.warning('请填写处理原因')
    return
  }

  submitting.value = true
  try {
    await updateProcessingStatus({
      personnelId: props.personnelId!,
      fromStatus: personnelData.value.processingStatus,
      toStatus: actionForm.actionType,
      reason: actionForm.reason
    })

    ElMessage.success('处理成功')

    // 刷新数据
    await fetchPersonnelDetail()
    await fetchProcessingHistory()

    // 重置表单为新的当前状态
    resetActionForm()

    // 通知父组件数据已更新（但不关闭抽屉）
    emit('processing-completed')
  } catch (error) {
    console.error('处理失败:', error)
    ElMessage.error('处理失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 重置表单 - 重置为当前处理状态
const resetActionForm = () => {
  if (personnelData.value) {
    // 重置为当前的处理状态
    actionForm.actionType = personnelData.value.processingStatus || 0

    // 重置为实际的当前处理原因，而不是默认原因
    actionForm.reason = currentStatusReason.value || ''

    // 如果没有实际的处理原因，才使用默认原因
    if (!actionForm.reason) {
      const currentOption = actionOptions.find(option => option.value === actionForm.actionType)
      actionForm.reason = currentOption ? currentOption.defaultReason : ''
    }
  } else {
    // 如果没有人员数据，重置为默认值
    actionForm.actionType = 1
    actionForm.reason = ''
  }
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',
    1: 'warning',
    2: 'danger'
  }
  return typeMap[status] || 'info'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleClose = () => {
  drawerVisible.value = false
}
</script>

<style scoped>
.processing-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.personnel-info-section {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-tag {
  font-weight: 500;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.abnormal-types {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.abnormal-tag {
  font-size: 12px;
}

.content-section {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.loading-state {
  padding: 40px 20px;
}

.personnel-content {
  max-width: 100%;
}

/* 基本信息区域 */
.basic-info-section {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-content {
  flex: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px 24px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
}

.abnormal-types {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.type-tag {
  margin: 0;
}

.avatar-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.personnel-avatar {
  border: 3px solid #f0f0f0;
}

/* 各个区域的通用样式 */
.status-section,
.action-section,
.history-section {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

/* 当前状态 */
.current-status {
  display: flex;
  align-items: center;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.status-tag {
  font-size: 14px;
  padding: 8px 16px;
}

.status-reason {
  font-size: 13px;
  color: #606266;
  background: #f5f7fa;
  padding: 6px 12px;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

/* 处理动作区域 */
.action-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.action-radios {
  display: flex;
  /* flex-direction: column; */
  gap: 8px;
}

.action-radio {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #fafafa;
  margin: 0;
}

.action-radio:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.action-radio.is-checked {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.radio-label {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  margin-right: 12px;
  min-width: 80px;
}

.radio-desc {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.reason-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reason-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.reason-input {
  border-radius: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.submit-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
}

.reset-btn {
  padding: 12px 24px;
  border-radius: 8px;
}

/* 处理记录 */
.empty-history {
  text-align: center;
  padding: 40px 0;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.history-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-change {
  display: flex;
  align-items: center;
  gap: 8px;
}

.from-status {
  color: #e6a23c;
  font-weight: 500;
}

.arrow-icon {
  color: #909399;
}

.to-status {
  color: #67c23a;
  font-weight: 500;
}

.history-time {
  color: #909399;
  font-size: 12px;
}

.history-content p {
  margin: 4px 0;
  color: #606266;
  line-height: 1.5;
}

.history-content strong {
  color: #303133;
}

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
