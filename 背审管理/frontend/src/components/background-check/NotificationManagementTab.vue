<template>
  <div class="notification-management">
    <el-tabs v-model="activeSubTab" class="notification-tabs">
      <!-- 发送通知页签 -->
      <el-tab-pane label="发送通知" name="send">
        <div class="send-notification">
          <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="notification-form">
            <div class="form-section">
              <h4 class="section-title">通知对象</h4>
              <el-form-item label="接收单位">
                <el-input :value="personnelData?.organization" disabled />
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">通知内容</h4>
              
              <el-form-item label="通知类型" prop="notificationType">
                <el-select v-model="formData.notificationType" placeholder="请选择通知类型" @change="handleTypeChange">
                  <el-option label="建议辞退" :value="1" />
                  <el-option label="建议调岗" :value="2" />
                  <el-option label="建议加强监管" :value="3" />
                  <el-option label="其他" :value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="通知内容" prop="notificationContent">
                <el-input
                  v-model="formData.notificationContent"
                  type="textarea"
                  :rows="8"
                  placeholder="请输入通知内容"
                  maxlength="1000"
                  show-word-limit
                />
              </el-form-item>
            </div>

            <div class="form-actions">
              <el-button @click="handleReset">重置</el-button>
              <el-button type="primary" :loading="sendLoading" @click="handleSendNotification">
                <el-icon><Message /></el-icon>
                发送通知
              </el-button>
            </div>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 通知记录页签 -->
      <el-tab-pane label="通知记录" name="history">
        <div class="notification-history">
          <div class="history-header">
            <h4 class="section-title">
              <el-icon><List /></el-icon>
              通知发送记录
            </h4>
            <el-button @click="refreshNotificationHistory" :loading="historyLoading" size="small">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>

          <div v-loading="historyLoading" class="history-content">
            <div v-if="notificationHistory.length === 0" class="empty-state">
              <el-empty description="暂无通知记录" />
            </div>
            
            <div v-else class="notification-list">
              <el-card 
                v-for="notification in notificationHistory" 
                :key="notification.id" 
                class="notification-card"
                shadow="hover"
              >
                <div class="notification-header">
                  <div class="notification-info">
                    <el-tag :type="getNotificationTypeColor(notification.notificationType)" size="small">
                      {{ getNotificationTypeText(notification.notificationType) }}
                    </el-tag>
                    <span class="organization-name">{{ notification.organizationName }}</span>
                  </div>
                  <div class="notification-status">
                    <el-tag :type="getSendStatusColor(notification.sendStatus)" size="small">
                      {{ getSendStatusText(notification.sendStatus) }}
                    </el-tag>
                    <el-tag v-if="notification.sendStatus === 2" :type="getReadStatusColor(notification.readStatus)" size="small">
                      {{ getReadStatusText(notification.readStatus) }}
                    </el-tag>
                  </div>
                </div>
                
                <div class="notification-content">
                  <p class="content-text">{{ notification.notificationContent }}</p>
                </div>
                
                <div class="notification-footer">
                  <div class="time-info">
                    <span class="send-time">发送时间：{{ formatDateTime(notification.sendTime) }}</span>
                    <span v-if="notification.readTime" class="read-time">
                      阅读时间：{{ formatDateTime(notification.readTime) }}
                    </span>
                  </div>
                  
                  <div v-if="notification.response" class="response-section">
                    <label class="response-label">回复内容：</label>
                    <p class="response-text">{{ notification.response }}</p>
                    <span class="response-time">回复时间：{{ formatDateTime(notification.responseTime) }}</span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { Message, List, Refresh } from '@element-plus/icons-vue'
import { sendNotification, getNotificationHistory } from '@/api/background-check'
import { getNotificationTypeText, type NotificationRecord } from '@/data/personnelMockData'

interface Props {
  personnelData: any
}

interface Emits {
  (e: 'notification-sent'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const activeSubTab = ref('send')
const sendLoading = ref(false)
const historyLoading = ref(false)
const notificationHistory = ref<NotificationRecord[]>([])

const formData = reactive({
  notificationType: 1,
  notificationContent: ''
})

const rules = {
  notificationType: [
    { required: true, message: '请选择通知类型', trigger: 'change' }
  ],
  notificationContent: [
    { required: true, message: '请输入通知内容', trigger: 'blur' },
    { min: 20, message: '通知内容至少需要20个字符', trigger: 'blur' }
  ]
}

// 通知模板生成函数
const getNotificationTemplate = (type: number, personnelData: any) => {
  const templates = {
    1: () => `关于${personnelData?.name || '该人员'}的处理建议

经背景审查发现，${personnelData?.name || '该人员'}存在异常情况，建议贵单位考虑对其进行辞退处理。

具体情况：
- 姓名：${personnelData?.name || ''}
- 身份证号：${personnelData?.idCard || ''}
- 职位：${personnelData?.position || ''}
- 异常类型：${personnelData?.abnormalTypes?.join('、') || ''}

请贵单位根据实际情况妥善处理。`,

    2: () => `关于${personnelData?.name || '该人员'}的岗位调整建议

经背景审查发现，${personnelData?.name || '该人员'}存在一定风险，建议贵单位考虑对其进行岗位调整。

建议调整到风险较低的岗位，并加强日常监管。`,

    3: () => `关于${personnelData?.name || '该人员'}的监管建议

经背景审查发现，${personnelData?.name || '该人员'}需要加强监管。

建议措施：
1. 定期进行工作表现评估
2. 加强日常行为监督
3. 必要时进行再次背景审查`,

    4: () => '请根据实际情况填写通知内容...'
  }

  const templateFn = templates[type as keyof typeof templates]
  return templateFn ? templateFn() : ''
}

watch(() => props.personnelData, () => {
  if (activeSubTab.value === 'history') {
    fetchNotificationHistory()
  }
}, { immediate: true })

const handleTypeChange = (type: number) => {
  formData.notificationContent = getNotificationTemplate(type, props.personnelData)
}

const handleReset = () => {
  formData.notificationType = 1
  formData.notificationContent = ''
  formRef.value?.clearValidate()
}

const handleSendNotification = async () => {
  if (!formRef.value || !props.personnelData) return

  try {
    await formRef.value.validate()
    sendLoading.value = true

    await sendNotification({
      personnelId: props.personnelData.id,
      organizationName: props.personnelData.organization,
      notificationType: formData.notificationType,
      notificationContent: formData.notificationContent
    })

    ElMessage.success('通知发送成功')
    emit('notification-sent')
    handleReset()
    
    // 刷新通知记录
    if (activeSubTab.value === 'history') {
      fetchNotificationHistory()
    }
    
  } catch (error) {
    console.error('发送通知失败:', error)
    ElMessage.error('发送通知失败')
  } finally {
    sendLoading.value = false
  }
}

const fetchNotificationHistory = async () => {
  if (!props.personnelData?.id) return
  
  try {
    historyLoading.value = true
    const response = await getNotificationHistory(props.personnelData.id)
    notificationHistory.value = response.data || []
  } catch (error) {
    console.error('获取通知记录失败:', error)
    notificationHistory.value = []
  } finally {
    historyLoading.value = false
  }
}

const refreshNotificationHistory = () => {
  fetchNotificationHistory()
}

// 状态相关方法
const getNotificationTypeColor = (type: number) => {
  const colorMap: Record<number, string> = {
    1: 'danger',
    2: 'warning', 
    3: 'info',
    4: ''
  }
  return colorMap[type] || ''
}

const getSendStatusColor = (status: number) => {
  const colorMap: Record<number, string> = {
    1: 'info',
    2: 'success',
    3: 'danger'
  }
  return colorMap[status] || ''
}

const getSendStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    1: '待发送',
    2: '已发送',
    3: '发送失败'
  }
  return textMap[status] || '未知'
}

const getReadStatusColor = (status: number) => {
  return status === 2 ? 'success' : 'warning'
}

const getReadStatusText = (status: number) => {
  return status === 2 ? '已读' : '未读'
}

const formatDateTime = (timeStr?: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

// 监听人员数据变化，更新模板
watch(() => props.personnelData, (newData) => {
  if (newData && formData.notificationType) {
    handleTypeChange(formData.notificationType)
  }
}, { immediate: true })

onMounted(() => {
  // 设置默认模板
  if (props.personnelData) {
    handleTypeChange(1)
  }
})
</script>

<style scoped>
.notification-management {
  height: 100%;
}

.notification-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.send-notification,
.notification-history {
  height: 100%;
  overflow-y: auto;
}

.notification-form {
  max-width: 600px;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.history-content {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notification-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.notification-card:hover {
  transform: translateY(-2px);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.notification-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.organization-name {
  font-weight: 500;
  color: #303133;
}

.notification-status {
  display: flex;
  gap: 8px;
}

.notification-content {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.content-text {
  margin: 0;
  line-height: 1.6;
  color: #303133;
  white-space: pre-wrap;
}

.notification-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.time-info {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.send-time,
.read-time {
  font-size: 12px;
  color: #909399;
}

.response-section {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.response-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.response-text {
  margin: 4px 0;
  color: #303133;
  line-height: 1.5;
}

.response-time {
  font-size: 11px;
  color: #909399;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

:deep(.el-tab-pane) {
  height: 100%;
}
</style>
