<template>
  <div class="industry-statistics-preview">
    <div class="report-content">
      <div class="report-title">行业统计报表</div>
      <div class="report-meta">
        <p>统计时间：{{ formatDateRange(params.dateRange) }}</p>
        <p>生成时间：{{ new Date().toLocaleString() }}</p>
        <p>统计维度：{{ getDimensionText(params.dimension) }}</p>
        <p>涉及行业：{{ reportData.length }} 个</p>
        <p v-if="params.region && params.region.length > 0">筛选区域：{{ params.region.join('、') }}</p>
      </div>
      
      <el-table :data="reportData" border stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="行业名称" min-width="150" />
        <el-table-column prop="totalPersonnel" label="总人员数" width="120" align="center" />
        <el-table-column prop="abnormalPersonnel" label="异常人员数" width="120" align="center" />
        <el-table-column label="异常率" width="120" align="center">
          <template #default="{ row }">
            <span :class="getAbnormalRateClass(parseFloat(row.abnormalRate))">
              {{ row.abnormalRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="pendingPersonnel" label="待处理人员" width="120" align="center" />
        <el-table-column label="风险等级" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getRiskLevelType(parseFloat(row.abnormalRate))">
              {{ getRiskLevelText(parseFloat(row.abnormalRate)) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'

// Props
interface Props {
  reportData: any[]
  params: any
}

const props = defineProps<Props>()

// 格式化日期范围
const formatDateRange = (dateRange: string[]) => {
  if (!dateRange || dateRange.length !== 2) return ''
  return `${dateRange[0]} 至 ${dateRange[1]}`
}

// 获取统计维度文本
const getDimensionText = (dimension: string) => {
  const dimensionMap: Record<string, string> = {
    region: '按区域统计',
    industry: '按行业统计',
    time: '按时间统计'
  }
  return dimensionMap[dimension] || '未知维度'
}

// 获取异常率样式类
const getAbnormalRateClass = (rate: number) => {
  if (rate >= 10) return 'high-risk'
  if (rate >= 5) return 'medium-risk'
  return 'low-risk'
}

// 获取风险等级类型
const getRiskLevelType = (rate: number) => {
  if (rate >= 10) return 'danger'
  if (rate >= 5) return 'warning'
  return 'success'
}

// 获取风险等级文本
const getRiskLevelText = (rate: number) => {
  if (rate >= 10) return '高风险'
  if (rate >= 5) return '中风险'
  return '低风险'
}

// 打印报表
const handlePrint = () => {
  const printContent = document.querySelector('.industry-statistics-preview .report-content')
  if (!printContent) {
    ElMessage.warning('无法找到打印内容')
    return
  }

  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    ElMessage.error('无法打开打印窗口')
    return
  }

  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>行业统计报表</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .report-title { font-size: 20px; font-weight: bold; text-align: center; margin-bottom: 20px; }
        .report-meta { background: #f5f7fa; padding: 16px; border-radius: 6px; margin-bottom: 20px; }
        .report-meta p { margin: 0 0 8px 0; font-size: 14px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f7fa; font-weight: bold; }
        .high-risk { color: #f56c6c; font-weight: bold; }
        .medium-risk { color: #e6a23c; font-weight: bold; }
        .low-risk { color: #67c23a; font-weight: bold; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      ${printContent.innerHTML}
    </body>
    </html>
  `)

  printWindow.document.close()
  printWindow.focus()
  printWindow.print()
  printWindow.close()
}

// 导出报表
const handleExport = async () => {
  if (!props.reportData || props.reportData.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  const fileName = '行业统计报表'
  const headers = ['序号', '行业名称', '总人员数', '异常人员数', '异常率', '待处理人员', '风险等级']
  const rows = props.reportData.map((item: any, index: number) => [
    String(index + 1),
    item.name || '',
    String(item.totalPersonnel || 0),
    String(item.abnormalPersonnel || 0),
    `${item.abnormalRate}%`,
    String(item.pendingPersonnel || 0),
    getRiskLevelText(parseFloat(item.abnormalRate))
  ])

  // 生成CSV内容
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n')

  // 添加BOM以支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })

  // 创建下载链接
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `${fileName}_${new Date().toISOString().split('T')[0]}.csv`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success('报表导出成功')
}

// 暴露方法给父组件
defineExpose({
  handlePrint,
  handleExport
})
</script>

<style scoped>
.industry-statistics-preview {
  height: 100%;
  overflow-y: auto;
}

.report-content {
  padding: 20px;
}

.report-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  text-align: center;
  margin-bottom: 20px;
}

.report-meta {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.report-meta p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.report-meta p:last-child {
  margin-bottom: 0;
}

.high-risk {
  color: #f56c6c;
  font-weight: 600;
}

.medium-risk {
  color: #e6a23c;
  font-weight: 600;
}

.low-risk {
  color: #67c23a;
  font-weight: 600;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}
</style>
