<template>
  <div class="abnormal-type-statistics-preview">
    <div class="report-content">
      <div class="report-title">异常类型统计报表</div>
      <div class="report-meta">
        <p>统计时间：{{ formatDateRange(params.dateRange) }}</p>
        <p>生成时间：{{ new Date().toLocaleString() }}</p>
        <p>统计维度：{{ getDimensionText(params.dimension) }}</p>
        <p>异常类型：{{ reportData.length }} 种</p>
        <p>总异常人员：{{ getTotalCount() }} 人</p>
        <p v-if="params.region && params.region.length > 0">筛选区域：{{ params.region.join('、') }}</p>
      </div>
      
      <el-table :data="reportData" border stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="typeName" label="异常类型" min-width="150" />
        <el-table-column prop="count" label="人员数量" width="120" align="center" />
        <el-table-column label="占比" width="120" align="center">
          <template #default="{ row }">
            <div class="percentage-cell">
              <span>{{ row.percentage }}%</span>
              <div class="percentage-bar">
                <div 
                  class="percentage-fill" 
                  :style="{ width: `${Math.min(row.percentage, 100)}%` }"
                ></div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="pendingCount" label="待处理数量" width="120" align="center" />
        <el-table-column prop="processedCount" label="已处理数量" width="120" align="center" />
        <el-table-column label="处理进度" width="120" align="center">
          <template #default="{ row }">
            <el-progress 
              :percentage="getProcessingProgress(row)" 
              :color="getProgressColor(row)"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        <el-table-column label="风险等级" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getRiskLevelType(row.count)">
              {{ getRiskLevelText(row.count) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'

// Props
interface Props {
  reportData: any[]
  params: any
}

const props = defineProps<Props>()

// 格式化日期范围
const formatDateRange = (dateRange: string[]) => {
  if (!dateRange || dateRange.length !== 2) return ''
  return `${dateRange[0]} 至 ${dateRange[1]}`
}

// 获取统计维度文本
const getDimensionText = (dimension: string) => {
  const dimensionMap: Record<string, string> = {
    region: '按区域统计',
    industry: '按行业统计',
    time: '按时间统计'
  }
  return dimensionMap[dimension] || '未知维度'
}

// 获取总数量
const getTotalCount = () => {
  return props.reportData.reduce((total, item) => total + (item.count || 0), 0)
}

// 获取处理进度
const getProcessingProgress = (row: any) => {
  if (row.count === 0) return 0
  return Math.round((row.processedCount / row.count) * 100)
}

// 获取进度条颜色
const getProgressColor = (row: any) => {
  const progress = getProcessingProgress(row)
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  return '#f56c6c'
}

// 获取风险等级类型
const getRiskLevelType = (count: number) => {
  if (count >= 30) return 'danger'
  if (count >= 15) return 'warning'
  return 'success'
}

// 获取风险等级文本
const getRiskLevelText = (count: number) => {
  if (count >= 30) return '高风险'
  if (count >= 15) return '中风险'
  return '低风险'
}

// 打印报表
const handlePrint = () => {
  const printContent = document.querySelector('.abnormal-type-statistics-preview .report-content')
  if (!printContent) {
    ElMessage.warning('无法找到打印内容')
    return
  }

  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    ElMessage.error('无法打开打印窗口')
    return
  }

  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>异常类型统计报表</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .report-title { font-size: 20px; font-weight: bold; text-align: center; margin-bottom: 20px; }
        .report-meta { background: #f5f7fa; padding: 16px; border-radius: 6px; margin-bottom: 20px; }
        .report-meta p { margin: 0 0 8px 0; font-size: 14px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f7fa; font-weight: bold; }
        .percentage-bar { width: 60px; height: 6px; background-color: #e4e7ed; border-radius: 3px; }
        .percentage-fill { height: 100%; background: linear-gradient(90deg, #409eff, #67c23a); border-radius: 3px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      ${printContent.innerHTML}
    </body>
    </html>
  `)

  printWindow.document.close()
  printWindow.focus()
  printWindow.print()
  printWindow.close()
}

// 导出报表
const handleExport = async () => {
  if (!props.reportData || props.reportData.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  const fileName = '异常类型统计报表'
  const headers = ['序号', '异常类型', '人员数量', '占比', '待处理数量', '已处理数量', '处理进度', '风险等级']
  const rows = props.reportData.map((item: any, index: number) => [
    String(index + 1),
    item.typeName || '',
    String(item.count || 0),
    `${item.percentage}%`,
    String(item.pendingCount || 0),
    String(item.processedCount || 0),
    `${getProcessingProgress(item)}%`,
    getRiskLevelText(item.count)
  ])

  // 生成CSV内容
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n')

  // 添加BOM以支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })

  // 创建下载链接
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `${fileName}_${new Date().toISOString().split('T')[0]}.csv`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success('报表导出成功')
}

// 暴露方法给父组件
defineExpose({
  handlePrint,
  handleExport
})
</script>

<style scoped>
.abnormal-type-statistics-preview {
  height: 100%;
  overflow-y: auto;
}

.report-content {
  padding: 20px;
}

.report-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  text-align: center;
  margin-bottom: 20px;
}

.report-meta {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.report-meta p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.report-meta p:last-child {
  margin-bottom: 0;
}

.percentage-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.percentage-bar {
  width: 60px;
  height: 6px;
  background-color: #e4e7ed;
  border-radius: 3px;
  overflow: hidden;
}

.percentage-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #67c23a);
  transition: width 0.3s ease;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-progress-bar__outer) {
  background-color: #e4e7ed;
}
</style>
