<template>
  <div class="abnormal-personnel-preview">
    <div class="report-content">
      <div class="report-title">异常人员名单统计报表</div>
      <div class="report-meta">
        <p>统计时间：{{ formatDateRange(params.dateRange) }}</p>
        <p>生成时间：{{ new Date().toLocaleString() }}</p>
        <p>异常人员总数：{{ reportData.length }} 人</p>
        <p v-if="params.region && params.region.length > 0">筛选区域：{{ params.region.join('、') }}</p>
        <p v-if="params.abnormalTypes && params.abnormalTypes.length > 0">筛选异常类型：{{ getAbnormalTypesText(params.abnormalTypes) }}</p>
      </div>
      
      <el-table :data="reportData" border stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="idCard" label="身份证号" width="180" />
        <el-table-column prop="organization" label="所属单位" min-width="150" />
        <el-table-column prop="region" label="区域" width="120" />
        <el-table-column label="异常类型" width="200">
          <template #default="{ row }">
            <el-tag 
              v-for="type in row.abnormalTypes" 
              :key="type" 
              type="danger" 
              size="small"
              class="type-tag"
            >
              {{ getSingleAbnormalTypeText(type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="处理状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getProcessingStatusType(row.processingStatus)">
              {{ getProcessingStatusText(row.processingStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="entryDate" label="入职日期" width="120" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getSingleAbnormalTypeText,
  getProcessingStatusText,
  backgroundCheckAbnormalTypes
} from '@/data/personnelMockData'

// Props
interface Props {
  reportData: any[]
  params: any
}

const props = defineProps<Props>()

// 格式化日期范围
const formatDateRange = (dateRange: string[]) => {
  if (!dateRange || dateRange.length !== 2) return ''
  return `${dateRange[0]} 至 ${dateRange[1]}`
}

// 获取异常类型文本
const getAbnormalTypesText = (types: string[]) => {
  return types.map(type => 
    backgroundCheckAbnormalTypes.find(t => t.value === type)?.label || type
  ).join('、')
}

// 获取处理状态类型
const getProcessingStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: '',
    1: 'info',
    2: 'warning',
    3: 'danger'
  }
  return typeMap[status] || ''
}

// 打印报表
const handlePrint = () => {
  const printContent = document.querySelector('.abnormal-personnel-preview .report-content')
  if (!printContent) {
    ElMessage.warning('无法找到打印内容')
    return
  }

  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    ElMessage.error('无法打开打印窗口')
    return
  }

  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>异常人员名单统计报表</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .report-title { font-size: 20px; font-weight: bold; text-align: center; margin-bottom: 20px; }
        .report-meta { background: #f5f7fa; padding: 16px; border-radius: 6px; margin-bottom: 20px; }
        .report-meta p { margin: 0 0 8px 0; font-size: 14px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f7fa; font-weight: bold; }
        .type-tag { background: #f56c6c; color: white; padding: 2px 6px; border-radius: 3px; margin-right: 4px; font-size: 12px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      ${printContent.innerHTML}
    </body>
    </html>
  `)

  printWindow.document.close()
  printWindow.focus()
  printWindow.print()
  printWindow.close()
}

// 导出报表
const handleExport = async () => {
  if (!props.reportData || props.reportData.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  const fileName = '异常人员名单报表'
  const headers = ['序号', '姓名', '身份证号', '所属单位', '区域', '异常类型', '处理状态', '入职日期']
  const rows = props.reportData.map((item: any, index: number) => [
    String(index + 1),
    item.name || '',
    item.idCard || '',
    item.organization || '',
    item.region || '',
    item.abnormalTypes ? item.abnormalTypes.map((type: string) =>
      getSingleAbnormalTypeText(type)).join('、') : '',
    getProcessingStatusText(item.processingStatus),
    item.entryDate || ''
  ])

  // 生成CSV内容
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n')

  // 添加BOM以支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })

  // 创建下载链接
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `${fileName}_${new Date().toISOString().split('T')[0]}.csv`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success('报表导出成功')
}

// 暴露方法给父组件
defineExpose({
  handlePrint,
  handleExport
})
</script>

<style scoped>
.abnormal-personnel-preview {
  height: 100%;
  overflow-y: auto;
}

.report-content {
  padding: 20px;
}

.report-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  text-align: center;
  margin-bottom: 20px;
}

.report-meta {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.report-meta p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.report-meta p:last-child {
  margin-bottom: 0;
}

.type-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}
</style>
