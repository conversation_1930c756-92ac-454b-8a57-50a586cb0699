<template>
  <div class="unit-statistics-params">
    <el-form :model="params" label-width="120px" class="params-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="时间范围" required>
            <el-date-picker
              v-model="params.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="区域选择">
            <el-select 
              v-model="params.region" 
              placeholder="请选择区域（可多选）" 
              multiple
              clearable
              style="width: 100%"
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="item in regionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单位类型">
            <el-select 
              v-model="params.industry" 
              placeholder="请选择单位类型（可多选）" 
              multiple
              clearable
              style="width: 100%"
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="item in industryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统计维度">
            <el-radio-group v-model="params.dimension">
              <el-radio value="region">按区域</el-radio>
              <el-radio value="industry">按行业</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-button type="primary" @click="handleGenerate" :loading="generating">
              <el-icon><Document /></el-icon>
              生成报表
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置参数
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Document, Refresh } from '@element-plus/icons-vue'
import { 
  industryTypes,
  baodingRegions
} from '@/data/personnelMockData'

// Props
interface Props {
  generating?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  generating: false
})

// Emits
const emit = defineEmits<{
  generate: [params: any]
  reset: []
}>()

// 区域选项
const regionOptions = baodingRegions.map(region => ({
  label: region,
  value: region
}))

// 行业选项
const industryOptions = industryTypes

// 参数数据
const params = reactive({
  dateRange: [
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    new Date().toISOString().split('T')[0]
  ],
  region: [] as string[],
  industry: [] as string[],
  dimension: 'region'
})

// 生成报表
const handleGenerate = () => {
  emit('generate', { ...params })
}

// 重置参数
const handleReset = () => {
  Object.assign(params, {
    dateRange: [
      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      new Date().toISOString().split('T')[0]
    ],
    region: [],
    industry: [],
    dimension: 'region'
  })
  emit('reset')
}
</script>

<style scoped>
.unit-statistics-params {
  padding: 0;
}

.params-form {
  margin: 0;
}
</style>
