// 轻量级权限服务

import type { UserInfo, UserRole, DataScope, ApiResponse } from '@/types/auth'
import { storage, STORAGE_KEYS } from '@/utils/storage'

/**
 * 轻量级权限服务类
 */
class AuthService {
  private currentUser: UserInfo | null = null
  private readonly CACHE_EXPIRY = 24 * 60 * 60 * 1000 // 24小时缓存
  
  // Mock用户数据
  private mockUsers: Record<UserRole, UserInfo> = {
    admin: {
      userId: 'admin-001',
      username: 'admin',
      name: '系统管理员',
      phone: '13800138000',
      email: '<EMAIL>',
      organizationId: 'org-admin',
      organizationName: '保定市公安局',
      roles: ['admin'],
      permissions: [
        'personnel:view:all',
        'task:manage',
        'task:assign',
        'task:review',
        'dashboard:admin'
      ]
    },
    unit: {
      userId: 'unit-001',
      username: 'unit001',
      name: '莲池区分局',
      phone: '13800138001',
      email: '<EMAIL>',
      organizationId: 'org-unit-001',
      organizationName: '莲池区分局',
      roles: ['unit'],
      permissions: [
        'personnel:view:org',
        'task:process',
        'task:submit',
        'dashboard:unit'
      ]
    }
  }

  /**
   * 获取当前角色（从localStorage）
   */
  private getCurrentRole(): UserRole {
    return storage.get<UserRole>(STORAGE_KEYS.CURRENT_ROLE) || 'admin'
  }

  /**
   * 设置当前角色（保存到localStorage）
   */
  private setCurrentRole(role: UserRole): void {
    storage.set(STORAGE_KEYS.CURRENT_ROLE, role, this.CACHE_EXPIRY)
  }

  /**
   * 获取Mock用户数据
   */
  private getMockUser(role?: UserRole): UserInfo {
    const currentRole = role || this.getCurrentRole()
    return this.mockUsers[currentRole]
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<UserInfo> {
    // 先尝试从缓存获取
    const cachedUser = storage.get<UserInfo>(STORAGE_KEYS.USER_INFO)
    if (cachedUser && !storage.isExpired(STORAGE_KEYS.USER_INFO)) {
      this.currentUser = cachedUser
      return cachedUser
    }

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))

    // 获取Mock用户数据
    const currentRole = this.getCurrentRole()
    this.currentUser = this.getMockUser(currentRole)

    // 缓存用户信息
    storage.set(STORAGE_KEYS.USER_INFO, this.currentUser, this.CACHE_EXPIRY)

    return this.currentUser
  }

  /**
   * 检查是否为管理员
   */
  isAdmin(): boolean {
    return this.getCurrentRole() === 'admin'
  }

  /**
   * 检查是否有指定权限
   */
  hasPermission(permission: string): boolean {
    const user = this.currentUser || this.getMockUser()
    return user.permissions.includes(permission)
  }

  /**
   * 获取数据访问范围
   */
  getDataScope(): DataScope {
    return this.isAdmin() ? 'all' : 'organization'
  }

  /**
   * 获取当前角色（公共方法）
   */
  getRole(): UserRole {
    return this.getCurrentRole()
  }

  /**
   * 切换用户角色（Mock功能）
   */
  switchRole(role: UserRole): void {
    // 保存新角色到localStorage
    this.setCurrentRole(role)

    // 清除用户信息缓存
    storage.remove(STORAGE_KEYS.USER_INFO)
    this.currentUser = null

    // 获取新角色的用户信息
    const newUser = this.getMockUser(role)
    this.currentUser = newUser

    // 缓存新用户信息
    storage.set(STORAGE_KEYS.USER_INFO, newUser, this.CACHE_EXPIRY)

    // 触发全局事件，通知角色变更
    window.dispatchEvent(new CustomEvent('auth-role-changed', {
      detail: { role, user: newUser }
    }))

    console.log(`🔄 角色已切换为: ${role === 'admin' ? '管理员' : '下级单位'}`)
    console.log('💾 角色信息已保存到localStorage')
  }

  /**
   * 获取用户权限列表
   */
  async getUserPermissions(): Promise<string[]> {
    const user = await this.getCurrentUser()
    return user.permissions
  }

  /**
   * 获取可访问的组织ID列表
   */
  async getAccessibleOrganizationIds(): Promise<string[]> {
    const user = await this.getCurrentUser()
    
    if (this.isAdmin()) {
      // 管理员可以访问所有组织
      return ['all'] // 特殊标识
    } else {
      // 下级单位只能访问自己的组织
      return [user.organizationId]
    }
  }

  /**
   * 模拟API调用 - 获取当前用户
   */
  async apiGetCurrentUser(): Promise<ApiResponse<UserInfo>> {
    await new Promise(resolve => setTimeout(resolve, 200))

    const currentRole = this.getCurrentRole()
    return {
      code: 200,
      data: this.getMockUser(currentRole),
      message: 'success'
    }
  }

  /**
   * 模拟API调用 - 检查权限
   */
  async apiCheckPermission(permission: string): Promise<ApiResponse<boolean>> {
    await new Promise(resolve => setTimeout(resolve, 50))
    
    const hasPermission = this.hasPermission(permission)
    
    return {
      code: 200,
      data: hasPermission,
      message: hasPermission ? 'success' : 'permission denied'
    }
  }

  /**
   * 重置服务状态
   */
  reset(): void {
    this.currentUser = null
    // 清除localStorage中的所有权限相关数据
    storage.clear()
    // 重置为默认角色
    this.setCurrentRole('admin')
    console.log('🔄 权限状态已重置，localStorage已清除')
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    storage.remove(STORAGE_KEYS.USER_INFO)
    this.currentUser = null
    console.log('🗑️ 用户信息缓存已清除')
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(): { hasCache: boolean; timestamp: number | null; isExpired: boolean } {
    return {
      hasCache: storage.has(STORAGE_KEYS.USER_INFO),
      timestamp: storage.getTimestamp(STORAGE_KEYS.USER_INFO),
      isExpired: storage.isExpired(STORAGE_KEYS.USER_INFO)
    }
  }
}

// 创建单例实例
export const authService = new AuthService()
export default authService
