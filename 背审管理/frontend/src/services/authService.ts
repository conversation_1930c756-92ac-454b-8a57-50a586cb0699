// 轻量级权限服务

import type { UserInfo, UserRole, DataScope, ApiResponse } from '@/types/auth'

/**
 * 轻量级权限服务类
 */
class AuthService {
  private currentUser: UserInfo | null = null
  private mockRole: UserRole = 'admin' // 默认管理员角色
  
  // Mock用户数据
  private mockUsers: Record<UserRole, UserInfo> = {
    admin: {
      userId: 'admin-001',
      username: 'admin',
      name: '系统管理员',
      phone: '13800138000',
      email: '<EMAIL>',
      organizationId: 'org-admin',
      organizationName: '保定市公安局',
      roles: ['admin'],
      permissions: [
        'personnel:view:all',
        'task:manage',
        'task:assign',
        'task:review',
        'dashboard:admin'
      ]
    },
    unit: {
      userId: 'unit-001',
      username: 'unit001',
      name: '莲池区分局',
      phone: '13800138001',
      email: '<EMAIL>',
      organizationId: 'org-unit-001',
      organizationName: '莲池区分局',
      roles: ['unit'],
      permissions: [
        'personnel:view:org',
        'task:process',
        'task:submit',
        'dashboard:unit'
      ]
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<UserInfo> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 返回Mock用户数据
    this.currentUser = this.mockUsers[this.mockRole]
    return this.currentUser
  }

  /**
   * 检查是否为管理员
   */
  isAdmin(): boolean {
    return this.mockRole === 'admin'
  }

  /**
   * 检查是否有指定权限
   */
  hasPermission(permission: string): boolean {
    const user = this.currentUser || this.mockUsers[this.mockRole]
    return user.permissions.includes(permission)
  }

  /**
   * 获取数据访问范围
   */
  getDataScope(): DataScope {
    return this.isAdmin() ? 'all' : 'organization'
  }

  /**
   * 获取当前角色
   */
  getCurrentRole(): UserRole {
    return this.mockRole
  }

  /**
   * 切换用户角色（Mock功能）
   */
  switchRole(role: UserRole): void {
    this.mockRole = role
    this.currentUser = null // 清除缓存，强制重新获取
    
    // 触发全局事件，通知角色变更
    window.dispatchEvent(new CustomEvent('auth-role-changed', {
      detail: { role, user: this.mockUsers[role] }
    }))
    
    console.log(`🔄 角色已切换为: ${role === 'admin' ? '管理员' : '下级单位'}`)
  }

  /**
   * 获取用户权限列表
   */
  async getUserPermissions(): Promise<string[]> {
    const user = await this.getCurrentUser()
    return user.permissions
  }

  /**
   * 获取可访问的组织ID列表
   */
  async getAccessibleOrganizationIds(): Promise<string[]> {
    const user = await this.getCurrentUser()
    
    if (this.isAdmin()) {
      // 管理员可以访问所有组织
      return ['all'] // 特殊标识
    } else {
      // 下级单位只能访问自己的组织
      return [user.organizationId]
    }
  }

  /**
   * 模拟API调用 - 获取当前用户
   */
  async apiGetCurrentUser(): Promise<ApiResponse<UserInfo>> {
    await new Promise(resolve => setTimeout(resolve, 200))
    
    return {
      code: 200,
      data: this.mockUsers[this.mockRole],
      message: 'success'
    }
  }

  /**
   * 模拟API调用 - 检查权限
   */
  async apiCheckPermission(permission: string): Promise<ApiResponse<boolean>> {
    await new Promise(resolve => setTimeout(resolve, 50))
    
    const hasPermission = this.hasPermission(permission)
    
    return {
      code: 200,
      data: hasPermission,
      message: hasPermission ? 'success' : 'permission denied'
    }
  }

  /**
   * 重置服务状态
   */
  reset(): void {
    this.currentUser = null
    this.mockRole = 'admin'
  }
}

// 创建单例实例
export const authService = new AuthService()
export default authService
