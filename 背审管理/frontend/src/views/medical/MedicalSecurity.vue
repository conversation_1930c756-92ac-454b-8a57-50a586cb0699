<template>
  <div class="medical-security">


    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <h3 class="search-title">
          <el-icon><Search /></el-icon>
          搜索筛选
        </h3>
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch" :loading="loading" class="action-btn">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset" class="action-btn">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleExport" :loading="exportLoading" class="action-btn">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>

      <div class="search-container">
        <el-form :model="searchForm" class="search-form" label-width="100px">
          <!-- 第一行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名">
                <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证号">
                <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属单位">
                <el-input v-model="searchForm.organization" placeholder="请输入医疗机构名称" clearable />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="区域">
                <el-input v-model="searchForm.region" placeholder="请输入区域" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处理状态">
                <el-select v-model="searchForm.processingStatus" placeholder="请选择处理状态" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="未处理" value="0" />
                  <el-option label="无需处理" value="1" />
                  <el-option label="重点关注" value="2" />
                  <el-option label="调岗/劝退" value="3" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="异常类型">
                <el-select 
                  v-model="searchForm.abnormalTypes" 
                  placeholder="请选择异常类型" 
                  multiple
                  clearable 
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in abnormalTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <div class="stats-info">
      <el-alert
        :title="`医疗机构共有 ${pagination.total} 名异常安保人员`"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <PersonnelTable
        :data="tableData"
        :loading="loading"
        @view-detail="handleViewDetail"
        @add-blacklist="handleAddBlacklist"
      />
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 人员详情弹窗 -->
    <PersonnelDetailDialog
      v-model="detailDialogVisible"
      :personnel-id="selectedPersonnelId"
      @edit="handleEditFromDetail"
    />

    <!-- 人员处理抽屉 -->
    <PersonnelProcessingDrawer
      v-model="processingDrawerVisible"
      :personnel-id="selectedPersonnelId"
      @processing-completed="handleProcessingCompleted"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import PersonnelTable from '@/components/background-check/PersonnelTable.vue'
import PersonnelDetailDialog from '@/components/background-check/PersonnelDetailDialog.vue'
import PersonnelProcessingDrawer from '@/components/background-check/PersonnelProcessingDrawer.vue'
import { getPersonnelList, exportPersonnelData } from '@/api/background-check'
import { backgroundCheckAbnormalTypes } from '@/data/personnelMockData'

// 异常类型选项
const abnormalTypeOptions = backgroundCheckAbnormalTypes

// 搜索表单
const searchForm = reactive({
  name: '',
  idCard: '',
  organization: '',
  region: '',
  processingStatus: '',
  abnormalTypes: [] as string[]
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const exportLoading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗状态
const detailDialogVisible = ref(false)
const processingDrawerVisible = ref(false)
const selectedPersonnelId = ref<number | null>(null)

// 获取人员列表
const fetchPersonnelList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      // 固定筛选条件：医疗机构的异常人员
      industry: ['healthcare'], // 医疗机构
      backgroundCheckResult: '2', // 异常
      current: pagination.page,
      size: pagination.size
    }
    
    const response = await getPersonnelList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchPersonnelList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    idCard: '',
    organization: '',
    region: '',
    processingStatus: '',
    abnormalTypes: []
  })
  pagination.page = 1
  fetchPersonnelList()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const params = {
      ...searchForm,
      industry: ['healthcare'],
      backgroundCheckResult: '2'
    }
    await exportPersonnelData(params)
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  } finally {
    exportLoading.value = false
  }
}

// 查看详情
const handleViewDetail = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  detailDialogVisible.value = true
}

// 从详情页编辑
const handleEditFromDetail = (personnelId: number) => {
  detailDialogVisible.value = false
  ElMessage.info('编辑功能开发中')
}

// 处理异常人员
const handleAddBlacklist = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  processingDrawerVisible.value = true
}

// 处理完成回调
const handleProcessingCompleted = () => {
  fetchPersonnelList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchPersonnelList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchPersonnelList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchPersonnelList()
})
</script>

<style scoped>
.medical-security {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
}

.search-container {
  background: #fafafa;
  padding: 20px;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 16px;
}

.stats-info {
  margin-bottom: 20px;
}

.table-card {
  border: 1px solid #e4e7ed;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
