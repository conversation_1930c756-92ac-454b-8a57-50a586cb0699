<template>
  <div class="personnel-management">
    <!-- 角色切换器 -->
    <RoleSwitcher v-if="isDev" />
    
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>专职保卫管理</h2>
      <p class="page-description">管理和查看专职保卫人员的背审信息</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <SearchForm
      v-model="searchForm"
      :form-config="searchFormConfig"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
      @field-change="handleFieldChange"
    >
      <template #extra-actions>
        <el-button 
          v-permission="'personnel:export'" 
          type="primary" 
          @click="handleExport" 
          :loading="exportLoading" 
          class="action-btn"
        >
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>
    </SearchForm>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <div class="table-title">
          <h3>专职保卫人员列表</h3>
          <el-tag type="info" size="small">共 {{ pagination.total }} 人</el-tag>
        </div>
        <div class="table-actions">
          <el-button 
            v-permission="'personnel:add'"
            type="primary" 
            @click="handleAdd"
          >
            <el-icon><Plus /></el-icon>
            新增人员
          </el-button>
        </div>
      </div>
      
      <PersonnelTable
        :data="tableData"
        :loading="loading"
        :selectable="true"
        :personnel-type="1"
        @view-detail="handleViewDetail"
        @edit="handleEdit"
        @delete="handleDelete"
        @selection-change="handleSelectionChange"
      />
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 人员详情弹窗 -->
    <PersonnelDetailDialog
      v-model="detailDialogVisible"
      :personnel-id="selectedPersonnelId"
      @edit="handleEditFromDetail"
    />

    <!-- 编辑数据弹窗 -->
    <PersonnelEditDialog 
      v-model="editDialogVisible"
      :personnel-id="selectedPersonnelId"
      :personnel-type="1"
      @success="handleEditSuccess"
    />

    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        已选择 {{ selectedRows.length }} 项
      </div>
      <div class="batch-actions">
        <el-button @click="clearSelection">取消选择</el-button>
        <el-button 
          v-permission="'personnel:batch:export'"
          type="primary" 
          @click="handleBatchExport"
        >
          批量导出
        </el-button>
        <el-button 
          v-permission="'personnel:batch:process'"
          type="warning" 
          @click="handleBatchProcess"
        >
          批量处理
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Plus } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/authStore'
import SearchForm from '@/components/common/SearchForm.vue'
import PersonnelTable from '@/components/background-check/PersonnelTable.vue'
import PersonnelDetailDialog from '@/components/background-check/PersonnelDetailDialog.vue'
import PersonnelEditDialog from '@/components/background-check/PersonnelEditDialog.vue'
import RoleSwitcher from '@/components/common/RoleSwitcher.vue'
import { mockApi } from '@/utils/mockData'
import { backgroundCheckAbnormalTypes, industryTypes } from '@/data/personnelMockData'

// 权限相关
const authStore = useAuthStore()
const isDev = computed(() => import.meta.env.DEV)

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const selectedPersonnelId = ref<number | null>(null)

// 搜索表单
const searchForm = ref({
  name: '',
  idCard: '',
  phone: '',
  organization: '',
  region: '',
  backgroundCheckResult: '',
  processingStatus: '',
  status: '',
  entryDateStart: '',
  entryDateEnd: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 搜索表单配置 - 暂时使用简化配置
const searchFormConfig = computed(() => {
  return []
})

// 方法
const fetchData = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm.value,
      personnelType: 1, // 固定为专职保卫
      page: pagination.page,
      size: pagination.size
    }
    
    const response = await mockApi.getPersonnelList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取专职保卫数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  pagination.page = 1
  fetchData()
}

const handleFieldChange = () => {
  // 字段变化时的处理逻辑
}

const handleExport = async () => {
  try {
    exportLoading.value = true
    // 导出逻辑
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleAdd = () => {
  selectedPersonnelId.value = null
  editDialogVisible.value = true
}

const handleViewDetail = (id: number) => {
  selectedPersonnelId.value = id
  detailDialogVisible.value = true
}

const handleEdit = (id: number) => {
  selectedPersonnelId.value = id
  editDialogVisible.value = true
}

const handleEditFromDetail = (id: number) => {
  detailDialogVisible.value = false
  handleEdit(id)
}

const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 删除逻辑
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    // 用户取消删除
  }
}

const handleEditSuccess = () => {
  editDialogVisible.value = false
  fetchData()
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const clearSelection = () => {
  selectedRows.value = []
}

const handleBatchExport = () => {
  ElMessage.success(`批量导出 ${selectedRows.value.length} 条记录`)
}

const handleBatchProcess = () => {
  ElMessage.success(`批量处理 ${selectedRows.value.length} 条记录`)
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(async () => {
  // 初始化权限
  if (!authStore.initialized) {
    await authStore.initAuth()
  }
  fetchData()
})
</script>

<style scoped>
.personnel-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 12px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1000;
}

.batch-info {
  color: #606266;
  font-size: 14px;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
