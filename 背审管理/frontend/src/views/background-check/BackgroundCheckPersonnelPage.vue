<!-- 背审人员管理页面 -->
<template>
  <div class="background-check-personnel">
    <!-- 状态切换Tab -->
    <el-card class="tabs-card" shadow="never">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="待背审" name="pending">
          <template #label>
            <span class="tab-label">
              待背审
              <el-badge :value="statistics.pending" :max="99" class="tab-badge" />
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="背审中" name="in_progress">
          <template #label>
            <span class="tab-label">
              背审中
              <el-badge :value="statistics.inProgress" :max="99" class="tab-badge" />
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="已完成背审" name="completed">
          <template #label>
            <span class="tab-label">
              已完成背审
              <el-badge :value="statistics.completed" :max="99" class="tab-badge" />
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 筛选条件区域 -->
    <SearchForm
      v-model="searchForm"
      :form-config="searchFormConfig"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 统计卡片 -->
    <BackgroundCheckStatusCard
      :statistics="statistics"
      :show-risk="true"
      :show-overdue="true"
      @card-click="handleCardClick"
      @show-detail="showStatisticsDetail = true"
    />

    <!-- 批量操作工具栏 -->
    <BatchOperationToolbar
      :selected-count="selectedPersonnel.length"
      :filtered-count="pagination.total"
      :current-status="activeTab"
      @mode-change="handleModeChange"
      @action="handleBatchAction"
    />

    <!-- 人员列表表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="idCard" label="身份证号" width="150">
          <template #default="scope">
            {{ maskIdCard(scope.row.idCard) }}
          </template>
        </el-table-column>
        <el-table-column prop="personnelType" label="人员类型" width="100">
          <template #default="scope">
            <el-tag :type="getPersonnelTypeColor(scope.row.personnelType)" size="small">
              {{ getPersonnelTypeText(scope.row.personnelType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="backgroundCheckStatus" label="背审状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.backgroundCheckStatus)" size="small">
              {{ getStatusText(scope.row.backgroundCheckStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelColor(scope.row.riskLevel)" size="small">
              {{ getRiskLevelText(scope.row.riskLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastCheckDate" label="最后背审时间" width="120" />
        <el-table-column prop="department" label="所属部门" min-width="150" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 人员详情抽屉 -->
    <PersonnelDetailDrawer
      v-model="detailDrawerVisible"
      :personnel-id="selectedPersonnelId"
      @view-task="handleViewTask"
    />

    <!-- 发起背审任务弹窗 -->
    <StartCheckTaskDialog
      v-model="startTaskDialogVisible"
      :selected-count="selectedPersonnel.length"
      :filtered-count="pagination.total"
      :selected-personnel="selectedPersonnelNames"
      :operation-mode="operationMode"
      @submit="handleStartTask"
    />

    <!-- 统计详情弹窗 -->
    <StatisticsDetailDialog
      v-model="showStatisticsDetail"
      :statistics="statistics"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import SearchForm from '@/components/common/SearchForm.vue'
import BackgroundCheckStatusCard from '@/components/background-check/BackgroundCheckStatusCard.vue'
import BatchOperationToolbar from '@/components/background-check/BatchOperationToolbar.vue'
import PersonnelDetailDrawer from '@/components/background-check/PersonnelDetailDrawer.vue'
import StartCheckTaskDialog from '@/components/background-check/StartCheckTaskDialog.vue'
import StatisticsDetailDialog from '@/components/background-check/StatisticsDetailDialog.vue'
import { createPersonnelSearchConfig } from '@/config/personnelSearchConfig'
import { backgroundCheckAbnormalTypes, industryTypes, getPersonnelTypeText } from '@/data/personnelMockData'
import type { PersonnelData } from '@/data/personnelMockData'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeTab = ref<'pending' | 'in_progress' | 'completed'>('pending')
const tableData = ref<PersonnelData[]>([])
const selectedPersonnel = ref<PersonnelData[]>([])
const operationMode = ref<'selected' | 'filtered'>('selected')

// 弹窗控制
const detailDrawerVisible = ref(false)
const startTaskDialogVisible = ref(false)
const showStatisticsDetail = ref(false)
const selectedPersonnelId = ref<number>()

// 搜索表单
const searchForm = ref({
  name: '',
  idCard: '',
  organization: '',
  region: '',
  riskLevel: '',
  personnelType: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计数据
const statistics = reactive({
  total: 1234,
  pending: 123,
  inProgress: 45,
  completed: 678,
  risk: 8,
  overdue: 3
})

// 搜索表单配置
const searchFormConfig = computed(() => {
  return createPersonnelSearchConfig(industryTypes, backgroundCheckAbnormalTypes)
})

// 选中人员姓名列表
const selectedPersonnelNames = computed(() => {
  return selectedPersonnel.value.map(p => p.name)
})

// 工具函数
const maskIdCard = (idCard: string) => {
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const getPersonnelTypeColor = (type: number) => {
  const colors = { 1: 'primary', 2: 'success', 3: 'warning' }
  return colors[type] || 'info'
}

const getStatusColor = (status?: string) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success'
  }
  return colors[status] || 'info'
}

const getStatusText = (status?: string) => {
  const texts = {
    pending: '待背审',
    in_progress: '背审中',
    completed: '已完成'
  }
  return texts[status] || '未知'
}

const getRiskLevelColor = (level?: string) => {
  const colors = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return colors[level] || 'info'
}

const getRiskLevelText = (level?: string) => {
  const texts = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return texts[level] || '未评估'
}

// 事件处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName as 'pending' | 'in_progress' | 'completed'
  pagination.page = 1
  selectedPersonnel.value = []
  fetchData()
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  pagination.page = 1
  fetchData()
}

const handleCardClick = (key: string) => {
  // 点击统计卡片切换到对应状态
  if (key === 'pending' || key === 'in_progress' || key === 'completed') {
    activeTab.value = key
    handleTabChange(key)
  }
}

const handleModeChange = (mode: 'selected' | 'filtered') => {
  operationMode.value = mode
}

const handleBatchAction = async (actionKey: string, mode: 'selected' | 'filtered') => {
  const count = mode === 'selected' ? selectedPersonnel.value.length : pagination.total
  
  if (count === 0) {
    ElMessage.warning('请选择要操作的人员')
    return
  }

  switch (actionKey) {
    case 'start_check':
      startTaskDialogVisible.value = true
      break
    case 'cancel_task':
      await handleCancelTask(mode)
      break
    case 'restart_check':
      startTaskDialogVisible.value = true
      break
    case 'export':
      await handleExport(mode)
      break
  }
}

const handleSelectionChange = (selection: PersonnelData[]) => {
  selectedPersonnel.value = selection
}

const handleViewDetail = (row: PersonnelData) => {
  selectedPersonnelId.value = row.id
  detailDrawerVisible.value = true
}

const handleViewTask = (taskId: string) => {
  // 跳转到任务详情页面
  router.push(`/background-check/tasks/${taskId}`)
}

const handleStartTask = async (formData: any) => {
  try {
    // 这里调用API发起背审任务
    console.log('发起背审任务:', formData)
    ElMessage.success('背审任务发起成功')
    
    // 刷新数据
    await fetchData()
    selectedPersonnel.value = []
  } catch (error) {
    console.error('发起背审任务失败:', error)
    ElMessage.error('发起背审任务失败')
  }
}

const handleCancelTask = async (mode: 'selected' | 'filtered') => {
  try {
    const count = mode === 'selected' ? selectedPersonnel.value.length : pagination.total
    await ElMessageBox.confirm(
      `确定要撤销 ${count} 人的背审任务吗？`,
      '确认撤销',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里调用API撤销任务
    ElMessage.success('背审任务撤销成功')
    
    // 刷新数据
    await fetchData()
    selectedPersonnel.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销背审任务失败:', error)
      ElMessage.error('撤销背审任务失败')
    }
  }
}

const handleExport = async (mode: 'selected' | 'filtered') => {
  try {
    const count = mode === 'selected' ? selectedPersonnel.value.length : pagination.total
    ElMessage.success(`导出 ${count} 条记录成功`)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 数据获取
const fetchData = async () => {
  try {
    loading.value = true
    
    // 这里应该调用真实的API
    // 暂时使用Mock数据
    const mockData = generateMockData()
    tableData.value = mockData.records
    pagination.total = mockData.total
    
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 生成Mock数据
const generateMockData = () => {
  const mockPersonnel: PersonnelData[] = []
  const statusMap = {
    pending: 'pending',
    in_progress: 'in_progress',
    completed: 'completed'
  }
  
  for (let i = 1; i <= pagination.size; i++) {
    mockPersonnel.push({
      id: i,
      name: `测试人员${i}`,
      idCard: `13060219850115${String(i).padStart(4, '0')}`,
      phone: `1380312800${i}`,
      email: `test${i}@baoding.com`,
      department: '保定市公安局',
      position: '安保队长',
      personnelType: Math.floor(Math.random() * 3) + 1,
      backgroundCheckResult: Math.floor(Math.random() * 3),
      status: 1,
      entryDate: '2020-03-15',
      region: '保定市竞秀区',
      createTime: '2020-03-15 09:00:00',
      updateTime: '2023-12-01 10:30:00',
      backgroundCheckStatus: statusMap[activeTab.value],
      lastCheckDate: '2023-06-01',
      nextCheckDate: '2024-06-01',
      checkCount: Math.floor(Math.random() * 5) + 1,
      riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high'
    })
  }
  
  return {
    records: mockPersonnel,
    total: statistics[activeTab.value] || 0
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})

// 组件复用标识
// 可复用组件：
// - BackgroundCheckStatusCard: 适用于背审结果处理模块
// - BatchOperationToolbar: 适用于背审结果处理模块
// - PersonnelDetailDrawer: 适用于背审结果处理模块
// - StartCheckTaskDialog: 适用于背审结果处理模块
// - StatisticsDetailDialog: 适用于背审结果处理模块
</script>

<style scoped>
.background-check-personnel {
  padding: 10px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.tabs-card {
  margin-bottom: 16px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-badge {
  transform: scale(0.8);
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
