<!-- 背审人员管理页面 -->
<template>
  <div class="background-check-personnel">
    <!-- 状态切换Tab -->
    <div class="tabs-section">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="待背审" name="pending" />
        <el-tab-pane label="背审中" name="in_progress" />
        <el-tab-pane label="已完成" name="completed" />
      </el-tabs>
    </div>

    <!-- 筛选条件区域 -->
    <div class="search-section">
      <SearchForm
        v-model="searchForm"
        :form-config="searchFormConfig"
        :custom-buttons="searchCustomButtons"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        @custom-button-click="handleCustomButtonClick"
      />
    </div>

    <!-- 表格标题行 -->
    <div class="table-header">
      <div class="table-info">
        <span class="status-info">{{ getStatusText(activeTab) }}：{{ pagination.total }}人</span>
        <span v-if="selectedPersonnel.length > 0" class="selected-info">
          已选择：{{ selectedPersonnel.length }}人
        </span>
      </div>
      <div class="table-actions">
        <el-button
          v-if="activeTab === 'pending'"
          type="primary"
          :disabled="selectedPersonnel.length === 0"
          @click="handleStartCheck"
        >
          发起背审
        </el-button>
        <el-button
          v-if="activeTab === 'in_progress'"
          type="warning"
          :disabled="selectedPersonnel.length === 0"
          @click="handleCancelTask"
        >
          撤销任务
        </el-button>
        <el-button
          v-if="activeTab === 'completed'"
          type="success"
          :disabled="selectedPersonnel.length === 0"
          @click="handleRestartCheck"
        >
          重新发起
        </el-button>
      </div>
    </div>

    <!-- 人员列表表格 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <!-- 动态渲染表格列 -->
        <template v-for="column in currentTableColumns" :key="column.prop || column.label">
          <!-- 选择列 -->
          <el-table-column
            v-if="column.type === 'selection'"
            type="selection"
            :width="column.width"
          />

          <!-- 头像列 -->
          <el-table-column
            v-else-if="column.slot === 'avatar'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
          >
            <template #default="scope">
              <PersonnelAvatar
                :src="scope.row.avatar"
                :name="scope.row.name"
                :personnel-id="scope.row.id"
                :size="55"
                @click="handleAvatarClick"
              />
            </template>
          </el-table-column>

          <!-- 身份证号列（脱敏显示） -->
          <el-table-column
            v-else-if="column.slot === 'idCard'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
          >
            <template #default="scope">
              {{ maskIdCard(scope.row.idCard) }}
            </template>
          </el-table-column>

          <!-- 人员类型列（纯文字显示） -->
          <el-table-column
            v-else-if="column.slot === 'personnelType'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
          >
            <template #default="scope">
              {{ getPersonnelTypeText(scope.row.personnelType) }}
            </template>
          </el-table-column>

          <!-- 负责方列 -->
          <el-table-column
            v-else-if="column.slot === 'responsible'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
          >
            <template #default="scope">
              {{ scope.row.responsiblePersonName || scope.row.responsibleOrgName || '-' }}
            </template>
          </el-table-column>

          <!-- 背审结果列 -->
          <el-table-column
            v-else-if="column.slot === 'backgroundCheckResult'"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
          >
            <template #default="scope">
              <span v-if="scope.row.backgroundCheckResult === 'abnormal' && scope.row.abnormalTypes">
                {{ getAbnormalTypesText(scope.row.abnormalTypes) }}
              </span>
              <span v-else>
                {{ getBackgroundCheckResultText(scope.row.backgroundCheckResult) }}
              </span>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            v-else-if="column.slot === 'action'"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
          >
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                text
                @click="handleViewDetail(scope.row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>

          <!-- 普通列 -->
          <el-table-column
            v-else
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
          />
        </template>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 人员详情抽屉 -->
    <PersonnelDetailDrawer
      v-model="detailDrawerVisible"
      :personnel-id="selectedPersonnelId"
      @view-task="handleViewTask"
    />

    <!-- 发起背审任务弹窗 -->
    <StartCheckTaskDialog
      v-model="startTaskDialogVisible"
      :selected-count="selectedPersonnel.length"
      :selected-personnel="selectedPersonnelNames"
      @submit="handleStartTask"
    />

    <!-- 统计详情弹窗 -->
    <StatisticsDetailDialog
      v-model="showStatisticsDetail"
      :statistics="statistics"
    />

    <!-- 头像预览对话框 -->
    <el-dialog
      v-model="avatarPreviewVisible"
      :title="`${avatarPreviewData.name} - 头像预览`"
      width="400px"
      center
    >
      <div class="avatar-preview">
        <el-image
          :src="avatarPreviewData.src"
          :alt="avatarPreviewData.name"
          fit="contain"
          style="width: 100%; max-height: 300px;"
        >
          <template #error>
            <div class="image-error">
              <el-icon size="50">
                <User />
              </el-icon>
              <p>图片加载失败</p>
            </div>
          </template>
        </el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, User } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import SearchForm from '@/components/common/SearchForm.vue'
import PersonnelAvatar from '@/components/common/PersonnelAvatar.vue'
import PersonnelDetailDrawer from '@/components/background-check/PersonnelDetailDrawer.vue'
import StartCheckTaskDialog from '@/components/background-check/StartCheckTaskDialog.vue'
import StatisticsDetailDialog from '@/components/background-check/StatisticsDetailDialog.vue'
import { getStatusConfig } from '@/config/backgroundCheckStatusConfig'
import { getPersonnelTypeText } from '@/data/personnelMockData'
import type { PersonnelData } from '@/data/personnelMockData'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeTab = ref<'pending' | 'in_progress' | 'completed'>('pending')
const tableData = ref<PersonnelData[]>([])
const selectedPersonnel = ref<PersonnelData[]>([])

// 弹窗控制
const detailDrawerVisible = ref(false)
const startTaskDialogVisible = ref(false)
const showStatisticsDetail = ref(false)
const selectedPersonnelId = ref<number>()

// 头像预览
const avatarPreviewVisible = ref(false)
const avatarPreviewData = ref({
  src: '',
  name: '',
  personnelId: ''
})

// 搜索表单
const searchForm = ref({
  name: '',
  idCard: '',
  organization: '',
  region: [] as string[],
  personnelType: [] as number[],
  entryDate: [] as string[],
  responsiblePerson: '',
  responsibleOrg: '',
  backgroundCheckResult: '',
  abnormalType: [] as string[]
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10, // 默认10条/页
  total: 0
})

// 统计数据
const statistics = reactive({
  total: 1234,
  pending: 123,
  inProgress: 45,
  completed: 678,
  risk: 8,
  overdue: 3
})

// 自定义按钮配置
const searchCustomButtons = computed(() => [
  {
    key: 'export',
    label: '导出Excel',
    type: 'default' as const,
    icon: Download
  }
])

// 根据当前状态获取搜索表单配置
const searchFormConfig = computed(() => {
  const config = getStatusConfig(activeTab.value)
  return config.searchConfig
})

// 根据当前状态获取表格列配置
const currentTableColumns = computed(() => {
  const config = getStatusConfig(activeTab.value)
  return config.tableColumns
})

// 选中人员姓名列表
const selectedPersonnelNames = computed(() => {
  return selectedPersonnel.value.map(p => p.name)
})

// 工具函数
const maskIdCard = (idCard: string) => {
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const getStatusText = (status?: string) => {
  if (!status) return '未知'
  const texts: Record<string, string> = {
    pending: '待背审',
    in_progress: '背审中',
    completed: '已完成'
  }
  return texts[status] || '未知'
}

const getBackgroundCheckResultText = (result?: string) => {
  if (!result) return '-'
  const texts: Record<string, string> = {
    normal: '正常',
    abnormal: '异常',
    risk: '风险'
  }
  return texts[result] || result
}

const getAbnormalTypesText = (types?: string[]) => {
  if (!types || types.length === 0) return '异常'
  const typeTexts: Record<string, string> = {
    identity_abnormal: '身份信息异常',
    criminal_record: '犯罪记录',
    credit_issue: '信用问题',
    other_abnormal: '其他异常'
  }
  return types.map(type => typeTexts[type] || type).join('、')
}

// 事件处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName as 'pending' | 'in_progress' | 'completed'
  pagination.page = 1
  selectedPersonnel.value = []
  fetchData()
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  pagination.page = 1
  fetchData()
}

// 自定义按钮点击处理
const handleCustomButtonClick = (button: any) => {
  switch (button.key) {
    case 'export':
      handleExport()
      break
  }
}

// 表格操作按钮处理
const handleStartCheck = () => {
  if (selectedPersonnel.value.length === 0) {
    ElMessage.warning('请先选择要发起背审的人员')
    return
  }
  startTaskDialogVisible.value = true
}

const handleCancelTask = async () => {
  if (selectedPersonnel.value.length === 0) {
    ElMessage.warning('请先选择要撤销任务的人员')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要撤销 ${selectedPersonnel.value.length} 人的背审任务吗？`,
      '确认撤销',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('背审任务撤销成功')
    await fetchData()
    selectedPersonnel.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销背审任务失败:', error)
      ElMessage.error('撤销背审任务失败')
    }
  }
}

const handleRestartCheck = () => {
  if (selectedPersonnel.value.length === 0) {
    ElMessage.warning('请先选择要重新发起背审的人员')
    return
  }
  startTaskDialogVisible.value = true
}

const handleSelectionChange = (selection: PersonnelData[]) => {
  selectedPersonnel.value = selection
}

const handleViewDetail = (row: PersonnelData) => {
  selectedPersonnelId.value = row.id
  detailDrawerVisible.value = true
}

const handleViewTask = (taskId: string) => {
  // 跳转到任务详情页面
  router.push(`/background-check/tasks/${taskId}`)
}

const handleStartTask = async (formData: any) => {
  try {
    // 这里调用API发起背审任务
    console.log('发起背审任务:', formData)
    ElMessage.success('背审任务发起成功')
    
    // 刷新数据
    await fetchData()
    selectedPersonnel.value = []
  } catch (error) {
    console.error('发起背审任务失败:', error)
    ElMessage.error('发起背审任务失败')
  }
}

const handleExport = async () => {
  try {
    const count = selectedPersonnel.value.length > 0 ? selectedPersonnel.value.length : pagination.total
    ElMessage.success(`导出 ${count} 条记录成功`)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 头像点击处理
const handleAvatarClick = (data: { src: string; name: string; personnelId?: string | number }) => {
  avatarPreviewData.value = {
    src: data.src,
    name: data.name,
    personnelId: String(data.personnelId || '')
  }
  avatarPreviewVisible.value = true
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 数据获取
const fetchData = async () => {
  try {
    loading.value = true
    
    // 这里应该调用真实的API
    // 暂时使用Mock数据
    const mockData = generateMockData()
    tableData.value = mockData.records
    pagination.total = mockData.total
    
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 生成Mock数据
const generateMockData = () => {
  const mockPersonnel: PersonnelData[] = []
  const statusMap: Record<string, 'pending' | 'in_progress' | 'completed'> = {
    pending: 'pending',
    in_progress: 'in_progress',
    completed: 'completed'
  }

  const statisticsMap: Record<string, number> = {
    pending: statistics.pending,
    in_progress: statistics.inProgress,
    completed: statistics.completed
  }

  const responsiblePersons = ['李审查员', '王审查员', '张审查员', '刘审查员']
  const responsibleOrgs = ['莲池分局', '竞秀分局', '满城分局', '清苑分局']
  const regions = ['保定市竞秀区', '保定市莲池区', '保定市满城区', '保定市清苑区']
  const genders = ['男', '女']
  const abnormalTypes = [
    ['identity_abnormal'],
    ['criminal_record'],
    ['credit_issue'],
    ['identity_abnormal', 'credit_issue'],
    ['other_abnormal']
  ]

  for (let i = 1; i <= pagination.size; i++) {
    const personnelData: any = {
      id: i,
      name: `测试人员${i}`,
      gender: genders[Math.floor(Math.random() * genders.length)],
      idCard: `13060219850115${String(i).padStart(4, '0')}`,
      phone: `1380312800${String(i).padStart(2, '0')}`,
      email: `test${i}@baoding.com`,
      department: '保定市公安局',
      position: '安保队长',
      personnelType: Math.floor(Math.random() * 3) + 1,
      backgroundCheckResult: Math.floor(Math.random() * 3),
      status: 1,
      entryDate: '2020-03-15',
      region: regions[Math.floor(Math.random() * regions.length)],
      createTime: '2020-03-15 09:00:00',
      updateTime: '2023-12-01 10:30:00',
      backgroundCheckStatus: statusMap[activeTab.value],
      lastCheckDate: '2023-06-01',
      nextCheckDate: '2024-06-01',
      checkCount: Math.floor(Math.random() * 5) + 1,
      riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high',
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${i}` // 生成随机头像
    }

    // 根据状态添加特定字段
    if (activeTab.value === 'in_progress' || activeTab.value === 'completed') {
      personnelData.responsiblePersonName = responsiblePersons[Math.floor(Math.random() * responsiblePersons.length)]
      personnelData.responsibleOrgName = responsibleOrgs[Math.floor(Math.random() * responsibleOrgs.length)]
      personnelData.dueDate = '2024-02-15'
    }

    if (activeTab.value === 'completed') {
      personnelData.completedDate = '2024-01-20'
      const result = ['normal', 'abnormal', 'risk'][Math.floor(Math.random() * 3)]
      personnelData.backgroundCheckResult = result

      // 如果是异常结果，添加异常类型
      if (result === 'abnormal') {
        personnelData.abnormalTypes = abnormalTypes[Math.floor(Math.random() * abnormalTypes.length)]
      }
    }

    mockPersonnel.push(personnelData)
  }

  return {
    records: mockPersonnel,
    total: statisticsMap[activeTab.value] || 0
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})

// 组件复用标识
// 可复用组件：
// - BackgroundCheckStatusCard: 适用于背审结果处理模块
// - BatchOperationToolbar: 适用于背审结果处理模块
// - PersonnelDetailDrawer: 适用于背审结果处理模块
// - StartCheckTaskDialog: 适用于背审结果处理模块
// - StatisticsDetailDialog: 适用于背审结果处理模块
</script>

<style scoped>
.background-check-personnel {
  padding: 20px;
  background-color: #ffffff;
  min-height: calc(100vh - 60px);
}

.tabs-section {
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.search-section {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-info {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.selected-info {
  font-size: 14px;
  color: #409eff;
  background: #ecf5ff;
  padding: 4px 12px;
  border-radius: 4px;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .background-check-personnel {
    padding: 10px;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .table-info {
    justify-content: center;
  }

  .table-actions {
    justify-content: center;
  }
}

/* 头像预览样式 */
.avatar-preview {
  text-align: center;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.image-error p {
  margin-top: 10px;
  font-size: 14px;
}
</style>
