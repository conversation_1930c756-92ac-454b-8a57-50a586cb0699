<template>
  <div class="data-management">
    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <h3 class="search-title">
          <el-icon><Search /></el-icon>
          搜索筛选
        </h3>
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch" :loading="loading" class="action-btn">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset" class="action-btn">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleExport" :loading="exportLoading" class="action-btn">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>

      <div class="search-container">
        <el-form :model="searchForm" class="search-form" label-width="100px">
          <!-- 第一行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名">
                <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证号">
                <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属单位">
                <el-input v-model="searchForm.organization" placeholder="请输入所属单位" clearable />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="所属行业">
                <el-select
                  v-model="searchForm.industry"
                  placeholder="请选择所属行业"
                  multiple
                  clearable
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in industryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="区域">
                <el-select v-model="searchForm.region" placeholder="请选择区域" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="竞秀区" value="竞秀区" />
                  <el-option label="莲池区" value="莲池区" />
                  <el-option label="满城区" value="满城区" />
                  <el-option label="清苑区" value="清苑区" />
                  <el-option label="徐水区" value="徐水区" />
                  <el-option label="涞水县" value="涞水县" />
                  <el-option label="阜平县" value="阜平县" />
                  <el-option label="定兴县" value="定兴县" />
                  <el-option label="唐县" value="唐县" />
                  <el-option label="高阳县" value="高阳县" />
                  <el-option label="容城县" value="容城县" />
                  <el-option label="涞源县" value="涞源县" />
                  <el-option label="望都县" value="望都县" />
                  <el-option label="安新县" value="安新县" />
                  <el-option label="易县" value="易县" />
                  <el-option label="曲阳县" value="曲阳县" />
                  <el-option label="蠡县" value="蠡县" />
                  <el-option label="顺平县" value="顺平县" />
                  <el-option label="博野县" value="博野县" />
                  <el-option label="雄县" value="雄县" />
                  <el-option label="高碑店市" value="高碑店市" />
                  <el-option label="安国市" value="安国市" />
                  <el-option label="定州市" value="定州市" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="人员状态">
                <el-select v-model="searchForm.status" placeholder="请选择人员状态" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="在职" value="1" />
                  <el-option label="离职" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="背审结果">
                <el-select v-model="searchForm.backgroundCheckResult" placeholder="请选择背景审查结果" clearable style="width: 100%" @change="handleBackgroundCheckResultChange">
                  <el-option label="全部" value="" />
                  <el-option label="未审查" value="0" />
                  <el-option label="正常" value="1" />
                  <el-option label="异常" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="searchForm.backgroundCheckResult === '2'">
              <el-form-item label="异常类型">
                <el-select
                  v-model="searchForm.abnormalTypes"
                  placeholder="请选择异常类型，默认全选"
                  multiple
                  clearable
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in abnormalTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处理结果">
                <el-select v-model="searchForm.processingStatus" placeholder="请选择处理结果" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="未处理" value="0" />
                  <el-option label="无需处理" value="1" />
                  <el-option label="重点关注" value="2" />
                  <el-option label="调岗/劝退" value="3" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <!-- 人员类型标签页和操作按钮 -->
    <el-card class="tabs-card" shadow="never">
      <div class="tabs-header">
        <div class="tabs-nav">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部人员" name="all"></el-tab-pane>
            <el-tab-pane label="专职保卫" name="type1"></el-tab-pane>
            <el-tab-pane label="保安人员" name="type2"></el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <div class="tab-content">
        <PersonnelTable
          :data="tableData"
          :loading="loading"
          @view-detail="handleViewDetail"
          @add-blacklist="handleAddBlacklist"
        />
      </div>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 人员详情弹窗 -->
    <PersonnelDetailDialog
      v-model="detailDialogVisible"
      :personnel-id="selectedPersonnelId"
      @edit="handleEditFromDetail"
    />

    <!-- 编辑数据弹窗 -->
    <PersonnelEditDialog 
      v-model="editDialogVisible"
      :personnel-id="selectedPersonnelId"
      @success="handleEditSuccess"
    />

    <!-- 异常处理抽屉 -->
    <PersonnelProcessingDrawer
      v-model="processingDrawerVisible"
      :personnel-id="selectedPersonnelId"
      @processing-completed="handleProcessingCompleted"
    />

    <!-- 发送通知弹窗 -->
    <NoticeDialog 
      v-model="noticeDialogVisible"
      :personnel-id="selectedPersonnelId"
      @success="handleNoticeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import PersonnelTable from '@/components/background-check/PersonnelTable.vue'
import PersonnelDetailDialog from '@/components/background-check/PersonnelDetailDialog.vue'
import PersonnelEditDialog from '@/components/background-check/PersonnelEditDialog.vue'
import PersonnelProcessingDrawer from '@/components/background-check/PersonnelProcessingDrawer.vue'
import NoticeDialog from '@/components/background-check/NoticeDialog.vue'
import { mockApi } from '@/utils/mockData'
import { backgroundCheckAbnormalTypes, industryTypes } from '@/data/personnelMockData'

// 模拟API函数
const getPersonnelList = (params: any) => {
  return mockApi.getPersonnelList(params)
}

const exportPersonnelData = (params: any) => {
  return mockApi.exportPersonnelData(params)
}

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const activeTab = ref('all')
const tableData = ref([])
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const processingDrawerVisible = ref(false)
const noticeDialogVisible = ref(false)
const selectedPersonnelId = ref<number | null>(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  idCard: '',
  organization: '',
  industry: [] as string[],
  region: '',
  status: '',
  backgroundCheckResult: '',
  abnormalTypes: [] as string[],
  processingStatus: ''
})

// 异常类型选项
const abnormalTypeOptions = ref(backgroundCheckAbnormalTypes)

// 行业类型选项
const industryOptions = ref(industryTypes)

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 获取人员列表
const fetchPersonnelList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      personnelType: activeTab.value === 'all' ? undefined : Number(activeTab.value.replace('type', '')),
      current: pagination.page,
      size: pagination.size
    }
    
    const response = await getPersonnelList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchPersonnelList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    idCard: '',
    organization: '',
    industry: [],
    region: '',
    status: '',
    backgroundCheckResult: '',
    abnormalTypes: [],
    processingStatus: ''
  })
  pagination.page = 1
  fetchPersonnelList()
}

// 背景审查结果变化处理
const handleBackgroundCheckResultChange = (value: string) => {
  // 如果不是选择异常，清空异常类型
  if (value !== '2') {
    searchForm.abnormalTypes = []
  }
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  pagination.page = 1
  fetchPersonnelList()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const params = {
      ...searchForm,
      personnelType: activeTab.value === 'all' ? '' : activeTab.value.replace('type', '')
    }
    
    await exportPersonnelData(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 查看详情
const handleViewDetail = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  detailDialogVisible.value = true
}

// 从详情页面编辑数据
const handleEditFromDetail = (personnelId: number) => {
  // 关闭详情弹窗
  detailDialogVisible.value = false
  // 打开编辑弹窗
  selectedPersonnelId.value = personnelId
  editDialogVisible.value = true
}

// 编辑数据
const handleEditData = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  editDialogVisible.value = true
}

// 异常处理
const handleAddBlacklist = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  processingDrawerVisible.value = true
}

// 发送通知
const handleSendNotice = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  noticeDialogVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  editDialogVisible.value = false
  fetchPersonnelList()
}

// 异常处理完成回调
const handleProcessingCompleted = () => {
  // 不关闭抽屉，只刷新列表数据
  fetchPersonnelList()
}

// 通知发送成功回调
const handleNoticeSuccess = () => {
  noticeDialogVisible.value = false
  ElMessage.success('通知发送成功')
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchPersonnelList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchPersonnelList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchPersonnelList()
})
</script>

<style scoped>
.data-management {
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.search-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-title .el-icon {
  color: #409eff;
}

.search-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  min-width: 88px;
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
}

.search-container {
  width: 100%;
}

.search-form {
  width: 100%;
}

/* 标签页卡片样式 */
.tabs-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tabs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tabs-nav {
  flex: 1;
}

.tab-content {
  padding-top: 20px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-tabs__header) {
  margin: 0 !important;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #337ecc 0%, #5aa3e6 100%);
}

:deep(.el-drawer__header) {
  margin-bottom: 0px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  :deep(.el-col) {
    width: 100% !important;
  }

  .search-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
  }
}

/* 动画效果 */
.search-card,
.tabs-card,
.pagination-wrapper {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 异常类型选择器特殊样式 */
:deep(.el-select__tags) {
  max-width: calc(100% - 30px);
}
</style>