<template>
  <div class="focus-list-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>重点关注人员管理</h2>
      <p>管理所有被列入重点关注名单的人员</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <h3 class="search-title">
          <el-icon><Search /></el-icon>
          搜索筛选
        </h3>
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleExport" :loading="exportLoading">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
      
      <div class="search-container">
        <el-form :model="searchForm" class="search-form" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名">
                <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证号">
                <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属单位">
                <el-input v-model="searchForm.organization" placeholder="请输入所属单位" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="区域">
                <el-select v-model="searchForm.region" placeholder="请选择区域" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="竞秀区" value="竞秀区" />
                  <el-option label="莲池区" value="莲池区" />
                  <el-option label="满城区" value="满城区" />
                  <el-option label="清苑区" value="清苑区" />
                  <el-option label="徐水区" value="徐水区" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="异常类型">
                <el-select 
                  v-model="searchForm.abnormalTypes" 
                  placeholder="请选择异常类型" 
                  multiple 
                  clearable 
                  style="width: 100%"
                  collapse-tags
                >
                  <el-option 
                    v-for="item in abnormalTypeOptions" 
                    :key="item.value" 
                    :label="item.label" 
                    :value="item.value" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <div class="table-info">
          <span>共 {{ pagination.total }} 条记录</span>
        </div>
        <div class="table-actions">
          <el-button type="warning" @click="handleBatchRemove" :disabled="selectedIds.length === 0">
            <el-icon><Remove /></el-icon>
            批量移除关注
          </el-button>
        </div>
      </div>

      <el-table 
        v-loading="loading"
        :data="tableData" 
        class="focus-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="idCard" label="身份证号" width="180" />
        <el-table-column prop="organization" label="所属单位" width="200" />
        <el-table-column prop="position" label="职位" width="120" />
        <el-table-column prop="abnormalTypes" label="异常类型" width="200">
          <template #default="{ row }">
            <div v-if="row.abnormalTypes && row.abnormalTypes.length > 0" class="abnormal-types">
              <el-tag 
                v-for="type in row.abnormalTypes" 
                :key="type" 
                type="danger" 
                size="small"
                class="abnormal-tag"
              >
                {{ getSingleAbnormalTypeText(type) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="region" label="区域" width="120" />
        <el-table-column prop="updateTime" label="加入时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row.id)">
              查看详情
            </el-button>
            <el-button type="warning" size="small" @click="handleRemoveFocus(row.id)">
              移除关注
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 人员详情弹窗 -->
    <PersonnelDetailDialog 
      v-model="detailDialogVisible"
      :personnel-id="selectedPersonnelId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, Remove } from '@element-plus/icons-vue'
import { mockApi } from '@/utils/mockData'
import { backgroundCheckAbnormalTypes, getSingleAbnormalTypeText } from '@/data/personnelMockData'
import PersonnelDetailDialog from '@/components/background-check/PersonnelDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const selectedIds = ref<number[]>([])
const detailDialogVisible = ref(false)
const selectedPersonnelId = ref<number | null>(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  idCard: '',
  organization: '',
  region: '',
  abnormalTypes: [] as string[]
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 异常类型选项
const abnormalTypeOptions = ref(backgroundCheckAbnormalTypes)

// 获取重点关注人员列表
const fetchFocusList = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.page,
      size: pagination.size,
      processingStatus: 1, // 只获取重点关注人员
      ...searchForm
    }
    const response = await mockApi.getPersonnelList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取重点关注人员列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchFocusList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    idCard: '',
    organization: '',
    region: '',
    abnormalTypes: []
  })
  pagination.page = 1
  fetchFocusList()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    // 这里可以实现导出逻辑
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 查看详情
const handleViewDetail = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  detailDialogVisible.value = true
}

// 移除关注
const handleRemoveFocus = async (personnelId: number) => {
  try {
    await ElMessageBox.confirm('确认要将该人员移除重点关注名单吗？', '确认操作', {
      type: 'warning'
    })
    
    // 调用API移除关注
    await mockApi.updateProcessingStatus({
      personnelId,
      fromStatus: 1,
      toStatus: 0,
      reason: '手动移除重点关注'
    })
    
    ElMessage.success('移除成功')
    fetchFocusList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除失败')
    }
  }
}

// 批量移除关注
const handleBatchRemove = async () => {
  try {
    await ElMessageBox.confirm(`确认要将选中的 ${selectedIds.value.length} 个人员移除重点关注名单吗？`, '确认操作', {
      type: 'warning'
    })
    
    // 批量调用API
    for (const id of selectedIds.value) {
      await mockApi.updateProcessingStatus({
        personnelId: id,
        fromStatus: 1,
        toStatus: 0,
        reason: '批量移除重点关注'
      })
    }
    
    ElMessage.success('批量移除成功')
    selectedIds.value = []
    fetchFocusList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量移除失败')
    }
  }
}

// 分页相关
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchFocusList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchFocusList()
}

// 格式化时间
const formatDateTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  fetchFocusList()
})
</script>

<style scoped>
.focus-list-management {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.search-card,
.table-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.search-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-info {
  color: #606266;
  font-size: 14px;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.abnormal-types {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.abnormal-tag {
  font-size: 11px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
