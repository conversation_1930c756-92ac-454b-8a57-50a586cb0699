<template>
  <div class="blacklist-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>黑名单管理</h2>
      <p>管理进入黑名单的人员，确保安全风险控制</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-container">
        <el-form :model="searchForm" :inline="true" class="search-form">
          <el-form-item label="姓名">
            <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable />
          </el-form-item>
          <el-form-item label="黑名单类型">
            <el-select v-model="searchForm.blacklistType" placeholder="请选择类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="临时黑名单" value="1" />
              <el-option label="永久黑名单" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="生效中" value="1" />
              <el-option label="已解除" value="2" />
              <el-option label="已过期" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="加入时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-actions-wrapper">
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <div class="table-info">
          <el-tag v-if="selectedRows.length > 0" type="info" class="selected-info">
            已选择 {{ selectedRows.length }} 条记录
          </el-tag>
        </div>
        <div class="table-actions">
          <el-button 
            type="warning" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchRemove"
          >
            <el-icon><Remove /></el-icon>
            批量解除
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            添加黑名单
          </el-button>
          <el-button @click="handleExport" :loading="exportLoading">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <el-table
        :data="tableData"
        :loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="idCard" label="身份证号" width="180" />
        <el-table-column prop="organization" label="所属单位" min-width="150" />
        <el-table-column prop="blacklistType" label="黑名单类型" width="120">
          <template #default="{ row }">
            <el-tag :type="row.blacklistType === 1 ? 'warning' : 'danger'">
              {{ row.blacklistType === 1 ? '临时' : '永久' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="加入原因" min-width="200" show-overflow-tooltip />
        <el-table-column prop="operator" label="操作人" width="100" />
        <el-table-column prop="startDate" label="生效日期" width="120" />
        <el-table-column prop="endDate" label="失效日期" width="120">
          <template #default="{ row }">
            {{ row.endDate || '永久' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 1 ? 'success' : row.status === 2 ? 'info' : 'warning'"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row.id)">
              详情
            </el-button>
            <el-button 
              v-if="row.status === 1"
              type="warning" 
              size="small" 
              @click="handleRemove(row.id)"
            >
              解除
            </el-button>
            <el-button 
              v-if="row.status === 1"
              type="info" 
              size="small" 
              @click="handleEdit(row.id)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 黑名单详情弹窗 -->
    <BlacklistDetailDialog 
      v-model="detailDialogVisible"
      :blacklist-id="selectedBlacklistId"
    />

    <!-- 添加黑名单弹窗 -->
    <BlacklistAddDialog 
      v-model="addDialogVisible"
      @success="handleAddSuccess"
    />

    <!-- 编辑黑名单弹窗 -->
    <BlacklistEditDialog 
      v-model="editDialogVisible"
      :blacklist-id="selectedBlacklistId"
      @success="handleEditSuccess"
    />

    <!-- 解除黑名单弹窗 -->
    <BlacklistRemoveDialog 
      v-model="removeDialogVisible"
      :blacklist-ids="removeIds"
      @success="handleRemoveSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, Plus, Remove } from '@element-plus/icons-vue'
import BlacklistDetailDialog from '@/components/background-check/BlacklistDetailDialog.vue'
import BlacklistAddDialog from '@/components/background-check/BlacklistAddDialog.vue'
import BlacklistEditDialog from '@/components/background-check/BlacklistEditDialog.vue'
import BlacklistRemoveDialog from '@/components/background-check/BlacklistRemoveDialog.vue'
import { getBlacklistList, exportBlacklistData } from '@/api/background-check'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const detailDialogVisible = ref(false)
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const removeDialogVisible = ref(false)
const selectedBlacklistId = ref<number | null>(null)
const removeIds = ref<number[]>([])

// 搜索表单
const searchForm = reactive({
  name: '',
  idCard: '',
  blacklistType: '',
  status: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '生效中',
    2: '已解除',
    3: '已过期'
  }
  return statusMap[status] || '未知'
}

// 获取黑名单列表
const fetchBlacklistList = async () => {
  try {
    loading.value = true
    const { dateRange, ...searchParams } = searchForm
    const params: any = {
      ...searchParams,
      page: pagination.page,
      size: pagination.size
    }
    if (dateRange && dateRange.length === 2) {
      params.startDate = dateRange[0]
      params.endDate = dateRange[1]
    }
    
    const response = await getBlacklistList(params)
    tableData.value = response.data.records || []
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取黑名单列表失败:', error)
    ElMessage.error('获取黑名单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchBlacklistList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    idCard: '',
    blacklistType: '',
    status: '',
    dateRange: []
  })
  pagination.page = 1
  fetchBlacklistList()
}

// 刷新
const handleRefresh = () => {
  fetchBlacklistList()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const { dateRange, ...searchParams } = searchForm
    const params: any = { ...searchParams }
    if (dateRange && dateRange.length === 2) {
      params.startDate = dateRange[0]
      params.endDate = dateRange[1]
    }
    
    await exportBlacklistData(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 选择改变
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 查看详情
const handleViewDetail = (blacklistId: number) => {
  selectedBlacklistId.value = blacklistId
  detailDialogVisible.value = true
}

// 添加黑名单
const handleAdd = () => {
  addDialogVisible.value = true
}

// 编辑黑名单
const handleEdit = (blacklistId: number) => {
  selectedBlacklistId.value = blacklistId
  editDialogVisible.value = true
}

// 解除黑名单
const handleRemove = (blacklistId: number) => {
  removeIds.value = [blacklistId]
  removeDialogVisible.value = true
}

// 批量解除
const handleBatchRemove = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要解除的记录')
    return
  }
  
  removeIds.value = selectedRows.value.map((row: any) => row.id)
  removeDialogVisible.value = true
}

// 添加成功回调
const handleAddSuccess = () => {
  addDialogVisible.value = false
  fetchBlacklistList()
  ElMessage.success('添加成功')
}

// 编辑成功回调
const handleEditSuccess = () => {
  editDialogVisible.value = false
  fetchBlacklistList()
  ElMessage.success('编辑成功')
}

// 解除成功回调
const handleRemoveSuccess = () => {
  removeDialogVisible.value = false
  selectedRows.value = []
  fetchBlacklistList()
  ElMessage.success('解除成功')
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchBlacklistList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchBlacklistList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchBlacklistList()
})
</script>

<style scoped>
.blacklist-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.search-form {
  width: 100%;
  margin-bottom: 0;
}

.search-actions-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.search-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #606266;
  font-size: 14px;
}

.selected-info {
  margin-left: 12px;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 16px;
}

:deep(.el-input) {
  width: 200px;
}

:deep(.el-select) {
  width: 150px;
}

:deep(.el-date-picker) {
  width: 240px;
}
</style>