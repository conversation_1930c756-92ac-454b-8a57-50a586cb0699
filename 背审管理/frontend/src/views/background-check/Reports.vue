<template>
  <div class="reports">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>报表管理</h2>
      <p>生成和管理各类背景审查报表</p>
    </div>

    <!-- 报表生成区域 -->
    <el-card class="generate-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>生成新报表</span>
        </div>
      </template>
      
      <el-form :model="generateForm" :rules="generateRules" ref="generateFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报表类型" prop="reportType">
              <el-select v-model="generateForm.reportType" placeholder="请选择报表类型" style="width: 100%">
                <el-option label="人员统计报表" value="personnel_statistics" />
                <el-option label="黑名单报表" value="blacklist_report" />
                <el-option label="风险分析报表" value="risk_analysis" />
                <el-option label="单位汇总报表" value="organization_summary" />
                <el-option label="月度报表" value="monthly_report" />
                <el-option label="年度报表" value="annual_report" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表格式" prop="format">
              <el-select v-model="generateForm.format" placeholder="请选择格式" style="width: 100%">
                <el-option label="PDF" value="pdf" />
                <el-option label="Excel" value="excel" />
                <el-option label="Word" value="word" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="统计时间" prop="dateRange">
              <el-date-picker
                v-model="generateForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表名称" prop="reportName">
              <el-input v-model="generateForm.reportName" placeholder="请输入报表名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="筛选条件">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-select v-model="generateForm.region" placeholder="选择区域" clearable>
                <el-option label="全部区域" value="" />
                <el-option label="东城区" value="东城区" />
                <el-option label="西城区" value="西城区" />
                <el-option label="朝阳区" value="朝阳区" />
                <el-option label="海淀区" value="海淀区" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="generateForm.personnelType" placeholder="人员类型" clearable>
                <el-option label="全部类型" value="" />
                <el-option label="有编制人员" value="1" />
                <el-option label="无编制人员" value="2" />
                <el-option label="外包人员" value="3" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="generateForm.riskLevel" placeholder="风险等级" clearable>
                <el-option label="全部等级" value="" />
                <el-option label="低风险" value="1" />
                <el-option label="中风险" value="2" />
                <el-option label="高风险" value="3" />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="generateForm.remarks" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleGenerate" :loading="generateLoading">
            <el-icon><Document /></el-icon>
            生成报表
          </el-button>
          <el-button @click="resetGenerateForm">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报表列表 -->
    <el-card class="list-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>报表列表</span>
          <div class="header-actions">
            <el-button @click="handleRefresh" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索筛选 -->
      <div class="search-container">
        <el-form :model="searchForm" :inline="true" class="search-form">
          <el-form-item label="报表名称">
            <el-input v-model="searchForm.reportName" placeholder="请输入报表名称" clearable />
          </el-form-item>
          <el-form-item label="报表类型">
            <el-select v-model="searchForm.reportType" placeholder="请选择类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="人员统计报表" value="personnel_statistics" />
              <el-option label="黑名单报表" value="blacklist_report" />
              <el-option label="风险分析报表" value="risk_analysis" />
              <el-option label="单位汇总报表" value="organization_summary" />
              <el-option label="月度报表" value="monthly_report" />
              <el-option label="年度报表" value="annual_report" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="待处理" value="1" />
              <el-option label="处理中" value="2" />
              <el-option label="已完成" value="3" />
              <el-option label="失败" value="4" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-actions-wrapper">
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleResetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </div>
      
      <!-- 数据表格 -->
      <el-table :data="tableData" :loading="loading" stripe>
        <el-table-column prop="reportName" label="报表名称" min-width="200" />
        <el-table-column prop="reportType" label="报表类型" width="150">
          <template #default="{ row }">
            {{ getReportTypeText(row.reportType) }}
          </template>
        </el-table-column>
        <el-table-column prop="format" label="格式" width="80">
          <template #default="{ row }">
            <el-tag :type="getFormatType(row.format)">{{ row.format.toUpperCase() }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.progress" 
              :status="row.status === 4 ? 'exception' : row.status === 3 ? 'success' : undefined"
              :stroke-width="6"
            />
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="100">
          <template #default="{ row }">
            {{ row.fileSize ? formatFileSize(row.fileSize) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createdBy" label="创建人" width="100" />
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        <el-table-column prop="duration" label="耗时" width="80">
          <template #default="{ row }">
            {{ row.duration ? `${row.duration}s` : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 3" 
              type="primary" 
              size="small" 
              @click="handleDownload(row.id)"
            >
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button 
              v-if="row.status === 3" 
              type="info" 
              size="small" 
              @click="handlePreview(row.id)"
            >
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button 
              v-if="row.status === 4" 
              type="warning" 
              size="small" 
              @click="handleRetry(row.id)"
            >
              <el-icon><Refresh /></el-icon>
              重试
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(row.id)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 报表预览弹窗 -->
    <ReportPreviewDialog 
      v-model="previewDialogVisible"
      :report-id="selectedReportId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Refresh, Document, Download, View, Delete 
} from '@element-plus/icons-vue'
import ReportPreviewDialog from '@/components/background-check/ReportPreviewDialog.vue'
import { mockApi } from '@/utils/mockData'
import type { ReportGenerateData, ReportQuery, ReportInfo } from '@/types/background-check'

// 模拟API函数
const generateReport = (data: ReportGenerateData) => {
  return mockApi.generateReport(data)
}

const getReportList = (params: ReportQuery) => {
  return mockApi.getReportList(params)
}

const downloadReport = (id: number) => {
  return mockApi.downloadReport(id)
}

const deleteReport = (id: number) => {
  return mockApi.deleteReport(id)
}

const retryReport = (id: number) => {
  return mockApi.retryReport(id)
}

// 响应式数据
const loading = ref(false)
const generateLoading = ref(false)
const tableData = ref([])
const previewDialogVisible = ref(false)
const selectedReportId = ref<number | null>(null)
const generateFormRef = ref()

// 生成表单
const generateForm = reactive({
  reportType: '',
  format: 'pdf',
  dateRange: [],
  reportName: '',
  region: '',
  personnelType: '',
  riskLevel: '',
  remarks: ''
})

// 生成表单验证规则
const generateRules = {
  reportType: [{ required: true, message: '请选择报表类型', trigger: 'change' }],
  format: [{ required: true, message: '请选择报表格式', trigger: 'change' }],
  dateRange: [{ required: true, message: '请选择统计时间', trigger: 'change' }],
  reportName: [{ required: true, message: '请输入报表名称', trigger: 'blur' }]
}

// 搜索表单
const searchForm = reactive({
  reportName: '',
  reportType: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 获取报表类型文本
const getReportTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'personnel_statistics': '人员统计报表',
    'blacklist_report': '黑名单报表',
    'risk_analysis': '风险分析报表',
    'organization_summary': '单位汇总报表',
    'monthly_report': '月度报表',
    'annual_report': '年度报表'
  }
  return typeMap[type] || type
}

// 获取格式类型
const getFormatType = (format: string) => {
  const formatMap: Record<string, string> = {
    'pdf': 'danger',
    'excel': 'success',
    'word': 'primary'
  }
  return formatMap[format] || 'info'
}

// 获取状态类型
const getStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'info',
    2: 'warning',
    3: 'success',
    4: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待处理',
    2: '处理中',
    3: '已完成',
    4: '失败'
  }
  return statusMap[status] || '未知'
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生成报表
const handleGenerate = async () => {
  try {
    await generateFormRef.value.validate()
    generateLoading.value = true
    
    const { dateRange, ...restForm } = generateForm
    const params = {
      ...restForm,
      startDate: dateRange[0],
      endDate: dateRange[1]
    }
    
    await generateReport(params)
    ElMessage.success('报表生成任务已提交，请稍后查看')
    
    // 重置表单
    resetGenerateForm()
    
    // 刷新列表
    fetchReportList()
  } catch (error) {
    console.error('生成报表失败:', error)
    ElMessage.error('生成报表失败')
  } finally {
    generateLoading.value = false
  }
}

// 重置生成表单
const resetGenerateForm = () => {
  generateFormRef.value?.resetFields()
  Object.assign(generateForm, {
    reportType: '',
    format: 'pdf',
    dateRange: [],
    reportName: '',
    region: '',
    personnelType: '',
    riskLevel: '',
    remarks: ''
  })
}

// 获取报表列表
const fetchReportList = async () => {
  try {
    loading.value = true
    const params: ReportQuery = {
      reportName: searchForm.reportName,
      reportType: searchForm.reportType,
      status: searchForm.status ? parseInt(searchForm.status) : undefined,
      page: pagination.page,
      size: pagination.size
    }
    
    const response = await getReportList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取报表列表失败:', error)
    ElMessage.error('获取报表列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchReportList()
}

// 重置搜索
const handleResetSearch = () => {
  Object.assign(searchForm, {
    reportName: '',
    reportType: '',
    status: ''
  })
  pagination.page = 1
  fetchReportList()
}

// 刷新
const handleRefresh = () => {
  fetchReportList()
}

// 下载报表
const handleDownload = async (reportId: number) => {
  try {
    await downloadReport(reportId)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 预览报表
const handlePreview = (reportId: number) => {
  selectedReportId.value = reportId
  previewDialogVisible.value = true
}

// 重试生成
const handleRetry = async (reportId: number) => {
  try {
    await retryReport(reportId)
    ElMessage.success('重试任务已提交')
    fetchReportList()
  } catch (error) {
    console.error('重试失败:', error)
    ElMessage.error('重试失败')
  }
}

// 删除报表
const handleDelete = async (reportId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个报表吗？', '确认删除', {
      type: 'warning'
    })
    
    await deleteReport(reportId)
    ElMessage.success('删除成功')
    fetchReportList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchReportList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchReportList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchReportList()
})
</script>

<style scoped>
.reports {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.generate-card {
  margin-bottom: 20px;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.search-form {
  width: 100%;
  margin-bottom: 0;
}

.search-actions-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.search-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 16px;
}

:deep(.el-input) {
  width: 200px;
}

:deep(.el-select) {
  width: 150px;
}

:deep(.el-progress) {
  width: 80px;
}
</style>