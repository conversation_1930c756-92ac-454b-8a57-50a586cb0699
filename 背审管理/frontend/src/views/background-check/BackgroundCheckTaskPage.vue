<!-- 背审任务管理页面 -->
<template>
  <div class="background-check-task">
    <!-- 任务统计卡片 -->
    <div class="task-statistics">
      <el-card 
        v-for="stat in taskStatistics" 
        :key="stat.key"
        class="stat-card"
        :class="stat.type"
        shadow="hover"
        @click="handleStatClick(stat.key)"
      >
        <div class="stat-content">
          <div class="stat-number">{{ stat.count }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div v-if="stat.isOverdue" class="overdue-indicator">
            <el-icon><Warning /></el-icon>
            逾期
          </div>
        </div>
      </el-card>
    </div>

    <!-- 筛选条件区域 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="任务状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待分配" value="pending" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="处理部门">
          <el-select v-model="searchForm.assignedToOrg" placeholder="请选择部门" clearable filterable>
            <el-option
              v-for="dept in departments"
              :key="dept.value"
              :label="dept.label"
              :value="dept.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select v-model="searchForm.priority" placeholder="请选择优先级" clearable>
            <el-option label="普通" value="low" />
            <el-option label="中等" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.createTimeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="是否逾期">
          <el-select v-model="searchForm.isOverdue" placeholder="请选择" clearable>
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <h3>背审任务列表</h3>
        <div class="table-actions">
          <el-button type="primary" @click="handleBatchRemind" :disabled="!hasOverdueTasks">
            <el-icon><Bell /></el-icon>
            批量催办
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
      
      <el-table
        :data="tableData"
        :loading="loading"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="taskNo" label="任务编号" width="140" />
        <el-table-column prop="title" label="任务标题" min-width="150" />
        <el-table-column prop="personnelCount" label="人员数量" width="100" align="center">
          <template #default="scope">
            {{ scope.row.personnelCount }}人
          </template>
        </el-table-column>
        <el-table-column prop="assignedToOrgName" label="处理部门" width="120" />
        <el-table-column label="进度" width="150">
          <template #default="scope">
            <div class="progress-container">
              <el-progress 
                :percentage="scope.row.progress.percentage" 
                :stroke-width="8"
                :show-text="false"
                :status="getProgressStatus(scope.row)"
              />
              <span class="progress-text">
                {{ scope.row.progress.completed }}/{{ scope.row.progress.total }}
                ({{ scope.row.progress.percentage }}%)
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="scope">
            <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="截止时间" width="120">
          <template #default="scope">
            <div :class="{ 'overdue-text': scope.row.isOverdue }">
              {{ formatDate(scope.row.dueDate) }}
              <div v-if="scope.row.isOverdue" class="overdue-badge">
                逾期{{ scope.row.overdueBy }}天
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.isOverdue"
              type="warning"
              size="small"
              text
              @click="handleRemind(scope.row)"
            >
              催办
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 任务详情弹窗 -->
    <TaskDetailDialog
      v-model="taskDetailVisible"
      :task-id="selectedTaskId"
    />

    <!-- 催办弹窗 -->
    <ReminderDialog
      v-model="reminderDialogVisible"
      :task-id="selectedTaskId"
      @submit="handleReminderSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Warning, Bell, Download } from '@element-plus/icons-vue'
import TaskDetailDialog from '@/components/background-check/TaskDetailDialog.vue'
import ReminderDialog from '@/components/background-check/ReminderDialog.vue'
import { backgroundCheckTasksMockData, calculateOverdue } from '@/data/backgroundCheckMockData'
import type { BackgroundCheckTask } from '@/data/backgroundCheckMockData'

// 响应式数据
const loading = ref(false)
const tableData = ref<BackgroundCheckTask[]>([])
const selectedTasks = ref<BackgroundCheckTask[]>([])

// 弹窗控制
const taskDetailVisible = ref(false)
const reminderDialogVisible = ref(false)
const selectedTaskId = ref<string>()

// 搜索表单
const searchForm = reactive({
  status: '',
  assignedToOrg: '',
  priority: '',
  createTimeRange: [],
  isOverdue: undefined as boolean | undefined
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// Mock数据
const departments = [
  { label: '莲池分局', value: 'org-001' },
  { label: '竞秀分局', value: 'org-002' },
  { label: '满城分局', value: 'org-003' },
  { label: '清苑分局', value: 'org-004' },
  { label: '徐水分局', value: 'org-005' }
]

// 任务统计数据
const taskStatistics = computed(() => [
  {
    key: 'total',
    label: '总任务',
    count: 89,
    type: 'primary'
  },
  {
    key: 'pending',
    label: '待分配',
    count: 12,
    type: 'info'
  },
  {
    key: 'in_progress',
    label: '进行中',
    count: 23,
    type: 'primary'
  },
  {
    key: 'completed',
    label: '已完成',
    count: 45,
    type: 'success'
  },
  {
    key: 'cancelled',
    label: '已取消',
    count: 6,
    type: 'info'
  },
  {
    key: 'overdue',
    label: '逾期任务',
    count: 3,
    type: 'danger',
    isOverdue: true
  }
])

// 是否有逾期任务
const hasOverdueTasks = computed(() => {
  return tableData.value.some(task => task.isOverdue)
})

// 工具函数
const getStatusColor = (status: string) => {
  const colors = {
    pending: 'info',
    assigned: 'primary',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '待分配',
    assigned: '已分配',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getPriorityColor = (priority: string) => {
  const colors = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return colors[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts = {
    low: '普通',
    medium: '中等',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || '未知'
}

const getProgressStatus = (task: BackgroundCheckTask) => {
  if (task.isOverdue) return 'exception'
  if (task.progress.percentage === 100) return 'success'
  return undefined
}

const formatDate = (dateStr: string) => {
  return dateStr.split(' ')[0]
}

// 事件处理
const handleStatClick = (key: string) => {
  if (key !== 'total') {
    searchForm.status = key === 'overdue' ? '' : key
    if (key === 'overdue') {
      searchForm.isOverdue = true
    } else {
      searchForm.isOverdue = undefined
    }
    handleSearch()
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    status: '',
    assignedToOrg: '',
    priority: '',
    createTimeRange: [],
    isOverdue: undefined
  })
  pagination.page = 1
  fetchData()
}

const handleSelectionChange = (selection: BackgroundCheckTask[]) => {
  selectedTasks.value = selection
}

const handleViewDetail = (task: BackgroundCheckTask) => {
  selectedTaskId.value = task.id
  taskDetailVisible.value = true
}

const handleRemind = (task: BackgroundCheckTask) => {
  selectedTaskId.value = task.id
  reminderDialogVisible.value = true
}

const handleBatchRemind = async () => {
  const overdueTasks = tableData.value.filter(task => task.isOverdue)
  if (overdueTasks.length === 0) {
    ElMessage.warning('没有逾期任务需要催办')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要对 ${overdueTasks.length} 个逾期任务发送催办通知吗？`,
      '批量催办',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success(`已对 ${overdueTasks.length} 个任务发送催办通知`)
  } catch (error) {
    // 用户取消
  }
}

const handleExport = () => {
  ElMessage.success('任务列表导出成功')
}

const handleReminderSubmit = (reminderData: any) => {
  ElMessage.success('催办通知发送成功')
  reminderDialogVisible.value = false
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 数据获取
const fetchData = async () => {
  try {
    loading.value = true
    
    // 使用Mock数据
    let filteredData = [...backgroundCheckTasksMockData]
    
    // 应用筛选条件
    if (searchForm.status) {
      filteredData = filteredData.filter(task => task.status === searchForm.status)
    }
    
    if (searchForm.assignedToOrg) {
      filteredData = filteredData.filter(task => task.assignedToOrg === searchForm.assignedToOrg)
    }
    
    if (searchForm.priority) {
      filteredData = filteredData.filter(task => task.priority === searchForm.priority)
    }
    
    if (searchForm.isOverdue !== undefined) {
      filteredData = filteredData.filter(task => task.isOverdue === searchForm.isOverdue)
    }
    
    // 更新逾期状态
    filteredData = filteredData.map(task => ({
      ...task,
      ...calculateOverdue(task.dueDate)
    }))
    
    pagination.total = filteredData.length
    
    // 分页
    const start = (pagination.page - 1) * pagination.size
    const end = start + pagination.size
    tableData.value = filteredData.slice(start, end)
    
  } catch (error) {
    console.error('获取任务数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.background-check-task {
  padding: 10px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.task-statistics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
  border-left: 4px solid #409eff;
}

.stat-card.success {
  border-left: 4px solid #67c23a;
}

.stat-card.danger {
  border-left: 4px solid #f56c6c;
}

.stat-card.info {
  border-left: 4px solid #909399;
}

.stat-content {
  text-align: center;
  padding: 8px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.overdue-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #f56c6c;
  font-size: 12px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-badge {
  font-size: 10px;
  color: #f56c6c;
  background: #fef0f0;
  padding: 1px 4px;
  border-radius: 2px;
  margin-top: 2px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .task-statistics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
