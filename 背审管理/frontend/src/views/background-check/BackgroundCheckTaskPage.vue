<!-- 背审任务管理页面 -->
<template>
  <div class="background-check-task">
    <!-- 搜索表单 -->
    <div class="search-section">
      <SearchForm
        :form-config="searchFormConfig"
        :model-value="searchForm"
        :custom-buttons="searchCustomButtons"
        @search="handleSearch"
        @reset="handleReset"
        @custom-button-click="handleCustomButtonClick"
      />
    </div>

    <!-- 表格标题行 -->
    <div class="table-header">
      <div class="table-info">
        <span class="status-info">背审任务：{{ pagination.total }}个</span>
        <span v-if="selectedTaskIds.size > 0" class="selected-info">
          已选择：{{ selectedTaskIds.size }}个
          <el-button
            type="text"
            size="small"
            @click="clearAllSelection"
            class="clear-selection-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </span>
      </div>
      <div class="table-actions">
        <el-button
          v-if="selectedTaskIds.size > 0"
          type="danger"
          @click="handleBatchCancel"
        >
          批量撤销
        </el-button>
        <el-button
          type="primary"
          @click="handleBatchRemind"
          :disabled="!hasOverdueTasks"
        >
          <el-icon><Bell /></el-icon>
          批量催办
        </el-button>
      </div>
    </div>

    <!-- 任务列表表格 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        row-key="id"
        :border="true"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="taskNo" label="任务编号" width="140" />
        <el-table-column prop="title" label="任务标题" min-width="150" />
        <el-table-column prop="personnelCount" label="人员数量" width="100" align="center">
          <template #default="scope">
            {{ scope.row.personnelCount }}人
          </template>
        </el-table-column>
        <el-table-column prop="assignedToUserName" label="处理人" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="进度" width="150">
          <template #default="scope">
            <div class="progress-container">
              <el-progress
                :percentage="scope.row.progress.percentage"
                :stroke-width="8"
                :show-text="false"
                :status="getProgressStatus(scope.row)"
              />
              <span class="progress-text">
                {{ scope.row.progress.completed }}/{{ scope.row.progress.total }}
                ({{ scope.row.progress.percentage }}%)
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reminderCount" label="催办次数" width="100" align="center">
          <template #default="scope">
            <span :class="{ 'reminder-count': scope.row.reminderCount > 0 }">
              {{ scope.row.reminderCount || 0 }}次
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="截止时间" width="120">
          <template #default="scope">
            <div :class="{ 'overdue-text': scope.row.isOverdue }">
              {{ formatDate(scope.row.dueDate) }}
              <div v-if="scope.row.isOverdue" class="overdue-badge">
                逾期{{ scope.row.overdueBy }}天
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.status !== 'cancelled' && scope.row.status !== 'completed'"
              type="danger"
              size="small"
              text
              @click="handleCancelTask(scope.row)"
            >
              撤销
            </el-button>
            <el-button
              v-if="scope.row.isOverdue"
              type="warning"
              size="small"
              text
              @click="handleRemind(scope.row)"
            >
              催办
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 任务详情弹窗 -->
    <TaskDetailDialog
      v-model="taskDetailVisible"
      :task-id="selectedTaskId"
    />

    <!-- 催办弹窗 -->
    <ReminderDialog
      v-model="reminderDialogVisible"
      :task-id="selectedTaskId"
      @submit="handleReminderSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Bell, Download, Close } from '@element-plus/icons-vue'
import SearchForm from '@/components/common/SearchForm.vue'
import TaskDetailDialog from '@/components/background-check/TaskDetailDialog.vue'
import ReminderDialog from '@/components/background-check/ReminderDialog.vue'
import { backgroundCheckTasksMockData, calculateOverdue } from '@/data/backgroundCheckMockData'
import type { BackgroundCheckTask } from '@/data/backgroundCheckMockData'

// 响应式数据
const loading = ref(false)
const tableData = ref<BackgroundCheckTask[]>([])
const selectedTasks = ref<BackgroundCheckTask[]>([])
const selectedTaskIds = ref<Set<string>>(new Set()) // 用于跨页面保持选择状态
const tableRef = ref() // 表格引用

// 弹窗控制
const taskDetailVisible = ref(false)
const reminderDialogVisible = ref(false)
const cancelTaskDialogVisible = ref(false)
const selectedTaskId = ref<string>()
const cancelTaskIds = ref<string[]>([])
const isBatchCancel = ref(false)

// 搜索表单
const searchForm = reactive({
  status: '',
  title: '',
  assignedToUser: '',
  dueDate: ''
})

// 搜索表单配置 - 一行4个字段
const searchFormConfig = computed(() => [
  [
    {
      key: 'status',
      type: 'select' as const,
      prop: 'status',
      label: '任务状态',
      placeholder: '请选择状态',
      options: [
        { label: '未完成', value: 'incomplete' },
        { label: '已完成', value: 'completed' }
      ]
    },
    {
      key: 'title',
      type: 'input' as const,
      prop: 'title',
      label: '任务标题',
      placeholder: '请输入任务标题'
    },
    {
      key: 'assignedToUser',
      type: 'input' as const,
      prop: 'assignedToUser',
      label: '处理人',
      placeholder: '请输入处理人姓名'
    },
    {
      key: 'dueDate',
      type: 'date' as const,
      prop: 'dueDate',
      label: '截止时间',
      placeholder: '请选择截止时间'
    }
  ]
])

// 自定义按钮配置
const searchCustomButtons = computed(() => [
  {
    key: 'export',
    label: '导出Excel',
    type: 'default' as const,
    icon: Download
  }
])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// Mock数据
const departments = [
  { label: '莲池分局', value: 'org-001' },
  { label: '竞秀分局', value: 'org-002' },
  { label: '满城分局', value: 'org-003' },
  { label: '清苑分局', value: 'org-004' },
  { label: '徐水分局', value: 'org-005' }
]

// 任务统计数据
const taskStatistics = computed(() => [
  {
    key: 'total',
    label: '总任务',
    count: 89,
    type: 'primary'
  },
  {
    key: 'pending',
    label: '待分配',
    count: 12,
    type: 'info'
  },
  {
    key: 'in_progress',
    label: '进行中',
    count: 23,
    type: 'primary'
  },
  {
    key: 'completed',
    label: '已完成',
    count: 45,
    type: 'success'
  },
  {
    key: 'cancelled',
    label: '已取消',
    count: 6,
    type: 'info'
  },
  {
    key: 'overdue',
    label: '逾期任务',
    count: 3,
    type: 'danger',
    isOverdue: true
  }
])

// 是否有逾期任务
const hasOverdueTasks = computed(() => {
  return tableData.value.some(task => task.isOverdue)
})

// 工具函数
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'info',
    assigned: 'primary',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info',
    incomplete: 'warning'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待分配',
    assigned: '已分配',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消',
    incomplete: '未完成'
  }
  return texts[status] || '未知'
}

const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return colors[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '普通',
    medium: '中等',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || '未知'
}

const getProgressStatus = (task: BackgroundCheckTask) => {
  if (task.isOverdue) return 'exception'
  if (task.progress.percentage === 100) return 'success'
  return undefined
}

const formatDate = (dateStr: string) => {
  return dateStr.split(' ')[0]
}

// 事件处理
const handleSearch = (formData: any) => {
  Object.assign(searchForm, formData)
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    status: '',
    title: '',
    assignedToUser: '',
    dueDate: ''
  })
  pagination.page = 1
  fetchData()
}

// 自定义按钮点击处理
const handleCustomButtonClick = (button: any) => {
  switch (button.key) {
    case 'export':
      handleExport()
      break
  }
}

const handleSelectionChange = (selection: BackgroundCheckTask[]) => {
  // 更新当前页面的选择
  selectedTasks.value = selection

  // 更新全局选择状态
  // 先移除当前页面所有数据的ID
  tableData.value.forEach(item => {
    selectedTaskIds.value.delete(item.id)
  })

  // 添加当前选中的ID
  selection.forEach(item => {
    selectedTaskIds.value.add(item.id)
  })
}

// 恢复选择状态
const restoreSelection = () => {
  if (!tableRef.value) return

  // 清空当前选择
  tableRef.value.clearSelection()

  // 恢复之前选中的项
  tableData.value.forEach(row => {
    if (selectedTaskIds.value.has(row.id)) {
      tableRef.value?.toggleRowSelection(row, true)
    }
  })
}

// 清空所有选择
const clearAllSelection = () => {
  selectedTaskIds.value.clear()
  selectedTasks.value = []
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

const handleViewDetail = (task: BackgroundCheckTask) => {
  selectedTaskId.value = task.id
  taskDetailVisible.value = true
}

// 撤销单个任务
const handleCancelTask = async (task: BackgroundCheckTask) => {
  try {
    await ElMessageBox.prompt(
      `确定要撤销任务"${task.title}"吗？请输入撤销原因：`,
      '撤销任务',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入撤销原因',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '请输入撤销原因'
          }
          return true
        }
      }
    )

    // 这里调用API撤销任务
    console.log('撤销任务:', task.id)
    ElMessage.success('任务撤销成功')
    fetchData()
  } catch (error) {
    // 用户取消
  }
}

// 批量撤销任务
const handleBatchCancel = async () => {
  const selectedIds = Array.from(selectedTaskIds.value)
  if (selectedIds.length === 0) {
    ElMessage.warning('请先选择要撤销的任务')
    return
  }

  try {
    await ElMessageBox.prompt(
      `确定要撤销选中的 ${selectedIds.length} 个任务吗？请输入撤销原因：`,
      '批量撤销任务',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入撤销原因',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '请输入撤销原因'
          }
          return true
        }
      }
    )

    // 这里调用API批量撤销任务
    console.log('批量撤销任务:', selectedIds)
    ElMessage.success(`成功撤销 ${selectedIds.length} 个任务`)
    clearAllSelection()
    fetchData()
  } catch (error) {
    // 用户取消
  }
}

const handleRemind = (task: BackgroundCheckTask) => {
  selectedTaskId.value = task.id
  reminderDialogVisible.value = true
}

const handleBatchRemind = async () => {
  const overdueTasks = tableData.value.filter(task => task.isOverdue)
  if (overdueTasks.length === 0) {
    ElMessage.warning('没有逾期任务需要催办')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要对 ${overdueTasks.length} 个逾期任务发送催办通知吗？`,
      '批量催办',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success(`已对 ${overdueTasks.length} 个任务发送催办通知`)
  } catch (error) {
    // 用户取消
  }
}

const handleExport = () => {
  ElMessage.success('任务列表导出成功')
}

const handleReminderSubmit = (_reminderData: any) => {
  ElMessage.success('催办通知发送成功')
  reminderDialogVisible.value = false
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 数据获取
const fetchData = async () => {
  try {
    loading.value = true
    
    // 使用Mock数据
    let filteredData = [...backgroundCheckTasksMockData]
    
    // 应用筛选条件
    if (searchForm.status) {
      if (searchForm.status === 'incomplete') {
        filteredData = filteredData.filter(task => task.status !== 'completed')
      } else {
        filteredData = filteredData.filter(task => task.status === searchForm.status)
      }
    }

    if (searchForm.title) {
      filteredData = filteredData.filter(task =>
        task.title.toLowerCase().includes(searchForm.title.toLowerCase())
      )
    }

    if (searchForm.assignedToUser) {
      filteredData = filteredData.filter(task =>
        task.assignedToUserName?.toLowerCase().includes(searchForm.assignedToUser.toLowerCase())
      )
    }

    if (searchForm.dueDate) {
      filteredData = filteredData.filter(task =>
        task.dueDate.startsWith(searchForm.dueDate)
      )
    }
    
    // 更新逾期状态
    filteredData = filteredData.map(task => ({
      ...task,
      ...calculateOverdue(task.dueDate)
    }))
    
    pagination.total = filteredData.length
    
    // 分页
    const start = (pagination.page - 1) * pagination.size
    const end = start + pagination.size
    tableData.value = filteredData.slice(start, end)

    // 恢复选择状态
    await nextTick()
    restoreSelection()

  } catch (error) {
    console.error('获取任务数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.background-check-task {
  padding: 10px 30px 30px 30px;
  background-color: #ffffff;
}

.search-section {
  margin-bottom: 10px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 10px;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-info {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.selected-info {
  font-size: 14px;
  color: #409eff;
  background: #ecf5ff;
  padding: 4px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-selection-btn {
  padding: 0 !important;
  margin-left: 4px;
  color: #909399;
  font-size: 12px;
  min-height: auto;
}

.clear-selection-btn:hover {
  color: #f56c6c;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.table-section {
  margin: 0 10px;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-badge {
  font-size: 10px;
  color: #f56c6c;
  background: #fef0f0;
  padding: 1px 4px;
  border-radius: 2px;
  margin-top: 2px;
}

.reminder-count {
  color: #f56c6c;
  font-weight: 600;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}


/* 覆盖组件样式 */
:deep(.search-card) {
  /* 自定义样式 */
  border: none;
  box-shadow: none;

}

:deep(.el-card__body) {
  padding: 5px !important;
}
:deep(.search-header) {
  margin-bottom: 10px;
}
:deep(.search-card) {
  margin-bottom: 10px;
  /* border-bottom: 1px solid #e4e7ed; */
}
</style>
