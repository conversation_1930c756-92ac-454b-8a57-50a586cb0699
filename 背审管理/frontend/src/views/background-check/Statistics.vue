<template>
  <div class="statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>数据统计分析</h2>
      <p>背景审查数据的统计分析和可视化展示</p>
    </div>

    <!-- 时间筛选 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-container">
        <el-form :model="filterForm" :inline="true" class="filter-form">
          <el-form-item label="统计时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-form-item>
          <el-form-item label="快速选择">
            <el-button-group>
              <el-button @click="setQuickDate('today')" :type="quickDateType === 'today' ? 'primary' : 'default'">
                今日
              </el-button>
              <el-button @click="setQuickDate('week')" :type="quickDateType === 'week' ? 'primary' : 'default'">
                本周
              </el-button>
              <el-button @click="setQuickDate('month')" :type="quickDateType === 'month' ? 'primary' : 'default'">
                本月
              </el-button>
              <el-button @click="setQuickDate('year')" :type="quickDateType === 'year' ? 'primary' : 'default'">
                本年
              </el-button>
            </el-button-group>
          </el-form-item>
        </el-form>
      </div>
      <div class="filter-actions-wrapper">
        <div class="filter-actions">
          <el-button type="primary" @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 概览统计卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ overviewData.totalPersonnel }}</div>
                <div class="stat-label">总人员数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon normal">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ overviewData.normalPersonnel }}</div>
                <div class="stat-label">正常人员</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ overviewData.riskPersonnel }}</div>
                <div class="stat-label">风险人员</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon danger">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ overviewData.blacklistPersonnel }}</div>
                <div class="stat-label">黑名单人员</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <!-- 人员类型分布 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>人员类型分布</span>
              <el-button type="text" @click="exportChart('personnelType')">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </template>
          <div ref="personnelTypeChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 风险等级分布 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>风险等级分布</span>
              <el-button type="text" @click="exportChart('riskLevel')">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </template>
          <div ref="riskLevelChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <!-- 区域分布 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>区域分布统计</span>
              <el-button type="text" @click="exportChart('region')">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </template>
          <div ref="regionChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 时间趋势 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>人员增量趋势</span>
              <el-button type="text" @click="exportChart('trend')">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </template>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 单位异常统计表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>各单位异常人员统计</span>
          <div class="header-actions">
            <el-button type="primary" @click="exportTable">
              <el-icon><Download /></el-icon>
              导出表格
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="organizationData" :loading="loading" stripe>
        <el-table-column prop="organization" label="单位名称" min-width="200" />
        <el-table-column prop="totalCount" label="总人数" width="100" align="center" />
        <el-table-column prop="normalCount" label="正常人员" width="100" align="center" />
        <el-table-column prop="riskCount" label="风险人员" width="100" align="center">
          <template #default="{ row }">
            <span :class="{ 'risk-text': row.riskCount > 0 }">{{ row.riskCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="blacklistCount" label="黑名单人员" width="120" align="center">
          <template #default="{ row }">
            <span :class="{ 'danger-text': row.blacklistCount > 0 }">{{ row.blacklistCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="riskRate" label="风险率" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getRiskRateType(row.riskRate)">
              {{ (row.riskRate * 100).toFixed(1) }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewOrganizationDetail(row.organization)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download, User, Check, Warning, Close } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { mockApi } from '@/utils/mockData'
import type { StatisticsQuery, StatisticsOverview, OrganizationStatistics } from '@/types/background-check'

// 模拟API函数
const getStatisticsData = (params: StatisticsQuery) => {
  return mockApi.getStatisticsOverview(params)
}

const exportStatisticsData = (params: any) => {
  return mockApi.exportData(params)
}

// 响应式数据
const loading = ref(false)
const quickDateType = ref('month')
const personnelTypeChart = ref()
const riskLevelChart = ref()
const regionChart = ref()
const trendChart = ref()

// 图表数据
const personnelTypeData = ref([
  { name: '有编制人员', value: 0 },
  { name: '无编制人员', value: 0 },
  { name: '外包人员', value: 0 }
])

const riskLevelData = ref([
  { name: '低风险', value: 0 },
  { name: '中风险', value: 0 },
  { name: '高风险', value: 0 }
])

const regionData = ref([
  { name: '东城区', value: 0 },
  { name: '西城区', value: 0 },
  { name: '朝阳区', value: 0 },
  { name: '海淀区', value: 0 }
])

// 筛选表单
const filterForm = reactive({
  dateRange: [] as string[]
})

// 概览数据
const overviewData = reactive({
  totalPersonnel: 0,
  normalPersonnel: 0,
  riskPersonnel: 0,
  blacklistPersonnel: 0
})

// 单位数据
const organizationData = ref([])

// 图表实例
let personnelTypeChartInstance: echarts.ECharts | null = null
let riskLevelChartInstance: echarts.ECharts | null = null
let regionChartInstance: echarts.ECharts | null = null
let trendChartInstance: echarts.ECharts | null = null

// 设置快速日期
const setQuickDate = (type: string) => {
  quickDateType.value = type
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const date = today.getDate()
  
  switch (type) {
    case 'today':
      filterForm.dateRange = [
        today.toISOString().split('T')[0],
        today.toISOString().split('T')[0]
      ]
      break
    case 'week':
      const weekStart = new Date(today.setDate(date - today.getDay()))
      const weekEnd = new Date(today.setDate(date - today.getDay() + 6))
      filterForm.dateRange = [
        weekStart.toISOString().split('T')[0],
        weekEnd.toISOString().split('T')[0]
      ]
      break
    case 'month':
      filterForm.dateRange = [
        new Date(year, month, 1).toISOString().split('T')[0],
        new Date(year, month + 1, 0).toISOString().split('T')[0]
      ]
      break
    case 'year':
      filterForm.dateRange = [
        new Date(year, 0, 1).toISOString().split('T')[0],
        new Date(year, 11, 31).toISOString().split('T')[0]
      ]
      break
  }
  
  fetchStatisticsData()
}

// 日期改变
const handleDateChange = () => {
  quickDateType.value = ''
  fetchStatisticsData()
}

// 刷新数据
const handleRefresh = () => {
  fetchStatisticsData()
}

// 获取统计数据
const fetchStatisticsData = async () => {
  try {
    loading.value = true
    const params = {
      startDate: filterForm.dateRange?.[0] || '',
      endDate: filterForm.dateRange?.[1] || ''
    }
    
    const response = await getStatisticsData(params)
    const data = response.data
    
    // 更新概览数据
    Object.assign(overviewData, data.overview)
    
    // 更新单位数据
    organizationData.value = data.organizations
    
    // 更新图表
    await nextTick()
    updateCharts(data.charts)
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 更新图表
const updateCharts = (chartData: any) => {
  // 人员类型分布饼图
  if (personnelTypeChart.value && !personnelTypeChartInstance) {
    personnelTypeChartInstance = echarts.init(personnelTypeChart.value)
  }
  if (personnelTypeChartInstance) {
    personnelTypeChartInstance.setOption({
      title: {
        text: '人员类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '人员类型',
          type: 'pie',
          radius: '50%',
          data: chartData.personnelType,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
  
  // 风险等级分布柱状图
  if (riskLevelChart.value && !riskLevelChartInstance) {
    riskLevelChartInstance = echarts.init(riskLevelChart.value)
  }
  if (riskLevelChartInstance) {
    riskLevelChartInstance.setOption({
      title: {
        text: '风险等级分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['低风险', '中风险', '高风险']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '人数',
          type: 'bar',
          data: chartData.riskLevel,
          itemStyle: {
            color: function(params: any) {
              const colors = ['#67C23A', '#E6A23C', '#F56C6C']
              return colors[params.dataIndex]
            }
          }
        }
      ]
    })
  }
  
  // 区域分布柱状图
  if (regionChart.value && !regionChartInstance) {
    regionChartInstance = echarts.init(regionChart.value)
  }
  if (regionChartInstance) {
    regionChartInstance.setOption({
      title: {
        text: '区域分布统计',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: chartData.region.map((item: any) => item.name),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '人数',
          type: 'bar',
          data: chartData.region.map((item: any) => item.value),
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    })
  }
  
  // 时间趋势折线图
  if (trendChart.value && !trendChartInstance) {
    trendChartInstance = echarts.init(trendChart.value)
  }
  if (trendChartInstance) {
    trendChartInstance.setOption({
      title: {
        text: '人员增量趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新增人员', '风险人员', '黑名单人员'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: chartData.trend.dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新增人员',
          type: 'line',
          data: chartData.trend.newPersonnel,
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '风险人员',
          type: 'line',
          data: chartData.trend.riskPersonnel,
          itemStyle: { color: '#E6A23C' }
        },
        {
          name: '黑名单人员',
          type: 'line',
          data: chartData.trend.blacklistPersonnel,
          itemStyle: { color: '#F56C6C' }
        }
      ]
    })
  }
}

// 获取风险率类型
const getRiskRateType = (rate: number) => {
  if (rate === 0) return 'success'
  if (rate < 0.1) return 'warning'
  return 'danger'
}

// 查看单位详情
const viewOrganizationDetail = (organization: string) => {
  // 跳转到数据管理页面，并筛选该单位
  // 这里可以使用路由跳转
  console.log('查看单位详情:', organization)
}

// 导出图表
const exportChart = (chartType: string) => {
  let chartInstance: echarts.ECharts | null = null
  let fileName = ''
  
  switch (chartType) {
    case 'personnelType':
      chartInstance = personnelTypeChartInstance
      fileName = '人员类型分布图'
      break
    case 'riskLevel':
      chartInstance = riskLevelChartInstance
      fileName = '风险等级分布图'
      break
    case 'region':
      chartInstance = regionChartInstance
      fileName = '区域分布统计图'
      break
    case 'trend':
      chartInstance = trendChartInstance
      fileName = '人员增量趋势图'
      break
  }
  
  if (chartInstance) {
    const url = chartInstance.getDataURL({
      type: 'png',
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.href = url
    link.download = `${fileName}.png`
    link.click()
    
    ElMessage.success('图表导出成功')
  }
}

// 导出表格
const exportTable = async () => {
  try {
    const params = {
      startDate: filterForm.dateRange?.[0] || '',
      endDate: filterForm.dateRange?.[1] || '',
      type: 'organization'
    }
    
    await exportStatisticsData(params)
    ElMessage.success('表格导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 组件挂载时初始化
onMounted(() => {
  setQuickDate('month')
  
  // 监听窗口大小变化，重新调整图表大小
  window.addEventListener('resize', () => {
    personnelTypeChartInstance?.resize()
    riskLevelChartInstance?.resize()
    regionChartInstance?.resize()
    trendChartInstance?.resize()
  })
})
</script>

<style scoped>
.statistics {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.filter-form {
  width: 100%;
  margin-bottom: 0;
}

.filter-actions-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.filter-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
}

.overview-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.normal {
  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);
}

.stat-icon.danger {
  background: linear-gradient(135deg, #F56C6C 0%, #f78989 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  width: 100%;
  height: 320px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.risk-text {
  color: #E6A23C;
  font-weight: 600;
}

.danger-text {
  color: #F56C6C;
  font-weight: 600;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 16px;
}

:deep(.el-date-picker) {
  width: 240px;
}

:deep(.el-button-group .el-button) {
  margin-left: 0;
}
</style>