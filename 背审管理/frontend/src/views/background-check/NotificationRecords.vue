<template>
  <div class="notification-records">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>通知记录管理</h2>
      <p>管理所有发送给各单位的通知记录</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <h3 class="search-title">
          <el-icon><Search /></el-icon>
          搜索筛选
        </h3>
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleExport" :loading="exportLoading">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
      
      <div class="search-container">
        <el-form :model="searchForm" class="search-form" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="人员姓名">
                <el-input v-model="searchForm.personnelName" placeholder="请输入人员姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="接收单位">
                <el-input v-model="searchForm.organizationName" placeholder="请输入接收单位" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="通知类型">
                <el-select v-model="searchForm.notificationType" placeholder="请选择通知类型" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="建议辞退" :value="1" />
                  <el-option label="建议调岗" :value="2" />
                  <el-option label="建议加强监管" :value="3" />
                  <el-option label="其他" :value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="发送状态">
                <el-select v-model="searchForm.sendStatus" placeholder="请选择发送状态" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="待发送" :value="1" />
                  <el-option label="已发送" :value="2" />
                  <el-option label="发送失败" :value="3" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="阅读状态">
                <el-select v-model="searchForm.readStatus" placeholder="请选择阅读状态" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="未读" :value="1" />
                  <el-option label="已读" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发送时间">
                <el-date-picker
                  v-model="searchForm.sendTimeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <div class="table-info">
          <span>共 {{ pagination.total }} 条记录</span>
        </div>
        <div class="table-actions">
          <el-button type="danger" @click="handleBatchDelete" :disabled="selectedIds.length === 0">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>
      </div>

      <el-table 
        v-loading="loading"
        :data="tableData" 
        class="notification-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="personnelName" label="人员姓名" width="120" />
        <el-table-column prop="organizationName" label="接收单位" width="200" />
        <el-table-column prop="notificationType" label="通知类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getNotificationTypeColor(row.notificationType)" size="small">
              {{ getNotificationTypeText(row.notificationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notificationContent" label="通知内容" width="300">
          <template #default="{ row }">
            <div class="content-preview">
              {{ getContentPreview(row.notificationContent) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sendStatus" label="发送状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getSendStatusColor(row.sendStatus)" size="small">
              {{ getSendStatusText(row.sendStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="readStatus" label="阅读状态" width="100">
          <template #default="{ row }">
            <el-tag 
              v-if="row.sendStatus === 2" 
              :type="getReadStatusColor(row.readStatus)" 
              size="small"
            >
              {{ getReadStatusText(row.readStatus) }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="160">
          <template #default="{ row }">
            {{ row.sendTime ? formatDateTime(row.sendTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              查看详情
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 通知详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="通知详情" width="800px">
      <div v-if="selectedNotification" class="notification-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="人员姓名">{{ selectedNotification.personnelName }}</el-descriptions-item>
          <el-descriptions-item label="接收单位">{{ selectedNotification.organizationName }}</el-descriptions-item>
          <el-descriptions-item label="通知类型">
            <el-tag :type="getNotificationTypeColor(selectedNotification.notificationType)">
              {{ getNotificationTypeText(selectedNotification.notificationType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发送状态">
            <el-tag :type="getSendStatusColor(selectedNotification.sendStatus)">
              {{ getSendStatusText(selectedNotification.sendStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发送时间">{{ formatDateTime(selectedNotification.sendTime) }}</el-descriptions-item>
          <el-descriptions-item label="阅读状态" v-if="selectedNotification.sendStatus === 2">
            <el-tag :type="getReadStatusColor(selectedNotification.readStatus)">
              {{ getReadStatusText(selectedNotification.readStatus) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="content-section">
          <h4>通知内容</h4>
          <div class="content-text">{{ selectedNotification.notificationContent }}</div>
        </div>
        
        <div v-if="selectedNotification.response" class="response-section">
          <h4>回复内容</h4>
          <div class="response-text">{{ selectedNotification.response }}</div>
          <div class="response-time">回复时间：{{ formatDateTime(selectedNotification.responseTime) }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, Delete } from '@element-plus/icons-vue'
import { getAllNotificationRecords } from '@/api/background-check'
import { getNotificationTypeText } from '@/data/personnelMockData'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const selectedIds = ref<number[]>([])
const detailDialogVisible = ref(false)
const selectedNotification = ref<any>(null)

// 搜索表单
const searchForm = reactive({
  personnelName: '',
  organizationName: '',
  notificationType: '',
  sendStatus: '',
  readStatus: '',
  sendTimeRange: null as any
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 获取通知记录列表
const fetchNotificationRecords = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const response = await getAllNotificationRecords(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取通知记录失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchNotificationRecords()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    personnelName: '',
    organizationName: '',
    notificationType: '',
    sendStatus: '',
    readStatus: '',
    sendTimeRange: null
  })
  pagination.page = 1
  fetchNotificationRecords()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 查看详情
const handleViewDetail = (notification: any) => {
  selectedNotification.value = notification
  detailDialogVisible.value = true
}

// 删除记录
const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确认要删除这条通知记录吗？', '确认删除', {
      type: 'warning'
    })
    
    ElMessage.success('删除成功')
    fetchNotificationRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确认要删除选中的 ${selectedIds.value.length} 条通知记录吗？`, '确认删除', {
      type: 'warning'
    })
    
    ElMessage.success('批量删除成功')
    selectedIds.value = []
    fetchNotificationRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 状态相关方法
const getNotificationTypeColor = (type: number) => {
  const colorMap: Record<number, string> = {
    1: 'danger',
    2: 'warning',
    3: 'info',
    4: ''
  }
  return colorMap[type] || ''
}

const getSendStatusColor = (status: number) => {
  const colorMap: Record<number, string> = {
    1: 'info',
    2: 'success',
    3: 'danger'
  }
  return colorMap[status] || ''
}

const getSendStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    1: '待发送',
    2: '已发送',
    3: '发送失败'
  }
  return textMap[status] || '未知'
}

const getReadStatusColor = (status: number) => {
  return status === 2 ? 'success' : 'warning'
}

const getReadStatusText = (status: number) => {
  return status === 2 ? '已读' : '未读'
}

const getContentPreview = (content: string) => {
  return content.length > 50 ? content.substring(0, 50) + '...' : content
}

// 分页相关
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchNotificationRecords()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchNotificationRecords()
}

// 格式化时间
const formatDateTime = (timeStr?: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  fetchNotificationRecords()
})
</script>

<style scoped>
.notification-records {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.search-card,
.table-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.search-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-info {
  color: #606266;
  font-size: 14px;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.content-preview {
  line-height: 1.5;
  color: #606266;
}

.text-muted {
  color: #c0c4cc;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.notification-detail {
  max-height: 600px;
  overflow-y: auto;
}

.content-section,
.response-section {
  margin-top: 20px;
}

.content-section h4,
.response-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.content-text,
.response-text {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  line-height: 1.6;
  white-space: pre-wrap;
}

.response-time {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
