<template>
  <div class="home">
    <div class="welcome-container">
      <h1>欢迎使用系统</h1>
      <p>这是一个基于 Vue 3 + TypeScript + Element Plus 的前端框架</p>
      <div class="features">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <span>Vue 3</span>
            </div>
          </template>
          <p>使用最新的 Vue 3 Composition API</p>
        </el-card>
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <span>TypeScript</span>
            </div>
          </template>
          <p>完整的 TypeScript 支持</p>
        </el-card>
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <span>Element Plus</span>
            </div>
          </template>
          <p>丰富的 UI 组件库</p>
        </el-card>
      </div>
      <div class="actions">
        <el-button type="primary" @click="$router.push('/about')">了解更多</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页逻辑
</script>

<style scoped>
.home {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

.welcome-container {
  text-align: center;
  max-width: 800px;
  padding: 40px;
}

.welcome-container h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 20px;
}

.welcome-container p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 40px;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.feature-card {
  height: 150px;
}

.card-header {
  font-weight: bold;
  font-size: 1.1rem;
}

.actions {
  margin-top: 30px;
}
</style>
