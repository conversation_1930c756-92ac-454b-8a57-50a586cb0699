<template>
  <div class="test-page">
    <h1>系统测试页面</h1>
    <p>如果您能看到这个页面，说明系统基本运行正常。</p>
    
    <el-card>
      <h2>菜单结构重构完成</h2>
      <p>新的菜单结构包含以下模块：</p>
      <ul>
        <li>安保背审 - 4个子页面</li>
        <li>统计报表 - 3个子页面</li>
        <li>医疗机构背审 - 2个子页面</li>
        <li>中小幼背审 - 2个子页面</li>
        <li>寄递人员背审 - 1个子页面</li>
      </ul>
      
      <el-button type="primary" @click="goToDashboard">
        前往工作台
      </el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToDashboard = () => {
  router.push('/dashboard')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-page h1 {
  color: #409eff;
  margin-bottom: 20px;
}

.test-page ul {
  margin: 20px 0;
}

.test-page li {
  margin: 8px 0;
}
</style>
