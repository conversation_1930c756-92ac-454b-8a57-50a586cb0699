<template>
  <div class="about">
    <div class="about-container">
      <h1>关于系统</h1>
      <div class="about-content">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>技术栈</span>
            </div>
          </template>
          <div class="tech-stack">
            <div class="tech-item">
              <strong>前端框架:</strong> Vue 3
            </div>
            <div class="tech-item">
              <strong>开发语言:</strong> TypeScript
            </div>
            <div class="tech-item">
              <strong>UI 组件库:</strong> Element Plus
            </div>
            <div class="tech-item">
              <strong>构建工具:</strong> Vite
            </div>
            <div class="tech-item">
              <strong>路由管理:</strong> Vue Router
            </div>
            <div class="tech-item">
              <strong>状态管理:</strong> Pinia
            </div>
          </div>
        </el-card>
        
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>系统特性</span>
            </div>
          </template>
          <div class="features-list">
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>响应式设计</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>TypeScript 类型安全</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>组件化开发</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>现代化构建工具</span>
            </div>
          </div>
        </el-card>
      </div>
      
      <div class="actions">
        <el-button @click="$router.push('/')">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 关于页面逻辑
</script>

<style scoped>
.about {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 80vh;
  padding-top: 40px;
}

.about-container {
  max-width: 800px;
  width: 100%;
  padding: 20px;
}

.about-container h1 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 40px;
}

.about-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.info-card {
  height: auto;
}

.card-header {
  font-weight: bold;
  font-size: 1.1rem;
}

.tech-stack {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tech-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.tech-item:last-child {
  border-bottom: none;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.feature-item .el-icon {
  color: #67c23a;
}

.actions {
  text-align: center;
  margin-top: 30px;
}
</style>
