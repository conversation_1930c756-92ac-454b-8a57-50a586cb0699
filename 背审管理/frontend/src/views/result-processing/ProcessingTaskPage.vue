<!-- 处理任务管理页面 -->
<template>
  <div class="processing-task-page">
    <!-- 搜索表单 -->
    <div class="search-section">
      <SearchForm
        :form-config="searchFormConfig"
        :model-value="searchForm"
        :custom-buttons="searchCustomButtons"
        @search="handleSearch"
        @reset="handleReset"
        @custom-button-click="handleCustomButtonClick"
      />
    </div>

    <!-- 表格标题行 -->
    <div class="table-header">
      <div class="table-info">
        <span class="status-info">处理任务：{{ pagination.total }}个</span>
        <span v-if="selectedTaskIds.size > 0" class="selected-info">
          已选择：{{ selectedTaskIds.size }}个
          <el-button
            type="text"
            size="small"
            @click="clearAllSelection"
            class="clear-selection-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </span>
      </div>
      <div class="table-actions">
        <el-button
          v-if="selectedTaskIds.size > 0"
          type="danger"
          @click="handleBatchCancel"
        >
          批量撤销
        </el-button>
        <el-button
          type="primary"
          @click="handleBatchRemind"
          :disabled="!hasOverdueTasks"
        >
          <el-icon><Bell /></el-icon>
          批量催办
        </el-button>
      </div>
    </div>

    <!-- 任务列表表格 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        row-key="id"
        :border="true"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="taskNo" label="任务编号" width="140" />
        <el-table-column prop="title" label="任务标题" min-width="150" />
        <el-table-column prop="personnelCount" label="人员数量" width="100" align="center">
          <template #default="scope">
            {{ scope.row.personnelCount }}人
          </template>
        </el-table-column>
        <el-table-column prop="assignedToUserName" label="处理人" width="120" />
        <el-table-column prop="createdAt" label="创建时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="进度" width="150">
          <template #default="scope">
            <div class="progress-container">
              <el-progress
                :percentage="scope.row.progress.percentage"
                :stroke-width="8"
                :show-text="false"
                :status="getProgressStatus(scope.row)"
              />
              <span class="progress-text">
                {{ scope.row.progress.completed }}/{{ scope.row.progress.total }}
                ({{ scope.row.progress.percentage }}%)
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reminderCount" label="催办次数" width="100" align="center">
          <template #default="scope">
            <span :class="{ 'reminder-count': scope.row.reminderCount > 0 }">
              {{ scope.row.reminderCount || 0 }}次
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="截止时间" width="120">
          <template #default="scope">
            <div :class="{ 'overdue-text': scope.row.isOverdue }">
              {{ formatDate(scope.row.dueDate) }}
              <div v-if="scope.row.isOverdue" class="overdue-badge">
                逾期{{ scope.row.overdueBy }}天
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.status !== 'cancelled' && scope.row.status !== 'completed'"
              type="danger"
              size="small"
              text
              @click="handleCancelTask(scope.row)"
            >
              撤销
            </el-button>
            <el-button
              v-if="scope.row.isOverdue"
              type="warning"
              size="small"
              text
              @click="handleRemind(scope.row)"
            >
              催办
            </el-button>
            <el-button
              v-if="scope.row.status === 'completed'"
              type="success"
              size="small"
              text
              @click="handleReprocess(scope.row)"
            >
              再次处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 任务详情抽屉 -->
    <TaskDetailDrawer
      v-model="taskDetailVisible"
      :task-id="selectedTaskId"
      task-type="result-processing"
      :task-detail="currentTaskDetail"
      :history-list="currentHistoryList"
      :personnel-list="currentPersonnelList"
      :on-fetch-task-detail="fetchTaskDetail"
      :on-fetch-history="fetchProcessingHistory"
      :on-fetch-personnel="fetchPersonnelList"
    />

    <!-- 催办弹窗 -->
    <ReminderDialog
      v-model="reminderDialogVisible"
      :task-id="selectedTaskId"
      @submit="handleReminderSubmit"
    />

    <!-- 撤销任务弹窗 -->
    <CancelTaskDialog
      v-model="cancelTaskDialogVisible"
      :task-ids="cancelTaskIds"
      :is-batch="isBatchCancel"
      @submit="handleCancelSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Bell, Download, Close } from '@element-plus/icons-vue'
import SearchForm from '@/components/common/SearchForm.vue'
import TaskDetailDrawer from '@/components/common/TaskDetailDrawer.vue'
import ReminderDialog from '@/components/result-processing/ReminderDialog.vue'
import CancelTaskDialog from '@/components/result-processing/CancelTaskDialog.vue'
import {
  processingTaskMockData,
  calculateOverdue,
  type ProcessingTask
} from '@/data/resultProcessingMockData'
import {
  taskStatusConfig,
  getTaskStatusOptions,
  getPriorityOptions
} from '@/config/resultProcessingConfig'

// 响应式数据
const loading = ref(false)
const tableData = ref<ProcessingTask[]>([])
const selectedTasks = ref<ProcessingTask[]>([])
const selectedTaskIds = ref<Set<string>>(new Set()) // 用于跨页面保持选择状态
const tableRef = ref() // 表格引用

// 弹窗控制
const taskDetailVisible = ref(false)
const reminderDialogVisible = ref(false)
const cancelTaskDialogVisible = ref(false)
const selectedTaskId = ref<string>()
const cancelTaskIds = ref<string[]>([])
const isBatchCancel = ref(false)

// 抽屉数据
const currentTaskDetail = ref<any>({})
const currentHistoryList = ref<any[]>([])
const currentPersonnelList = ref<any[]>([])

// 搜索表单
const searchForm = reactive({
  status: '',
  title: '',
  assignedToUser: '',
  dueDate: '',
  priority: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 搜索表单配置
const searchFormConfig = computed(() => [
  [
    {
      key: 'status',
      type: 'select' as const,
      prop: 'status',
      label: '任务状态',
      placeholder: '请选择任务状态',
      options: getTaskStatusOptions()
    },
    {
      key: 'title',
      type: 'input' as const,
      prop: 'title',
      label: '任务标题',
      placeholder: '请输入任务标题'
    },
    {
      key: 'assignedToUser',
      type: 'input' as const,
      prop: 'assignedToUser',
      label: '处理人',
      placeholder: '请输入处理人'
    },
    {
      key: 'priority',
      type: 'select' as const,
      prop: 'priority',
      label: '优先级',
      placeholder: '请选择优先级',
      options: getPriorityOptions()
    }
  ]
])

// 自定义按钮
const searchCustomButtons = [
  {
    text: '导出',
    type: 'default' as const,
    icon: Download,
    event: 'export'
  }
]

// 计算属性
const hasOverdueTasks = computed(() => {
  return tableData.value.some(task => task.isOverdue)
})

// 工具函数
const formatDate = (dateStr: string | undefined) => {
  if (!dateStr) return '-'
  return dateStr.split(' ')[0]
}

const getStatusText = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.label || status
}

const getStatusColor = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.color || 'info'
}

const getProgressStatus = (task: ProcessingTask) => {
  if (task.isOverdue) return 'exception'
  if (task.progress.percentage === 100) return 'success'
  return undefined
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    let filteredData = [...processingTaskMockData]

    // 应用搜索条件
    if (searchForm.status) {
      filteredData = filteredData.filter(task => task.status === searchForm.status)
    }
    if (searchForm.title) {
      filteredData = filteredData.filter(task =>
        task.title.toLowerCase().includes(searchForm.title.toLowerCase())
      )
    }
    if (searchForm.assignedToUser) {
      filteredData = filteredData.filter(task =>
        task.assignedToUserName?.toLowerCase().includes(searchForm.assignedToUser.toLowerCase())
      )
    }
    if (searchForm.priority) {
      filteredData = filteredData.filter(task => task.priority === searchForm.priority)
    }

    // 更新逾期状态
    filteredData = filteredData.map(task => ({
      ...task,
      ...calculateOverdue(task.dueDate)
    }))

    tableData.value = filteredData
    pagination.total = filteredData.length

    // 恢复选择状态
    await nextTick()
    restoreSelection()

  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 恢复选择状态
const restoreSelection = () => {
  if (selectedTaskIds.value.size === 0) return

  const rowsToSelect = tableData.value.filter(task =>
    selectedTaskIds.value.has(task.id)
  )

  rowsToSelect.forEach(row => {
    tableRef.value?.toggleRowSelection(row, true)
  })
}

// 事件处理
const handleSearch = (formData: any) => {
  Object.assign(searchForm, formData)
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    status: '',
    title: '',
    assignedToUser: '',
    dueDate: '',
    priority: ''
  })
  pagination.page = 1
  loadData()
}

const handleCustomButtonClick = (event: string) => {
  if (event === 'export') {
    ElMessage.success('导出功能开发中...')
  }
}

// 选择相关
const handleSelectionChange = (selection: ProcessingTask[]) => {
  selectedTasks.value = selection

  // 更新跨页面选择状态
  const currentPageIds = new Set(tableData.value.map(task => task.id))

  // 移除当前页面的选择
  for (const id of selectedTaskIds.value) {
    if (currentPageIds.has(id)) {
      selectedTaskIds.value.delete(id)
    }
  }

  // 添加新的选择
  selection.forEach(task => {
    selectedTaskIds.value.add(task.id)
  })
}

const clearAllSelection = () => {
  selectedTaskIds.value.clear()
  tableRef.value?.clearSelection()
}

// 分页
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 任务操作
const handleViewDetail = (task: ProcessingTask) => {
  selectedTaskId.value = task.id
  taskDetailVisible.value = true
}

const handleCancelTask = (task: ProcessingTask) => {
  cancelTaskIds.value = [task.id]
  isBatchCancel.value = false
  cancelTaskDialogVisible.value = true
}

const handleBatchCancel = () => {
  if (selectedTaskIds.value.size === 0) {
    ElMessage.warning('请先选择要撤销的任务')
    return
  }

  cancelTaskIds.value = Array.from(selectedTaskIds.value)
  isBatchCancel.value = true
  cancelTaskDialogVisible.value = true
}

const handleRemind = (task: ProcessingTask) => {
  selectedTaskId.value = task.id
  reminderDialogVisible.value = true
}

const handleBatchRemind = () => {
  if (!hasOverdueTasks.value) {
    ElMessage.warning('当前没有逾期任务需要催办')
    return
  }

  ElMessageBox.confirm(
    '确定要对所有逾期任务进行催办吗？',
    '批量催办确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('批量催办成功！')
    loadData()
  }).catch(() => {
    // 用户取消
  })
}

const handleReprocess = (task: ProcessingTask) => {
  ElMessageBox.confirm(
    `确定要对任务"${task.title}"再次下发处理吗？`,
    '确认再次处理',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('任务已重新下发处理！')
    loadData()
  }).catch(() => {
    // 用户取消
  })
}

// 弹窗回调
const handleReminderSubmit = (data: any) => {
  ElMessage.success('催办成功！')
  reminderDialogVisible.value = false
  loadData()
}

const handleCancelSubmit = (data: any) => {
  ElMessage.success('任务撤销成功！')
  cancelTaskDialogVisible.value = false
  clearAllSelection()
  loadData()
}

const handleCancelTaskFromDrawer = (taskId: string) => {
  taskDetailVisible.value = false
  const task = tableData.value.find(t => t.id === taskId)
  if (task) {
    handleCancelTask(task)
  }
}

const handleRemindTaskFromDrawer = (taskId: string) => {
  taskDetailVisible.value = false
  const task = tableData.value.find(t => t.id === taskId)
  if (task) {
    handleRemind(task)
  }
}

// 抽屉数据获取函数
const fetchTaskDetail = async (taskId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    const task = processingTaskMockData.find(t => t.id === taskId)
    if (task) {
      currentTaskDetail.value = task
    }
    return task
  } catch (error) {
    ElMessage.error('获取任务详情失败')
    throw error
  }
}

const fetchProcessingHistory = async (taskId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    const history = [
      {
        time: '2024-01-15 09:00:00',
        type: 'create',
        title: '任务创建',
        content: '处理任务已创建，等待分配处理人员',
        operator: '张管理员'
      },
      {
        time: '2024-01-15 10:30:00',
        type: 'assign',
        title: '任务分配',
        content: '任务已分配给李处理员',
        operator: '张管理员'
      }
    ]
    currentHistoryList.value = history
    return history
  } catch (error) {
    ElMessage.error('获取处理历史失败')
    throw error
  }
}

const fetchPersonnelList = async (
  taskId: string,
  searchForm: any,
  pagination: any
) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 400))

    // 模拟人员数据
    let mockPersonnelList = [
      {
        id: '1',
        name: '张三',
        idCard: '110101199001011234',
        phone: '***********',
        organization: '技术部',
        abnormalType: 'education_fraud',
        processingStatus: 'completed',
        processingResult: 'focus'
      },
      {
        id: '2',
        name: '李四',
        idCard: '110101199002022345',
        phone: '***********',
        organization: '市场部',
        abnormalType: 'criminal_record',
        processingStatus: 'completed',
        processingResult: 'dismiss'
      },
      {
        id: '3',
        name: '王五',
        idCard: '110101199003033456',
        phone: '***********',
        organization: '财务部',
        abnormalType: 'credit_issue',
        processingStatus: 'pending'
      }
    ]

    // 应用搜索条件
    if (searchForm.name) {
      mockPersonnelList = mockPersonnelList.filter(p => p.name.includes(searchForm.name))
    }
    if (searchForm.idCard) {
      mockPersonnelList = mockPersonnelList.filter(p => p.idCard.includes(searchForm.idCard))
    }
    if (searchForm.status) {
      if (searchForm.status === 'incomplete') {
        mockPersonnelList = mockPersonnelList.filter(p => p.processingStatus === 'pending')
      } else if (searchForm.status === 'completed') {
        mockPersonnelList = mockPersonnelList.filter(p => p.processingStatus === 'completed')
      }
    }

    // 分页处理
    const start = (pagination.page - 1) * pagination.size
    const end = start + pagination.size
    const paginatedList = mockPersonnelList.slice(start, end)

    currentPersonnelList.value = paginatedList

    return {
      list: paginatedList,
      total: mockPersonnelList.length
    }
  } catch (error) {
    ElMessage.error('获取人员列表失败')
    throw error
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.processing-task-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.search-section {
  margin-bottom: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-info {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.selected-info {
  color: #409eff;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-selection-btn {
  padding: 0;
  margin-left: 4px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.reminder-count {
  color: #e6a23c;
  font-weight: 500;
}

.overdue-text {
  color: #f56c6c;
}

.overdue-badge {
  font-size: 12px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 2px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header) {
  background-color: #fafafa;
}

:deep(.el-table th) {
  background-color: #fafafa !important;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f5f7fa;
}

:deep(.el-table__row:hover > td) {
  background-color: #f5f7fa !important;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  background-color: #ebeef5;
}

:deep(.el-progress-bar__inner) {
  transition: width 0.3s ease;
}

/* 按钮样式 */
:deep(.el-button--text) {
  padding: 4px 8px;
  margin: 0 2px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .table-info {
    justify-content: center;
  }

  .table-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .processing-task-page {
    padding: 12px;
  }

  .table-header {
    padding: 12px;
  }

  .status-info {
    font-size: 14px;
  }
}
</style>
