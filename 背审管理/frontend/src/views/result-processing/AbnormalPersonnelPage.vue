<!-- 异常人员管理页面 -->
<template>
  <div class="abnormal-personnel">
    <!-- 状态切换Tab -->
    <div class="tabs-section">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="待处理" name="pending" />
        <el-tab-pane label="已处理" name="processed" />
      </el-tabs>
    </div>

    <!-- 筛选条件区域 -->
    <div class="search-section">
      <SearchForm
        v-model="searchForm"
        :form-config="searchFormConfig"
        :custom-buttons="searchCustomButtons"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        @custom-button-click="handleCustomButtonClick"
      />
    </div>

    <!-- 表格标题行 -->
    <div class="table-header">
      <div class="table-info">
        <span class="status-info">{{ getStatusText(activeTab) }}：{{ pagination.total }}人</span>
        <span v-if="selectedPersonnelIdCards.size > 0" class="selected-info">
          已选择：{{ selectedPersonnelIdCards.size }}人
          <el-button
            type="text"
            size="small"
            @click="clearAllSelection"
            class="clear-selection-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </span>
      </div>
      <div class="table-actions">
        <el-button v-if="activeTab === 'pending'" type="primary" @click="handleStartProcessing">
          下发处理任务
        </el-button>
        <el-button v-if="activeTab === 'processed'" type="success" @click="handleReprocess">
          再次处理
        </el-button>
      </div>
    </div>

    <!-- 人员列表表格 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        @selection-change="handleSelectionChange"
        row-key="idCard"
        :border="true"
      >
        <!-- 动态渲染表格列 -->
        <template v-for="column in currentTableColumns" :key="column.prop || column.label">
          <!-- 选择列 -->
          <el-table-column v-if="column.type === 'selection'" type="selection" :width="column.width" />

          <!-- 头像列 -->
          <el-table-column v-else-if="column.slot === 'avatar'" :prop="column.prop" :label="column.label"
            :width="column.width" :min-width="column.minWidth" :fixed="column.fixed">
            <template #default="scope">
              <PersonnelAvatar
                :src="scope.row.avatar"
                :name="scope.row.name"
                :personnel-id="scope.row.id"
                :size="55"
                @click="handleAvatarClick"
              />
            </template>
          </el-table-column>

          <!-- 身份证号列（脱敏显示） -->
          <el-table-column v-else-if="column.slot === 'idCard'" :prop="column.prop" :label="column.label"
            :width="column.width" :min-width="column.minWidth" :fixed="column.fixed">
            <template #default="scope">
              {{ maskIdCard(scope.row.idCard) }}
            </template>
          </el-table-column>

          <!-- 异常类型列 -->
          <el-table-column v-else-if="column.slot === 'abnormalType'" :prop="column.prop" :label="column.label"
            :width="column.width" :min-width="column.minWidth" :fixed="column.fixed">
            <template #default="scope">
              <el-tag :type="getAbnormalTypeColor(scope.row.abnormalType)">
                {{ getAbnormalTypeLabel(scope.row.abnormalType) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 异常等级列 -->
          <el-table-column v-else-if="column.slot === 'abnormalLevel'" :prop="column.prop" :label="column.label"
            :width="column.width" :min-width="column.minWidth" :fixed="column.fixed">
            <template #default="scope">
              <el-tag :type="getAbnormalLevelColor(scope.row.abnormalLevel)">
                {{ getAbnormalLevelLabel(scope.row.abnormalLevel) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 发现时间列 -->
          <el-table-column v-else-if="column.slot === 'discoveredAt'" :prop="column.prop" :label="column.label"
            :width="column.width" :min-width="column.minWidth" :fixed="column.fixed">
            <template #default="scope">
              {{ formatDate(scope.row.discoveredAt) }}
            </template>
          </el-table-column>

          <!-- 处理状态列 -->
          <el-table-column v-else-if="column.slot === 'processingStatus'" :prop="column.prop" :label="column.label"
            :width="column.width" :min-width="column.minWidth" :fixed="column.fixed">
            <template #default="scope">
              <el-tag :type="getProcessingStatusColor(scope.row.processingStatus)">
                {{ getProcessingStatusLabel(scope.row.processingStatus) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column v-else-if="column.slot === 'action'" :label="column.label" :width="column.width"
            :min-width="column.minWidth" :fixed="column.fixed">
            <template #default="scope">
              <el-button type="primary" size="" text @click="handleViewDetail(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>

          <!-- 普通列 -->
          <el-table-column v-else :prop="column.prop" :label="column.label" :width="column.width"
            :min-width="column.minWidth" :fixed="column.fixed" />
        </template>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 人员详情抽屉 -->
    <UnifiedPersonnelDetailDrawer
      v-model="detailDrawerVisible"
      :personnel-id="selectedPersonnelId"
      mode="result-processing"
      @view-task="handleViewTask"
    />

    <!-- 发起处理任务弹窗 -->
    <StartProcessingTaskDialog
      v-model="startTaskDialogVisible"
      :selected-count="selectedPersonnelIdCards.size"
      :total-count="pagination.total"
      :selected-personnel="selectedPersonnelNames"
      @submit="handleStartTask"
    />

    <!-- 头像预览对话框 -->
    <el-dialog v-model="avatarPreviewVisible" :title="`${avatarPreviewData.name} - 头像预览`" width="400px" center>
      <div class="avatar-preview">
        <el-image :src="avatarPreviewData.src" :alt="avatarPreviewData.name" fit="contain"
          style="width: 100%; max-height: 300px;">
          <template #error>
            <div class="image-error">
              <el-icon size="50">
                <User />
              </el-icon>
              <p>图片加载失败</p>
            </div>
          </template>
        </el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, User, Close } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import SearchForm from '@/components/common/SearchForm.vue'
import PersonnelAvatar from '@/components/common/PersonnelAvatar.vue'
import UnifiedPersonnelDetailDrawer from '@/components/common/UnifiedPersonnelDetailDrawer.vue'
import StartProcessingTaskDialog from '@/components/result-processing/StartProcessingTaskDialog.vue'
import {
  abnormalPersonnelMockData,
  type AbnormalPersonnel
} from '@/data/resultProcessingMockData'
import {
  abnormalTypeConfig,
  abnormalLevelConfig,
  processingStatusConfig,
  getAbnormalTypeOptions,
  getProcessingStatusOptions
} from '@/config/resultProcessingConfig'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeTab = ref<'pending' | 'processed'>('pending')
const tableData = ref<AbnormalPersonnel[]>([])
const selectedPersonnel = ref<AbnormalPersonnel[]>([])
const selectedPersonnelIdCards = ref<Set<string>>(new Set()) // 用于跨页面保持选择状态（基于身份证号）
const tableRef = ref() // 表格引用

// 弹窗控制
const detailDrawerVisible = ref(false)
const startTaskDialogVisible = ref(false)
const selectedPersonnelId = ref<string>()

// 头像预览
const avatarPreviewVisible = ref(false)
const avatarPreviewData = ref({
  src: '',
  name: '',
  personnelId: ''
})

// 搜索表单
const searchForm = ref({
  name: '',
  idCard: '',
  organization: '',
  abnormalType: '',
  processingStatus: '',
  discoveredTimeRange: [] as string[]
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表格列配置
const baseTableColumns = [
  { type: 'selection', width: 55 },
  { slot: 'avatar', prop: 'avatar', label: '照片', width: 80 },
  { prop: 'name', label: '姓名', width: 120 },
  { slot: 'idCard', prop: 'idCard', label: '身份证号', width: 180 },
  { prop: 'phone', label: '联系方式', width: 140 },
  { prop: 'organization', label: '所属机构', width: 150 },
  { slot: 'abnormalType', prop: 'abnormalType', label: '异常类型', width: 120 },
  { slot: 'abnormalLevel', prop: 'abnormalLevel', label: '异常等级', width: 100 },
  { slot: 'discoveredAt', prop: 'discoveredAt', label: '发现时间', width: 120 },
  { slot: 'processingStatus', prop: 'processingStatus', label: '处理状态', width: 100 },
  { prop: 'assignedToName', label: '处理人', width: 120 },
  { slot: 'action', label: '操作', width: 120, fixed: 'right' }
]

// 当前表格列配置
const currentTableColumns = computed(() => baseTableColumns)

// 搜索表单配置
const searchFormConfig = computed(() => [
  [
    {
      key: 'name',
      type: 'input' as const,
      prop: 'name',
      label: '姓名',
      placeholder: '请输入姓名'
    },
    {
      key: 'abnormalType',
      type: 'select' as const,
      prop: 'abnormalType',
      label: '异常类型',
      options: getAbnormalTypeOptions()
    },
    {
      key: 'processingStatus',
      type: 'select' as const,
      prop: 'processingStatus',
      label: '处理状态',
      options: getProcessingStatusOptions()
    },
    {
      key: 'discoveredTimeRange',
      type: 'daterange' as const,
      prop: 'discoveredTimeRange',
      label: '发现时间'
    }
  ]
])

// 自定义按钮
const searchCustomButtons = [
  {
    text: '导出',
    type: 'default' as const,
    icon: Download,
    event: 'export'
  }
]

// 计算属性
const selectedPersonnelNames = computed(() => {
  return abnormalPersonnelMockData
    .filter(p => selectedPersonnelIdCards.value.has(p.idCard))
    .map(p => p.name)
})

// 工具函数
const maskIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const formatDate = (dateStr: string | undefined) => {
  if (!dateStr) return '-'
  return dateStr.split(' ')[0]
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '待处理',
    'processed': '已处理'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getAbnormalTypeLabel = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.label || type
}

const getAbnormalTypeColor = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.color || 'info'
}

const getAbnormalLevelLabel = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.label || level
}

const getAbnormalLevelColor = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.color || 'info'
}

const getProcessingStatusLabel = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.label || status
}

const getProcessingStatusColor = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.color || 'info'
}

// 事件处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName as 'pending' | 'processed'
  pagination.page = 1
  loadData()
}

const handleSearch = (formData: any) => {
  Object.assign(searchForm.value, formData)
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm.value, {
    name: '',
    idCard: '',
    organization: '',
    abnormalType: '',
    processingStatus: '',
    discoveredTimeRange: []
  })
  pagination.page = 1
  loadData()
}

const handleCustomButtonClick = (event: string) => {
  if (event === 'export') {
    ElMessage.success('导出功能开发中...')
  }
}

const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    let filteredData = [...abnormalPersonnelMockData]

    // 根据标签页筛选
    if (activeTab.value === 'pending') {
      filteredData = filteredData.filter(p =>
        ['pending', 'assigned', 'processing'].includes(p.processingStatus)
      )
    } else {
      filteredData = filteredData.filter(p =>
        ['completed', 'rejected'].includes(p.processingStatus)
      )
    }

    // 应用搜索条件
    if (searchForm.value.name) {
      filteredData = filteredData.filter(p =>
        p.name.includes(searchForm.value.name)
      )
    }
    if (searchForm.value.abnormalType) {
      filteredData = filteredData.filter(p =>
        p.abnormalType === searchForm.value.abnormalType
      )
    }
    if (searchForm.value.processingStatus) {
      filteredData = filteredData.filter(p =>
        p.processingStatus === searchForm.value.processingStatus
      )
    }

    tableData.value = filteredData
    pagination.total = filteredData.length

    // 恢复选择状态
    await nextTick()
    restoreSelection()

  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 恢复选择状态
const restoreSelection = () => {
  if (selectedPersonnelIdCards.value.size === 0) return

  const rowsToSelect = tableData.value.filter(personnel =>
    selectedPersonnelIdCards.value.has(personnel.idCard)
  )

  rowsToSelect.forEach(row => {
    tableRef.value?.toggleRowSelection(row, true)
  })
}

// 选择相关
const handleSelectionChange = (selection: AbnormalPersonnel[]) => {
  selectedPersonnel.value = selection

  // 更新跨页面选择状态
  const currentPageIdCards = new Set(tableData.value.map(p => p.idCard))

  // 移除当前页面的选择
  for (const idCard of selectedPersonnelIdCards.value) {
    if (currentPageIdCards.has(idCard)) {
      selectedPersonnelIdCards.value.delete(idCard)
    }
  }

  // 添加新的选择
  selection.forEach(personnel => {
    selectedPersonnelIdCards.value.add(personnel.idCard)
  })
}

const clearAllSelection = () => {
  selectedPersonnelIdCards.value.clear()
  tableRef.value?.clearSelection()
}

// 头像点击
const handleAvatarClick = (data: { src: string; name: string; personnelId: string }) => {
  avatarPreviewData.value = data
  avatarPreviewVisible.value = true
}

// 处理任务相关
const handleStartProcessing = () => {
  startTaskDialogVisible.value = true
}

const handleReprocess = () => {
  if (selectedPersonnelIdCards.value.size === 0) {
    ElMessage.warning('请先选择要再次处理的人员')
    return
  }

  ElMessageBox.confirm(
    `确定要对选中的 ${selectedPersonnelIdCards.value.size} 人再次下发处理任务吗？`,
    '确认再次处理',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    startTaskDialogVisible.value = true
  })
}

const handleStartTask = (taskData: any) => {
  ElMessage.success('处理任务创建成功！')
  startTaskDialogVisible.value = false
  clearAllSelection()
  loadData()
}

// 详情查看
const handleViewDetail = (personnel: AbnormalPersonnel) => {
  selectedPersonnelId.value = personnel.id
  detailDrawerVisible.value = true
}

const handleViewTask = (taskId: string) => {
  // 跳转到任务详情页面
  router.push(`/result-processing/tasks?taskId=${taskId}`)
}

// 分页
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.abnormal-personnel {
  padding: 10px 30px 30px 30px;
  background-color: #ffffff;
}

.tabs-section {
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  padding: 12px 0;
  /* border-bottom: 1px solid #e4e7ed; */
}

.table-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-info {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.selected-info {
  font-size: 14px;
  color: #409eff;
  background: #ecf5ff;
  padding: 4px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-selection-btn {
  padding: 0 !important;
  margin-left: 4px;
  color: #909399;
  font-size: 12px;
  min-height: auto;
}

.clear-selection-btn:hover {
  color: #f56c6c;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .abnormal-personnel {
    padding: 5px 20px;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .table-info {
    justify-content: center;
  }

  .table-actions {
    justify-content: center;
  }
}

/* 头像预览样式 */
.avatar-preview {
  text-align: center;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.image-error p {
  margin-top: 10px;
  font-size: 14px;
}

/* 覆盖组件样式 */
:deep(.search-card) {
  /* 自定义样式 */
  border: none;
  box-shadow: none;
}

:deep(.el-card__body) {
  padding: 5px !important;
}

:deep(.search-header) {
  margin-bottom: 10px;
}

:deep(.search-card) {
  margin-bottom: 10px;
  /* border-bottom: 1px solid #e4e7ed; */
}
</style>
