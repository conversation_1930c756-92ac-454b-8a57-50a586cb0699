<template>
  <div class="abnormal-personnel-page">
    <!-- 搜索表单 -->
    <SearchForm
      :config="searchFormConfig"
      :custom-buttons="customButtons"
      @search="handleSearch"
      @reset="handleReset"
      @export="handleExport"
    />

    <!-- 状态标签页 -->
    <el-card class="status-tabs-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="待处理" name="pending">
          <template #label>
            <span>待处理 <el-tag size="small" type="info">{{ pendingCount }}</el-tag></span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="已处理" name="processed">
          <template #label>
            <span>已处理 <el-tag size="small" type="success">{{ processedCount }}</el-tag></span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 表格标题行 -->
    <el-card class="table-header-card">
      <div class="table-header">
        <div class="table-title">
          <span>异常人员：{{ totalCount }}个</span>
          <span v-if="selectedPersonnelIdCards.size > 0" class="selected-info">
            已选择：{{ selectedPersonnelIdCards.size }}个
            <el-button type="text" @click="clearSelection">
              <el-icon><Close /></el-icon>
            </el-button>
          </span>
        </div>
        <div class="table-actions">
          <el-button
            v-if="selectedPersonnelIdCards.size > 0"
            type="primary"
            @click="handleBatchProcessing"
          >
            批量下发处理任务
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 人员列表表格 -->
    <el-card class="table-card">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        @select="handleSelect"
        @select-all="handleSelectAll"
        row-key="idCard"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true" />
        
        <el-table-column label="照片" width="80">
          <template #default="scope">
            <PersonnelAvatar
              :src="scope.row.avatar"
              :name="scope.row.name"
              @click="handleAvatarClick(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="name" label="姓名" width="120" />

        <el-table-column label="身份证号" width="180">
          <template #default="scope">
            <span>{{ maskIdCard(scope.row.idCard) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="phone" label="联系方式" width="140" />
        <el-table-column prop="organization" label="所属机构" width="150" />

        <el-table-column label="异常类型" width="120">
          <template #default="scope">
            <el-tag :type="getAbnormalTypeColor(scope.row.abnormalType)">
              {{ getAbnormalTypeLabel(scope.row.abnormalType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="异常等级" width="100">
          <template #default="scope">
            <el-tag :type="getAbnormalLevelColor(scope.row.abnormalLevel)">
              {{ getAbnormalLevelLabel(scope.row.abnormalLevel) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="发现时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.discoveredAt) }}
          </template>
        </el-table-column>

        <el-table-column label="处理状态" width="100">
          <template #default="scope">
            <el-tag :type="getProcessingStatusColor(scope.row.processingStatus)">
              {{ getProcessingStatusLabel(scope.row.processingStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="assignedToName" label="处理人" width="120" />

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleViewDetail(scope.row)">
              详情
            </el-button>
            <el-button
              v-if="scope.row.processingStatus === 'pending'"
              type="text"
              size="small"
              @click="handleSingleProcessing(scope.row)"
            >
              下发处理
            </el-button>
            <el-button
              v-if="scope.row.processingStatus === 'completed'"
              type="text"
              size="small"
              @click="handleReprocess(scope.row)"
            >
              再次处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 头像预览弹窗 -->
    <el-dialog v-model="avatarPreviewVisible" title="头像预览" width="400px">
      <div class="avatar-preview">
        <img :src="previewAvatarSrc" :alt="previewPersonnelName" />
        <p>{{ previewPersonnelName }}</p>
      </div>
    </el-dialog>

    <!-- 发起处理任务弹窗 -->
    <StartProcessingTaskDialog
      v-model="processingDialogVisible"
      :selected-personnel="selectedPersonnelForProcessing"
      :operation-scope="operationScope"
      @confirm="handleProcessingConfirm"
    />

    <!-- 异常人员详情弹窗 -->
    <AbnormalPersonnelDetailDialog
      v-model="detailDialogVisible"
      :personnel="selectedPersonnelForDetail"
      @start-processing="handleStartProcessingFromDetail"
      @reprocess="handleReprocessFromDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import SearchForm from '@/components/common/SearchForm.vue'
import PersonnelAvatar from '@/components/common/PersonnelAvatar.vue'
import StartProcessingTaskDialog from '@/components/result-processing/StartProcessingTaskDialog.vue'
import AbnormalPersonnelDetailDialog from '@/components/result-processing/AbnormalPersonnelDetailDialog.vue'
import { 
  abnormalPersonnelMockData, 
  type AbnormalPersonnel 
} from '@/data/resultProcessingMockData'
import {
  abnormalTypeConfig,
  abnormalLevelConfig,
  processingStatusConfig,
  getAbnormalTypeOptions,
  getProcessingStatusOptions
} from '@/config/resultProcessingConfig'

// 响应式数据
const loading = ref(false)
const activeTab = ref('pending')
const tableData = ref<AbnormalPersonnel[]>([])
const selectedPersonnelIdCards = ref<Set<string>>(new Set())
const tableRef = ref()

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 弹窗状态
const avatarPreviewVisible = ref(false)
const previewAvatarSrc = ref('')
const previewPersonnelName = ref('')
const processingDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const selectedPersonnelForProcessing = ref<AbnormalPersonnel[]>([])
const selectedPersonnelForDetail = ref<AbnormalPersonnel | null>(null)
const operationScope = ref<'selected' | 'all'>('selected')

// 搜索表单配置
const searchFormConfig = computed(() => [
  [
    {
      key: 'name',
      type: 'input' as const,
      prop: 'name',
      label: '姓名',
      placeholder: '请输入姓名'
    },
    {
      key: 'abnormalType',
      type: 'select' as const,
      prop: 'abnormalType',
      label: '异常类型',
      options: getAbnormalTypeOptions()
    },
    {
      key: 'processingStatus',
      type: 'select' as const,
      prop: 'processingStatus',
      label: '处理状态',
      options: getProcessingStatusOptions()
    },
    {
      key: 'discoveredTimeRange',
      type: 'daterange' as const,
      prop: 'discoveredTimeRange',
      label: '发现时间'
    }
  ]
])

// 自定义按钮
const customButtons = [
  {
    text: '导出',
    type: 'default' as const,
    event: 'export'
  }
]

// 计算属性
const totalCount = computed(() => tableData.value.length)
const pendingCount = computed(() => 
  abnormalPersonnelMockData.filter(p => 
    ['pending', 'assigned', 'processing'].includes(p.processingStatus)
  ).length
)
const processedCount = computed(() => 
  abnormalPersonnelMockData.filter(p => 
    ['completed', 'rejected'].includes(p.processingStatus)
  ).length
)

// 工具函数
const maskIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const formatDate = (dateStr: string | undefined) => {
  if (!dateStr) return '-'
  return dateStr.split(' ')[0]
}

const getAbnormalTypeLabel = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.label || type
}

const getAbnormalTypeColor = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.color || 'info'
}

const getAbnormalLevelLabel = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.label || level
}

const getAbnormalLevelColor = (level: string) => {
  return abnormalLevelConfig[level as keyof typeof abnormalLevelConfig]?.color || 'info'
}

const getProcessingStatusLabel = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.label || status
}

const getProcessingStatusColor = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.color || 'info'
}

// 事件处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  loadData()
}

const handleSearch = (formData: any) => {
  console.log('搜索条件:', formData)
  loadData()
}

const handleReset = () => {
  loadData()
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const loadData = () => {
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    let filteredData = [...abnormalPersonnelMockData]
    
    // 根据标签页筛选
    if (activeTab.value === 'pending') {
      filteredData = filteredData.filter(p => 
        ['pending', 'assigned', 'processing'].includes(p.processingStatus)
      )
    } else {
      filteredData = filteredData.filter(p => 
        ['completed', 'rejected'].includes(p.processingStatus)
      )
    }
    
    tableData.value = filteredData
    pagination.value.total = filteredData.length
    loading.value = false
  }, 500)
}

// 选择相关
const handleSelectionChange = (selection: AbnormalPersonnel[]) => {
  // 更新选中状态
  const currentPageIdCards = new Set(tableData.value.map(p => p.idCard))
  
  // 移除当前页面的选择
  for (const idCard of selectedPersonnelIdCards.value) {
    if (currentPageIdCards.has(idCard)) {
      selectedPersonnelIdCards.value.delete(idCard)
    }
  }
  
  // 添加新的选择
  selection.forEach(personnel => {
    selectedPersonnelIdCards.value.add(personnel.idCard)
  })
}

const handleSelect = (selection: AbnormalPersonnel[], row: AbnormalPersonnel) => {
  if (selection.includes(row)) {
    selectedPersonnelIdCards.value.add(row.idCard)
  } else {
    selectedPersonnelIdCards.value.delete(row.idCard)
  }
}

const handleSelectAll = (selection: AbnormalPersonnel[]) => {
  const currentPageIdCards = tableData.value.map(p => p.idCard)
  
  if (selection.length === 0) {
    // 取消全选
    currentPageIdCards.forEach(idCard => {
      selectedPersonnelIdCards.value.delete(idCard)
    })
  } else {
    // 全选
    currentPageIdCards.forEach(idCard => {
      selectedPersonnelIdCards.value.add(idCard)
    })
  }
}

const clearSelection = () => {
  selectedPersonnelIdCards.value.clear()
  tableRef.value?.clearSelection()
}

// 头像点击
const handleAvatarClick = (personnel: AbnormalPersonnel) => {
  previewAvatarSrc.value = personnel.avatar || '/default-avatar.png'
  previewPersonnelName.value = personnel.name
  avatarPreviewVisible.value = true
}

// 处理任务相关
const handleBatchProcessing = () => {
  const selectedPersonnel = abnormalPersonnelMockData.filter(p => 
    selectedPersonnelIdCards.value.has(p.idCard)
  )
  selectedPersonnelForProcessing.value = selectedPersonnel
  operationScope.value = 'selected'
  processingDialogVisible.value = true
}

const handleSingleProcessing = (personnel: AbnormalPersonnel) => {
  selectedPersonnelForProcessing.value = [personnel]
  operationScope.value = 'selected'
  processingDialogVisible.value = true
}

const handleReprocess = (personnel: AbnormalPersonnel) => {
  ElMessageBox.confirm(
    `确定要对 ${personnel.name} 再次下发处理任务吗？`,
    '确认再次处理',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    handleSingleProcessing(personnel)
  })
}

const handleProcessingConfirm = (taskData: any) => {
  ElMessage.success('处理任务创建成功！')
  processingDialogVisible.value = false
  clearSelection()
  loadData()
}

// 详情查看
const handleViewDetail = (personnel: AbnormalPersonnel) => {
  selectedPersonnelForDetail.value = personnel
  detailDialogVisible.value = true
}

const handleStartProcessingFromDetail = (personnel: AbnormalPersonnel) => {
  selectedPersonnelForProcessing.value = [personnel]
  operationScope.value = 'selected'
  processingDialogVisible.value = true
}

const handleReprocessFromDetail = (personnel: AbnormalPersonnel) => {
  selectedPersonnelForProcessing.value = [personnel]
  operationScope.value = 'selected'
  processingDialogVisible.value = true
}

// 分页
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  loadData()
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.abnormal-personnel-page {
  padding: 20px;
}

.status-tabs-card {
  margin-bottom: 16px;
}

.table-header-card {
  margin-bottom: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selected-info {
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.table-card {
  margin-bottom: 16px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.avatar-preview {
  text-align: center;
}

.avatar-preview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
}

.avatar-preview p {
  margin-top: 16px;
  font-size: 16px;
  font-weight: 500;
}

/* 覆盖组件样式 */
:deep(.search-card) {
  border: none;
  box-shadow: none;
}

:deep(.el-card__body) {
  padding: 5px !important;
}

:deep(.search-header) {
  margin-bottom: 10px;
}

:deep(.search-card) {
  margin-bottom: 10px;
}
</style>
