<template>
  <div class="dashboard-container">
    <!-- 第一行：人员总览指标卡（独占一行） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <div class="chart-item metrics-row">
          <PersonnelMetricsCard @click="handleChartClick('personnel-metrics')" />
        </div>
      </el-col>
    </el-row>

    <!-- 第二行：人员分布类图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="8">
        <div class="chart-item">
          <PersonnelTypeChart @click="handleChartClick('personnel-type')" />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-item">
          <IndustryDistributionChart @click="handleChartClick('industry-distribution')" />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-item">
          <BackgroundCheckChart @click="handleChartClick('background-check')" />
        </div>
      </el-col>
    </el-row>

    <!-- 第三行：异常分析类图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="8">
        <div class="chart-item">
          <AbnormalTypeChart @click="handleChartClick('abnormal-type')" />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-item">
          <ProcessingStatusChart @click="handleChartClick('processing-status')" />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-item">
          <AbnormalTrendChart @click="handleChartClick('abnormal-trend')" />
        </div>
      </el-col>
    </el-row>

    <!-- 第四行：趋势分析类图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="8">
        <div class="chart-item">
          <MonthlyTrendChart @click="handleChartClick('monthly-trend')" />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-item">
          <CompletionRateChart @click="handleChartClick('completion-rate')" />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-item">
          <RegionDistributionChart @click="handleChartClick('region-distribution')" />
        </div>
      </el-col>
    </el-row>

    <!-- 详情弹窗组件 -->
    <PersonnelMetricsDetail v-model="personnelMetricsDetailVisible" />
    <PersonnelTypeDetail v-model="personnelTypeDetailVisible" />
    <IndustryDistributionDetail v-model="industryDistributionDetailVisible" />
    <MonthlyTrendDetail v-model="monthlyTrendDetailVisible" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 导入图表组件
import PersonnelMetricsCard from '@/components/dashboard/PersonnelMetricsCard.vue'
import PersonnelTypeChart from '@/components/dashboard/PersonnelTypeChart.vue'
import IndustryDistributionChart from '@/components/dashboard/IndustryDistributionChart.vue'
import BackgroundCheckChart from '@/components/dashboard/BackgroundCheckChart.vue'
import AbnormalTypeChart from '@/components/dashboard/AbnormalTypeChart.vue'
import ProcessingStatusChart from '@/components/dashboard/ProcessingStatusChart.vue'
import AbnormalTrendChart from '@/components/dashboard/AbnormalTrendChart.vue'
import MonthlyTrendChart from '@/components/dashboard/MonthlyTrendChart.vue'
import CompletionRateChart from '@/components/dashboard/CompletionRateChart.vue'
import RegionDistributionChart from '@/components/dashboard/RegionDistributionChart.vue'

// 导入详情弹窗组件
import PersonnelMetricsDetail from '@/components/dashboard/details/PersonnelMetricsDetail.vue'
import PersonnelTypeDetail from '@/components/dashboard/details/PersonnelTypeDetail.vue'
import IndustryDistributionDetail from '@/components/dashboard/details/IndustryDistributionDetail.vue'
import MonthlyTrendDetail from '@/components/dashboard/details/MonthlyTrendDetail.vue'

// 详情弹窗显示状态
const personnelMetricsDetailVisible = ref(false)
const personnelTypeDetailVisible = ref(false)
const industryDistributionDetailVisible = ref(false)
const monthlyTrendDetailVisible = ref(false)



// 事件处理函数
const handleChartClick = (chartType: string) => {
  switch (chartType) {
    case 'personnel-metrics':
      personnelMetricsDetailVisible.value = true
      break
    case 'personnel-type':
      personnelTypeDetailVisible.value = true
      break
    case 'industry-distribution':
      industryDistributionDetailVisible.value = true
      break
    case 'monthly-trend':
      monthlyTrendDetailVisible.value = true
      break
    default:
      ElMessage.warning('暂无详情数据')
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 行布局样式 */
.chart-row {
  margin-bottom: 24px;
}

.chart-row:last-child {
  margin-bottom: 0;
}

/* 图表项样式 */
.chart-item {
  height: 400px;
  transition: all 0.3s ease;
}

.chart-item:hover {
  transform: scale(1.02);
}

/* 指标卡行样式 */
.chart-item.metrics-row {
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;
  }

  .chart-row {
    margin-bottom: 16px;
  }

  .chart-item {
    height: 320px;
    margin-bottom: 16px;
  }

  /* 移动端改为单列布局 */
  :deep(.el-col) {
    width: 100% !important;
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .chart-item {
    height: 360px;
  }

  /* 平板端改为双列布局 */
  :deep(.el-col) {
    width: 50% !important;
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  .chart-item {
    height: 380px;
  }
}

@media (min-width: 1441px) {
  .chart-item {
    height: 420px;
  }
}

/* 动画效果 */
.chart-item {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为不同行的图表添加延迟动画 */
.chart-row:nth-child(1) .chart-item { animation-delay: 0.1s; }
.chart-row:nth-child(2) .chart-item { animation-delay: 0.3s; }
.chart-row:nth-child(3) .chart-item { animation-delay: 0.5s; }
.chart-row:nth-child(4) .chart-item { animation-delay: 0.7s; }

/* 同一行内的图表错开动画 */
.chart-row .el-col:nth-child(1) .chart-item { animation-delay: inherit; }
.chart-row .el-col:nth-child(2) .chart-item { animation-delay: calc(var(--base-delay, 0.1s) + 0.1s); }
.chart-row .el-col:nth-child(3) .chart-item { animation-delay: calc(var(--base-delay, 0.1s) + 0.2s); }

/* 设置基础延迟变量 */
.chart-row:nth-child(1) { --base-delay: 0.1s; }
.chart-row:nth-child(2) { --base-delay: 0.3s; }
.chart-row:nth-child(3) { --base-delay: 0.5s; }
.chart-row:nth-child(4) { --base-delay: 0.7s; }
</style>
