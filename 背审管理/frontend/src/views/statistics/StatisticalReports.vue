<template>
  <div class="statistical-reports">
    <div class="reports-layout">
      <!-- 左侧报表目录树 -->
      <div class="reports-sidebar">
        <el-card class="tree-card" shadow="never">
          <template #header>
            <div class="tree-header">
              <el-icon>
                <FolderOpened />
              </el-icon>
              <span>报表目录</span>
            </div>
          </template>
          <el-tree :data="reportTreeData" :props="treeProps" node-key="id" :expand-on-click-node="true"
            :highlight-current="true" @node-click="handleNodeClick" class="report-tree">
            <template #default="{ node, data }">
              <div class="tree-node">
                <el-icon v-if="data.type === 'folder'">
                  <Folder />
                </el-icon>
                <el-icon v-else>
                  <Document />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
              </div>
            </template>
          </el-tree>
        </el-card>
      </div>

      <!-- 右侧内容区域 -->
      <div class="reports-content">
        <!-- 报表参数配置区域 -->
        <el-card class="params-card" shadow="never" v-if="selectedReport">
          <template #header>
            <div class="params-header">
              <el-icon>
                <Setting />
              </el-icon>
              <span>{{ selectedReport.name }} - 参数配置</span>
            </div>
          </template>

          <!-- 动态加载参数组件 -->
          <AbnormalPersonnelParams v-if="selectedReport.key === 'abnormal-personnel'" :generating="generating"
            @generate="handleGenerateReport" @reset="handleResetParams" />
          <UnitStatisticsParams v-else-if="selectedReport.key === 'unit-statistics'" :generating="generating"
            @generate="handleGenerateReport" @reset="handleResetParams" />
          <IndustryStatisticsParams v-else-if="selectedReport.key === 'industry-statistics'" :generating="generating"
            @generate="handleGenerateReport" @reset="handleResetParams" />
          <AbnormalTypeStatisticsParams v-else-if="selectedReport.key === 'abnormal-type-statistics'"
            :generating="generating" @generate="handleGenerateReport" @reset="handleResetParams" />
        </el-card>

        <!-- 报表预览区域 -->
        <el-card class="preview-card" shadow="never" v-if="showPreview">
          <template #header>
            <div class="preview-header">
              <el-icon>
                <View />
              </el-icon>
              <span>{{ selectedReport.name }} - 报表预览</span>
              <div class="preview-actions">
                <el-button type="primary" @click="handlePrintReport">
                  <el-icon>
                    <Printer />
                  </el-icon>
                  打印报表
                </el-button>
                <el-button @click="handleExportReport" :loading="exporting">
                  <el-icon>
                    <Download />
                  </el-icon>
                  导出Excel
                </el-button>
              </div>
            </div>
          </template>

          <!-- 动态加载预览组件 -->
          <AbnormalPersonnelPreview v-if="selectedReport.key === 'abnormal-personnel'" ref="previewComponentRef"
            :report-data="reportData" :params="currentParams" />
          <UnitStatisticsPreview v-else-if="selectedReport.key === 'unit-statistics'" ref="previewComponentRef"
            :report-data="reportData" :params="currentParams" />
          <IndustryStatisticsPreview v-else-if="selectedReport.key === 'industry-statistics'" ref="previewComponentRef"
            :report-data="reportData" :params="currentParams" />
          <AbnormalTypeStatisticsPreview v-else-if="selectedReport.key === 'abnormal-type-statistics'"
            ref="previewComponentRef" :report-data="reportData" :params="currentParams" />
        </el-card>
      </div>
    </div>
  </div>


</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  FolderOpened,
  Folder,
  Document,
  Setting,
  View,
  Printer,
  Download
} from '@element-plus/icons-vue'

import { generateReportMockData } from '@/utils/reportMockData'

// 导入报表组件
import AbnormalPersonnelParams from '@/components/reports/AbnormalPersonnelParams.vue'
import AbnormalPersonnelPreview from '@/components/reports/AbnormalPersonnelPreview.vue'
import UnitStatisticsParams from '@/components/reports/UnitStatisticsParams.vue'
import UnitStatisticsPreview from '@/components/reports/UnitStatisticsPreview.vue'
import IndustryStatisticsParams from '@/components/reports/IndustryStatisticsParams.vue'
import IndustryStatisticsPreview from '@/components/reports/IndustryStatisticsPreview.vue'
import AbnormalTypeStatisticsParams from '@/components/reports/AbnormalTypeStatisticsParams.vue'
import AbnormalTypeStatisticsPreview from '@/components/reports/AbnormalTypeStatisticsPreview.vue'

// 报表树形数据结构
const reportTreeData = ref([
  {
    id: 'personnel-reports',
    label: '人员报表',
    type: 'folder',
    children: [
      {
        id: 'abnormal-personnel',
        label: '异常人员名单报表',
        type: 'report',
        key: 'abnormal-personnel',
        name: '异常人员名单报表',
        description: '生成指定时间范围内的异常人员详细名单'
      }
    ]
  },
  {
    id: 'statistics-reports',
    label: '统计分析报表',
    type: 'folder',
    children: [
      {
        id: 'unit-statistics',
        label: '单位统计报表',
        type: 'report',
        key: 'unit-statistics',
        name: '单位统计报表',
        description: '按单位统计异常人员分布情况'
      },
      {
        id: 'industry-statistics',
        label: '行业统计报表',
        type: 'report',
        key: 'industry-statistics',
        name: '行业统计报表',
        description: '各行业的人员分布、异常统计、趋势分析'
      },
      {
        id: 'abnormal-type-statistics',
        label: '异常类型统计报表',
        type: 'report',
        key: 'abnormal-type-statistics',
        name: '异常类型统计报表',
        description: '各异常类型的人员分布、异常统计、趋势分析'
      }
    ]
  }
])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 当前选中的报表
const selectedReport = ref<any>(null)

// 报表数据
const reportData = ref<any[]>([])

// 当前参数
const currentParams = ref<any>({})

// 显示预览
const showPreview = ref(false)

// 加载状态
const generating = ref(false)
const exporting = ref(false)

// 预览组件引用
const previewComponentRef = ref()



// 树节点点击事件
const handleNodeClick = (data: any) => {
  if (data.type === 'report') {
    selectedReport.value = data
    showPreview.value = false
    reportData.value = []
    currentParams.value = {}
  }
}

// 处理生成报表事件
const handleGenerateReport = async (params: any) => {
  if (!selectedReport.value) {
    ElMessage.warning('请先选择报表类型')
    return
  }

  if (!params.dateRange || params.dateRange.length !== 2) {
    ElMessage.warning('请选择时间范围')
    return
  }

  try {
    generating.value = true
    currentParams.value = params

    // 使用Mock数据生成报表
    const mockData = generateReportMockData(selectedReport.value.key, params)
    reportData.value = mockData
    showPreview.value = true

    ElMessage.success('报表生成成功')
  } catch (error) {
    console.error('生成报表失败:', error)
    ElMessage.error('生成报表失败')
  } finally {
    generating.value = false
  }
}

// 处理重置参数事件
const handleResetParams = () => {
  showPreview.value = false
  reportData.value = []
  currentParams.value = {}
}



// 处理打印报表
const handlePrintReport = () => {
  if (previewComponentRef.value && previewComponentRef.value.handlePrint) {
    previewComponentRef.value.handlePrint()
  } else {
    ElMessage.warning('当前报表不支持打印功能')
  }
}

// 处理导出报表
const handleExportReport = async () => {
  if (previewComponentRef.value && previewComponentRef.value.handleExport) {
    exporting.value = true
    try {
      await previewComponentRef.value.handleExport()
    } catch (error) {
      console.error('导出报表失败:', error)
      ElMessage.error('导出报表失败')
    } finally {
      exporting.value = false
    }
  } else {
    ElMessage.warning('当前报表不支持导出功能')
  }
}
</script>

<style scoped>
.statistical-reports {
  padding: 0;
  height: 100%;
  min-height: 80vh;
}

.reports-layout {
  display: flex;
  gap: 16px;
  min-height: 85vh;
}

/* 左侧报表目录树样式 */
.reports-sidebar {
  width: 280px;
  flex-shrink: 0;
}

.tree-card {
  height: 100%;
  border: 1px solid #e4e7ed;
}

.tree-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.report-tree {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.tree-node .el-icon {
  color: #409eff;
}

.node-label {
  font-size: 14px;
  color: #303133;
}

/* 右侧内容区域样式 */
.reports-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

/* 参数配置卡片样式 */
.params-card {
  border: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.params-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.params-form {
  padding: 0;
}

/* 预览卡片样式 */
.preview-card {
  flex: 1;
  border: 1px solid #e4e7ed;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 12px;
}

/* 报表内容样式 */
.report-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.report-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  text-align: center;
  margin-bottom: 20px;
}

.report-meta {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.report-meta p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.report-meta p:last-child {
  margin-bottom: 0;
}

.type-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

/* 树形组件自定义样式 */
:deep(.el-tree-node__content) {
  padding: 8px 0;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #409eff;
}

:deep(.el-tree-node__expand-icon) {
  color: #409eff;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .reports-layout {
    flex-direction: column;
    height: auto;
  }

  .reports-sidebar {
    width: 100%;
    height: 300px;
  }

  .reports-content {
    height: auto;
  }
}

@media print {

  .reports-sidebar,
  .params-card {
    display: none;
  }

  .preview-header {
    display: none;
  }

  .reports-layout {
    flex-direction: column;
  }

  .reports-content {
    width: 100%;
  }
}
</style>
