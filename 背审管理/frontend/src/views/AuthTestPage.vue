<template>
  <div class="auth-test-page">
    <!-- 角色切换器 -->
    <RoleSwitcher />
    
    <div class="container">
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>权限系统测试页面</span>
            <el-button type="primary" @click="refreshAuth">刷新权限</el-button>
          </div>
        </template>
        
        <!-- 用户信息展示 -->
        <div class="user-section">
          <h3>当前用户信息</h3>
          <el-descriptions :column="2" border v-if="user">
            <el-descriptions-item label="用户ID">{{ user.userId }}</el-descriptions-item>
            <el-descriptions-item label="用户名">{{ user.username }}</el-descriptions-item>
            <el-descriptions-item label="姓名">{{ user.name }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ user.phone }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ user.email }}</el-descriptions-item>
            <el-descriptions-item label="组织ID">{{ user.organizationId }}</el-descriptions-item>
            <el-descriptions-item label="组织名称">{{ user.organizationName }}</el-descriptions-item>
            <el-descriptions-item label="角色">
              <el-tag v-for="role in user.roles" :key="role" :type="role === 'admin' ? 'danger' : 'success'">
                {{ role === 'admin' ? '管理员' : '下级单位' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          <el-empty v-else description="用户信息加载中..." />
        </div>
        
        <!-- 权限信息展示 -->
        <div class="permission-section">
          <h3>权限信息</h3>
          <div class="permission-info">
            <p><strong>是否管理员:</strong>
              <el-tag :type="isAdmin ? 'danger' : 'success'">
                {{ isAdmin ? '是' : '否' }}
              </el-tag>
            </p>
            <p><strong>数据访问范围:</strong>
              <el-tag :type="dataScope === 'all' ? 'warning' : 'info'">
                {{ dataScope === 'all' ? '全部数据' : '本组织数据' }}
              </el-tag>
            </p>
            <p><strong>当前角色:</strong>
              <el-tag :type="currentRole === 'admin' ? 'danger' : 'success'">
                {{ currentRole === 'admin' ? '管理员' : '下级单位' }}
              </el-tag>
            </p>
          </div>

          <!-- localStorage状态 -->
          <div class="storage-status">
            <h4>localStorage状态:</h4>
            <div class="storage-info">
              <p><strong>有缓存:</strong>
                <el-tag :type="cacheStatus.hasCache ? 'success' : 'warning'">
                  {{ cacheStatus.hasCache ? '是' : '否' }}
                </el-tag>
              </p>
              <p v-if="cacheStatus.timestamp"><strong>缓存时间:</strong>
                <el-tag type="info">{{ formatTimestamp(cacheStatus.timestamp) }}</el-tag>
              </p>
              <p><strong>是否过期:</strong>
                <el-tag :type="cacheStatus.isExpired ? 'danger' : 'success'">
                  {{ cacheStatus.isExpired ? '是' : '否' }}
                </el-tag>
              </p>
              <div class="storage-actions">
                <el-button size="small" @click="clearCache">清除缓存</el-button>
                <el-button size="small" @click="refreshCacheStatus">刷新状态</el-button>
              </div>
            </div>
          </div>
          
          <div class="permissions-list">
            <h4>用户权限列表:</h4>
            <el-tag 
              v-for="permission in userPermissions" 
              :key="permission" 
              style="margin: 4px;"
              size="small"
            >
              {{ permission }}
            </el-tag>
          </div>
        </div>
        
        <!-- 权限测试 -->
        <div class="permission-test">
          <h3>权限指令测试</h3>
          <div class="test-buttons">
            <!-- 使用 v-permission 指令 -->
            <el-button v-permission="'personnel:view:all'" type="primary">
              查看所有人员 (仅管理员可见)
            </el-button>
            
            <el-button v-permission="'personnel:view:org'" type="success">
              查看本组织人员 (下级单位可见)
            </el-button>
            
            <el-button v-permission="'task:manage'" type="warning">
              任务管理 (仅管理员可见)
            </el-button>
            
            <el-button v-permission="'task:process'" type="info">
              任务处理 (下级单位可见)
            </el-button>
            
            <!-- 使用 v-role 指令 -->
            <el-button v-role="'admin'" type="danger">
              管理员专用功能
            </el-button>
            
            <el-button v-role="'unit'" type="success">
              下级单位专用功能
            </el-button>
            
            <!-- 多权限测试 -->
            <el-button v-permission="['task:assign', 'task:review']" type="primary">
              任务分配或审核 (任一权限即可)
            </el-button>
            
            <el-button v-permission:all="['personnel:view:all', 'task:manage']" type="warning">
              需要全部权限 (必须同时拥有)
            </el-button>
          </div>
        </div>
        
        <!-- 编程式权限检查 -->
        <div class="programmatic-test">
          <h3>编程式权限检查</h3>
          <div class="test-results">
            <p>hasPermission('personnel:view:all'): 
              <el-tag :type="hasPersonnelViewAll ? 'success' : 'danger'">
                {{ hasPersonnelViewAll }}
              </el-tag>
            </p>
            <p>hasPermission('task:process'): 
              <el-tag :type="hasTaskProcess ? 'success' : 'danger'">
                {{ hasTaskProcess }}
              </el-tag>
            </p>
            <p>isAdmin(): 
              <el-tag :type="isAdminResult ? 'success' : 'danger'">
                {{ isAdminResult }}
              </el-tag>
            </p>
            <p>getCurrentRole(): 
              <el-tag>{{ currentRoleResult }}</el-tag>
            </p>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useAuthStore } from '@/stores/authStore'
import { hasPermission, isAdmin as checkIsAdmin, getCurrentRole } from '@/utils/auth'
import authService from '@/services/authService'
import RoleSwitcher from '@/components/common/RoleSwitcher.vue'

const authStore = useAuthStore()

// 计算属性
const user = computed(() => authStore.user)
const isAdmin = computed(() => authStore.isAdmin)
const dataScope = computed(() => authStore.dataScope)
const currentRole = computed(() => authStore.currentRole)
const userPermissions = computed(() => authStore.userPermissions)

// localStorage缓存状态
const cacheStatus = ref(authService.getCacheStatus())

// 编程式权限检查
const hasPersonnelViewAll = computed(() => hasPermission('personnel:view:all'))
const hasTaskProcess = computed(() => hasPermission('task:process'))
const isAdminResult = computed(() => checkIsAdmin())
const currentRoleResult = computed(() => getCurrentRole())

// 方法
const refreshAuth = async () => {
  try {
    await authStore.refreshUserInfo()
    refreshCacheStatus()
    console.log('权限信息已刷新')
  } catch (error) {
    console.error('刷新权限失败:', error)
  }
}

const clearCache = () => {
  authService.clearCache()
  refreshCacheStatus()
}

const refreshCacheStatus = () => {
  cacheStatus.value = authService.getCacheStatus()
}

const formatTimestamp = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  if (!authStore.initialized) {
    await authStore.initAuth()
  }
  refreshCacheStatus()
})
</script>

<style scoped>
.auth-test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 80px; /* 为固定的角色切换器留出空间 */
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-section,
.permission-section,
.permission-test,
.programmatic-test {
  margin-bottom: 30px;
}

.permission-info {
  margin-bottom: 20px;
}

.permission-info p {
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.permissions-list {
  margin-top: 16px;
}

.storage-status {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.storage-info {
  margin-top: 12px;
}

.storage-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 16px;
}

.test-results {
  margin-top: 16px;
}

.test-results p {
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

h3 {
  color: #409eff;
  margin-bottom: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

h4 {
  color: #606266;
  margin: 16px 0 8px 0;
}
</style>
