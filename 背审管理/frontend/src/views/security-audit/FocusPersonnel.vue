<template>
  <div class="focus-personnel">

    <!-- 标签页 -->
    <el-card class="tabs-card" shadow="never">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" type="card">
        <!-- 重点关注人员 -->
        <el-tab-pane label="重点关注人员" name="focus">
          <div class="tab-content">
            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="searchForm" label-width="100px">
                <!-- 第一行 -->
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="姓名">
                      <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="身份证号">
                      <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="所属单位">
                      <el-input v-model="searchForm.organization" placeholder="请输入所属单位" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第二行 -->
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="区域">
                      <el-input v-model="searchForm.region" placeholder="请输入区域" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="人员类型">
                      <el-select v-model="searchForm.personnelType" placeholder="请选择人员类型" clearable style="width: 100%">
                        <el-option label="全部" value="" />
                        <el-option label="专职保卫" value="1" />
                        <el-option label="保安人员" value="2" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="异常类型">
                      <el-select v-model="searchForm.abnormalTypes" placeholder="请选择异常类型" multiple clearable style="width: 100%">
                        <el-option
                          v-for="type in backgroundCheckAbnormalTypes"
                          :key="type.value"
                          :label="type.label"
                          :value="type.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第三行 -->
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="入职时间">
                      <el-date-picker
                        v-model="searchForm.entryDateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        style="width: 100%"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        clearable
                        :shortcuts="entryDateShortcuts"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="关注时间">
                      <el-date-picker
                        v-model="searchForm.focusDateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        style="width: 100%"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        clearable
                        :shortcuts="focusDateShortcuts"
                      />
                    </el-form-item>
                  </el-col>
                   <el-col :span="8">
                    <el-form-item style="text-align: right; margin-bottom: 0;">
                      <el-button type="primary" @click="handleSearch" :loading="loading" class="action-btn">
                        <el-icon><Search /></el-icon>
                        搜索
                      </el-button>
                      <el-button @click="handleReset" class="action-btn">
                        <el-icon><Refresh /></el-icon>
                        重置
                      </el-button>
                      <el-button @click="handleExportFocus" :loading="exportLoading" class="action-btn">
                        <el-icon><Download /></el-icon>
                        导出
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>

              
              </el-form>
            </div>
            <!-- 重点关注人员表格 -->
            <el-table :data="focusTableData" :loading="loading" stripe border>
              <el-table-column prop="name" label="姓名" width="100" />
              <el-table-column prop="idCard" label="身份证号" width="180" />
              <el-table-column prop="organization" label="所属单位" />
              <el-table-column prop="entryDate" label="入职日期" width="120" />
              <el-table-column prop="region" label="区域" width="120" />
              <el-table-column label="关注时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.focusTime) }}
                </template>
              </el-table-column>
              <el-table-column label="异常类型" width="200">
                <template #default="{ row }">
                  <el-tag 
                    v-for="type in row.abnormalTypes" 
                    :key="type" 
                    type="danger" 
                    size="small"
                    class="type-tag"
                  >
                    {{ getSingleAbnormalTypeText(type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" @click="handleViewFocusDetail(row.id)">
                    处理
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

          </div>
        </el-tab-pane>

        <!-- 调岗/劝退人员 -->
        <el-tab-pane label="调岗/劝退人员" name="transfer">
          <div class="tab-content">
            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="searchForm" label-width="100px">
                <!-- 第一行 -->
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="姓名">
                      <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="身份证号">
                      <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="所属单位">
                      <el-input v-model="searchForm.organization" placeholder="请输入所属单位" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第二行 -->
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="区域">
                      <el-input v-model="searchForm.region" placeholder="请输入区域" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="人员类型">
                      <el-select v-model="searchForm.personnelType" placeholder="请选择人员类型" clearable style="width: 100%">
                        <el-option label="全部" value="" />
                        <el-option label="专职保卫" value="1" />
                        <el-option label="保安人员" value="2" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="异常类型">
                      <el-select v-model="searchForm.abnormalTypes" placeholder="请选择异常类型" multiple clearable style="width: 100%">
                        <el-option
                          v-for="type in backgroundCheckAbnormalTypes"
                          :key="type.value"
                          :label="type.label"
                          :value="type.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第三行 -->
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="入职时间">
                      <el-date-picker
                        v-model="searchForm.entryDateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        style="width: 100%"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        clearable
                        :shortcuts="entryDateShortcuts"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="关注时间">
                      <el-date-picker
                        v-model="searchForm.focusDateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        style="width: 100%"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        clearable
                        :shortcuts="focusDateShortcuts"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item style="text-align: right; margin-bottom: 0;">
                      <el-button type="primary" @click="handleSearch" :loading="loading" class="action-btn">
                        <el-icon><Search /></el-icon>
                        搜索
                      </el-button>
                      <el-button @click="handleReset" class="action-btn">
                        <el-icon><Refresh /></el-icon>
                        重置
                      </el-button>
                      <el-button @click="handleExportTransfer" :loading="exportLoading" class="action-btn">
                        <el-icon><Download /></el-icon>
                        导出
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 操作按钮行 -->
                <el-row :gutter="24">
                  
                </el-row>
              </el-form>
            </div>


            <!-- 调岗/劝退人员表格 -->
            <el-table :data="transferTableData" :loading="loading" stripe border>
              <el-table-column prop="name" label="姓名" width="100" />
              <el-table-column prop="idCard" label="身份证号" width="180" />
              <el-table-column prop="organization" label="所属单位" />
              <el-table-column prop="entryDate" label="入职日期" width="120" />
              <el-table-column prop="region" label="区域" width="120" />
              <el-table-column label="计划劝退时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.plannedTransferTime) }}
                </template>
              </el-table-column>
              <el-table-column label="当前状态" width="120">
                <template #default="{ row }">
                  <el-tag :type="getTransferStatusType(row.transferStatus)">
                    {{ getTransferStatusText(row.transferStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" @click="handleViewTransferDetail(row.id)">
                    处理
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="activeTab === 'focus'">
      <el-pagination
        v-model:current-page="focusPagination.page"
        v-model:page-size="focusPagination.size"
        :page-sizes="[10, 20, 50]"
        :total="focusPagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleFocusSizeChange"
        @current-change="handleFocusCurrentChange"
      />
    </div>
    <div class="pagination-wrapper" v-else>
      <el-pagination
        v-model:current-page="transferPagination.page"
        v-model:page-size="transferPagination.size"
        :page-sizes="[10, 20, 50]"
        :total="transferPagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleTransferSizeChange"
        @current-change="handleTransferCurrentChange"
      />
    </div>

    <!-- 重点关注详情抽屉 -->
    <FocusDetailDrawer
      v-model="focusDetailVisible"
      :personnel-id="selectedPersonnelId"
      @status-changed="handleStatusChanged"
    />

    <!-- 调岗/劝退详情抽屉 -->
    <TransferDetailDrawer
      v-model="transferDetailVisible"
      :personnel-id="selectedPersonnelId"
      @status-changed="handleStatusChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import { getPersonnelList, exportPersonnelData } from '@/api/background-check'
import { backgroundCheckAbnormalTypes } from '@/data/personnelMockData'
import FocusDetailDrawer from '@/components/background-check/FocusDetailDrawer.vue'
import TransferDetailDrawer from '@/components/background-check/TransferDetailDrawer.vue'
import { getSingleAbnormalTypeText } from '@/data/personnelMockData'
// import FocusDetailDrawer from '@/components/background-check/FocusDetailDrawer.vue'
// import TransferDetailDrawer from '@/components/background-check/TransferDetailDrawer.vue'

// 当前标签页
const activeTab = ref('focus')

// 搜索表单
const searchForm = reactive({
  name: '',
  idCard: '',
  organization: '',
  region: '',
  personnelType: '',
  abnormalTypes: [] as string[],
  entryDateRange: [] as string[],
  focusDateRange: [] as string[]
})

// 入职时间快捷选项
const entryDateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '最近半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
      return [start, end]
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  }
]

// 关注时间快捷选项
const focusDateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '最近半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
      return [start, end]
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  }
]

// 表格数据
const focusTableData = ref([])
const transferTableData = ref([])
const loading = ref(false)
const exportLoading = ref(false)

// 分页
const focusPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const transferPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 抽屉状态
const focusDetailVisible = ref(false)
const transferDetailVisible = ref(false)
const selectedPersonnelId = ref<number | null>(null)

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString()
}

// 获取调岗状态类型
const getTransferStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'warning', // 计划中
    1: 'success', // 已完成
    2: 'danger'   // 已取消
  }
  return typeMap[status] || 'info'
}

// 获取调岗状态文本
const getTransferStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    0: '计划中',
    1: '已完成',
    2: '已取消'
  }
  return textMap[status] || '未知'
}

// 获取重点关注人员列表
const fetchFocusPersonnel = async () => {
  try {
    loading.value = true
    const { entryDateRange, focusDateRange, ...formData } = searchForm
    const params = {
      ...formData,
      personnelType: searchForm.personnelType ? Number(searchForm.personnelType) : undefined,
      processingStatus: '2', // 重点关注
      // 处理日期范围
      entryDateStart: entryDateRange && entryDateRange.length > 0 ? entryDateRange[0] : undefined,
      entryDateEnd: entryDateRange && entryDateRange.length > 1 ? entryDateRange[1] : undefined,
      focusDateStart: focusDateRange && focusDateRange.length > 0 ? focusDateRange[0] : undefined,
      focusDateEnd: focusDateRange && focusDateRange.length > 1 ? focusDateRange[1] : undefined,
      page: focusPagination.page,
      size: focusPagination.size
    }
    
    const response = await getPersonnelList(params)
    focusTableData.value = response.data.records.map((item: any) => ({
      ...item,
      focusTime: item.updateTime || item.createTime
    }))
    focusPagination.total = response.data.total
  } catch (error) {
    console.error('获取重点关注人员失败:', error)
    ElMessage.error('获取重点关注人员失败')
  } finally {
    loading.value = false
  }
}

// 获取调岗/劝退人员列表
const fetchTransferPersonnel = async () => {
  try {
    loading.value = true
    const { entryDateRange, focusDateRange, ...formData } = searchForm
    const params = {
      ...formData,
      personnelType: searchForm.personnelType ? Number(searchForm.personnelType) : undefined,
      processingStatus: '3', // 调岗/劝退
      // 处理日期范围
      entryDateStart: entryDateRange && entryDateRange.length > 0 ? entryDateRange[0] : undefined,
      entryDateEnd: entryDateRange && entryDateRange.length > 1 ? entryDateRange[1] : undefined,
      focusDateStart: focusDateRange && focusDateRange.length > 0 ? focusDateRange[0] : undefined,
      focusDateEnd: focusDateRange && focusDateRange.length > 1 ? focusDateRange[1] : undefined,
      page: transferPagination.page,
      size: transferPagination.size
    }
    
    const response = await getPersonnelList(params)
    transferTableData.value = response.data.records.map((item: any) => ({
      ...item,
      plannedTransferTime: item.updateTime || item.createTime,
      transferStatus: Math.floor(Math.random() * 3) // 模拟状态
    }))
    transferPagination.total = response.data.total
  } catch (error) {
    console.error('获取调岗/劝退人员失败:', error)
    ElMessage.error('获取调岗/劝退人员失败')
  } finally {
    loading.value = false
  }
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  if (tabName === 'focus') {
    fetchFocusPersonnel()
  } else {
    fetchTransferPersonnel()
  }
}

// 搜索
const handleSearch = () => {
  if (activeTab.value === 'focus') {
    focusPagination.page = 1
    fetchFocusPersonnel()
  } else {
    transferPagination.page = 1
    fetchTransferPersonnel()
  }
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    idCard: '',
    organization: '',
    region: '',
    personnelType: '',
    abnormalTypes: [],
    entryDateRange: [],
    focusDateRange: []
  })
  if (activeTab.value === 'focus') {
    focusPagination.page = 1
    fetchFocusPersonnel()
  } else {
    transferPagination.page = 1
    fetchTransferPersonnel()
  }
}

// 查看重点关注详情
const handleViewFocusDetail = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  focusDetailVisible.value = true
}

// 查看调岗/劝退详情
const handleViewTransferDetail = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  transferDetailVisible.value = true
}

// 状态变更回调
const handleStatusChanged = () => {
  if (activeTab.value === 'focus') {
    fetchFocusPersonnel()
  } else {
    fetchTransferPersonnel()
  }
}

// 导出重点关注人员数据
const handleExportFocus = async () => {
  try {
    exportLoading.value = true
    const { entryDateRange, focusDateRange, ...formData } = searchForm
    const params = {
      ...formData,
      // 固定筛选条件：重点关注
      processingStatus: '2', // 重点关注
      // 处理日期范围
      entryDateStart: entryDateRange && entryDateRange.length > 0 ? entryDateRange[0] : undefined,
      entryDateEnd: entryDateRange && entryDateRange.length > 1 ? entryDateRange[1] : undefined,
      focusDateStart: focusDateRange && focusDateRange.length > 0 ? focusDateRange[0] : undefined,
      focusDateEnd: focusDateRange && focusDateRange.length > 1 ? focusDateRange[1] : undefined,
      // 导出所有数据
      page: 1,
      size: 10000
    }

    await exportPersonnelData(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 导出调岗/劝退人员数据
const handleExportTransfer = async () => {
  try {
    exportLoading.value = true
    const { entryDateRange, focusDateRange, ...formData } = searchForm
    const params = {
      ...formData,
      // 固定筛选条件：调岗/劝退
      processingStatus: '3', // 调岗/劝退
      // 处理日期范围
      entryDateStart: entryDateRange && entryDateRange.length > 0 ? entryDateRange[0] : undefined,
      entryDateEnd: entryDateRange && entryDateRange.length > 1 ? entryDateRange[1] : undefined,
      focusDateStart: focusDateRange && focusDateRange.length > 0 ? focusDateRange[0] : undefined,
      focusDateEnd: focusDateRange && focusDateRange.length > 1 ? focusDateRange[1] : undefined,
      // 导出所有数据
      page: 1,
      size: 10000
    }

    await exportPersonnelData(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 重点关注分页
const handleFocusSizeChange = (size: number) => {
  focusPagination.size = size
  focusPagination.page = 1
  fetchFocusPersonnel()
}

const handleFocusCurrentChange = (page: number) => {
  focusPagination.page = page
  fetchFocusPersonnel()
}

// 调岗/劝退分页
const handleTransferSizeChange = (size: number) => {
  transferPagination.size = size
  transferPagination.page = 1
  fetchTransferPersonnel()
}

const handleTransferCurrentChange = (page: number) => {
  transferPagination.page = page
  fetchTransferPersonnel()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchFocusPersonnel()
})
</script>

<style scoped>
.focus-personnel {
  background-color: #f5f7fa;
  animation: fadeInUp 0.5s ease-out;
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.search-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 标签页卡片样式 */
.tabs-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
}

.search-section {
  margin-bottom: 20px;
  padding: 10px;
  /* background: #fafafa; */
  border-radius: 6px;
}

.tab-header {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
}

.tab-subtitle {
  font-size: 14px;
  color: white;
  font-weight: 500;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.type-tag {
  margin-right: 4px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Element Plus 标签页样式覆盖 */


:deep(.el-tabs__content) {
  padding: 0;
}
</style>
