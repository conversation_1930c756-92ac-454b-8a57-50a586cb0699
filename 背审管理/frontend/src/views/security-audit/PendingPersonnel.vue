<template>
  <div class="pending-personnel">
    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <h3 class="search-title">
          <el-icon><Search /></el-icon>
          搜索筛选
        </h3>
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch" :loading="loading" class="action-btn">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset" class="action-btn">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleExport" :loading="exportLoading" class="action-btn">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>

      <div class="search-container">
        <el-form :model="searchForm" class="search-form" label-width="100px">
          <!-- 第一行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名">
                <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证号">
                <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属单位">
                <el-input v-model="searchForm.organization" placeholder="请输入所属单位" clearable />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="所属行业">
                <el-select 
                  v-model="searchForm.industry" 
                  placeholder="请选择所属行业" 
                  multiple
                  clearable 
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in industryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="区域">
                <el-input v-model="searchForm.region" placeholder="请输入区域" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="异常类型">
                <el-select 
                  v-model="searchForm.abnormalTypes" 
                  placeholder="请选择异常类型" 
                  multiple
                  clearable 
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in abnormalTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="入职时间">
                <el-date-picker
                  v-model="searchForm.entryDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                  :shortcuts="entryDateShortcuts"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <!-- 人员类型标签页和数据表格 -->
    <el-card class="tabs-card" shadow="never">
      <div class="tabs-header">
        <div class="tabs-nav">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部人员" name="all"></el-tab-pane>
            <el-tab-pane label="专职保卫" name="type1"></el-tab-pane>
            <el-tab-pane label="保安人员" name="type2"></el-tab-pane>
          </el-tabs>
        </div>
       
      </div>

      <div class="tab-content">
        <PersonnelTable
          :data="tableData"
          :loading="loading"
          @view-detail="handleViewDetail"
          @add-blacklist="handleAddBlacklist"
        />
      </div>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 人员详情弹窗 -->
    <PersonnelDetailDialog
      v-model="detailDialogVisible"
      :personnel-id="selectedPersonnelId"
      @edit="handleEditFromDetail"
    />

    <!-- 编辑数据弹窗 -->
    <PersonnelEditDialog
      v-model="editDialogVisible"
      :personnel-id="selectedPersonnelId"
      @success="handleEditSuccess"
    />

    <!-- 人员处理抽屉 -->
    <PersonnelProcessingDrawer
      v-model="processingDrawerVisible"
      :personnel-id="selectedPersonnelId"
      @processing-completed="handleProcessingCompleted"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, Check } from '@element-plus/icons-vue'
import PersonnelTable from '@/components/background-check/PersonnelTable.vue'
import PersonnelDetailDialog from '@/components/background-check/PersonnelDetailDialog.vue'
import PersonnelEditDialog from '@/components/background-check/PersonnelEditDialog.vue'
import PersonnelProcessingDrawer from '@/components/background-check/PersonnelProcessingDrawer.vue'
import { getPersonnelList, exportPersonnelData, batchUpdateNormalPersonnelStatus } from '@/api/background-check'
import { industryTypes, backgroundCheckAbnormalTypes } from '@/data/personnelMockData'

// 行业选项
const industryOptions = industryTypes

// 异常类型选项
const abnormalTypeOptions = backgroundCheckAbnormalTypes

// 搜索表单
const searchForm = reactive({
  name: '',
  idCard: '',
  organization: '',
  industry: [] as string[],
  region: '',
  abnormalTypes: [] as string[],
  entryDateRange: [] as string[]
})

// 入职时间快捷选项
const entryDateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '最近半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
      return [start, end]
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const exportLoading = ref(false)
const batchUpdateLoading = ref(false)

// 标签页
const activeTab = ref('all')

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗状态
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const processingDrawerVisible = ref(false)
const selectedPersonnelId = ref<number | null>(null)

// 获取人员列表
const fetchPersonnelList = async () => {
  try {
    loading.value = true
    const { entryDateRange, ...formData } = searchForm
    const params = {
      ...formData,
      // 固定筛选条件：异常且未处理
      backgroundCheckResult: '2', // 异常
      processingStatus: '0', // 未处理
      personnelType: activeTab.value === 'all' ? undefined : Number(activeTab.value.replace('type', '')),
      // 处理日期范围
      entryDateStart: entryDateRange && entryDateRange.length > 0 ? entryDateRange[0] : undefined,
      entryDateEnd: entryDateRange && entryDateRange.length > 1 ? entryDateRange[1] : undefined,
      page: pagination.page,
      size: pagination.size
    }

    const response = await getPersonnelList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchPersonnelList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    idCard: '',
    organization: '',
    industry: [],
    region: '',
    abnormalTypes: [],
    entryDateRange: []
  })
  pagination.page = 1
  fetchPersonnelList()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const params = {
      ...searchForm,
      backgroundCheckResult: '2',
      processingStatus: '0'
    }
    await exportPersonnelData(params)
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  } finally {
    exportLoading.value = false
  }
}

// 查看详情
const handleViewDetail = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  detailDialogVisible.value = true
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  pagination.page = 1
  fetchPersonnelList()
}

// 批量更新正常人员状态
const handleBatchUpdateNormalStatus = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将把所有背景审查结果为"正常"的人员处置状态设置为"无需处理"，是否继续？',
      '批量处理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    batchUpdateLoading.value = true
    const response = await batchUpdateNormalPersonnelStatus()
    ElMessage.success(response.data.message)

    // 刷新列表
    fetchPersonnelList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新失败:', error)
      ElMessage.error('批量更新失败')
    }
  } finally {
    batchUpdateLoading.value = false
  }
}

// 从详情页编辑
const handleEditFromDetail = (personnelId: number) => {
  // 关闭详情弹窗
  detailDialogVisible.value = false
  // 打开编辑弹窗
  selectedPersonnelId.value = personnelId
  editDialogVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  editDialogVisible.value = false
  ElMessage.success('编辑成功')
  // 刷新列表
  fetchPersonnelList()
}

// 处理异常人员
const handleAddBlacklist = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  processingDrawerVisible.value = true
}

// 处理完成回调
const handleProcessingCompleted = () => {
  fetchPersonnelList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchPersonnelList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchPersonnelList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchPersonnelList()
})
</script>

<style scoped>
.pending-personnel {
  background-color: #f5f7fa;
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.search-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.search-container {
  /* background: #fafafa; */
  padding: 10px;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 16px;
}

/* 标签页卡片样式 */
.tabs-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
}

.tabs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.tabs-nav {
  flex: 1;
}

.tabs-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
}

.tab-content {
  padding: 20px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 动画效果 */
.pending-personnel {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
