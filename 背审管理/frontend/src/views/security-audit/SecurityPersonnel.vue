<template>
  <div class="data-management">
    <!-- 搜索和筛选区域 -->
    <SearchForm
      v-model="searchForm"
      :form-config="searchFormConfig"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
      @field-change="handleFieldChange"
    >
      <template #extra-actions>
        <el-button type="primary" @click="handleExport" :loading="exportLoading" class="action-btn">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>
    </SearchForm>

    <!-- 人员类型标签页和操作按钮 -->
    <el-card class="tabs-card" shadow="never">
      <div class="tabs-header">
        <div class="tabs-nav">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部人员" name="all"></el-tab-pane>
            <el-tab-pane label="专职保卫" name="type1"></el-tab-pane>
            <el-tab-pane label="保安人员" name="type2"></el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <div class="tab-content">
        <PersonnelTable
          :data="tableData"
          :loading="loading"
          @view-detail="handleViewDetail"
          @add-blacklist="handleAddBlacklist"
        />
      </div>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 人员详情弹窗 -->
    <PersonnelDetailDialog
      v-model="detailDialogVisible"
      :personnel-id="selectedPersonnelId"
      @edit="handleEditFromDetail"
    />

    <!-- 编辑数据弹窗 -->
    <PersonnelEditDialog 
      v-model="editDialogVisible"
      :personnel-id="selectedPersonnelId"
      @success="handleEditSuccess"
    />

    <!-- 异常处理抽屉 -->
    <PersonnelProcessingDrawer
      v-model="processingDrawerVisible"
      :personnel-id="selectedPersonnelId"
      @processing-completed="handleProcessingCompleted"
    />

    <!-- 发送通知弹窗 -->
    <NoticeDialog 
      v-model="noticeDialogVisible"
      :personnel-id="selectedPersonnelId"
      @success="handleNoticeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import SearchForm from '@/components/common/SearchForm.vue'
import PersonnelTable from '@/components/background-check/PersonnelTable.vue'
import PersonnelDetailDialog from '@/components/background-check/PersonnelDetailDialog.vue'
import PersonnelEditDialog from '@/components/background-check/PersonnelEditDialog.vue'
import PersonnelProcessingDrawer from '@/components/background-check/PersonnelProcessingDrawer.vue'
import NoticeDialog from '@/components/background-check/NoticeDialog.vue'
import { mockApi } from '@/utils/mockData'
import { backgroundCheckAbnormalTypes, industryTypes } from '@/data/personnelMockData'
import { createPersonnelSearchConfig } from '@/config/personnelSearchConfig'
import type { FieldConfig } from '@/types/searchForm'

// 模拟API函数
const getPersonnelList = (params: any) => {
  return mockApi.getPersonnelList(params)
}

const exportPersonnelData = (params: any) => {
  return mockApi.exportPersonnelData(params)
}

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const activeTab = ref('all')
const tableData = ref([])
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const processingDrawerVisible = ref(false)
const noticeDialogVisible = ref(false)
const selectedPersonnelId = ref<number | null>(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  idCard: '',
  organization: '',
  industry: [] as string[],
  region: '',
  status: '',
  backgroundCheckResult: '',
  abnormalTypes: [] as string[],
  processingStatus: '',
  entryDateRange: [] as string[]
})

// 异常类型选项
const abnormalTypeOptions = ref(backgroundCheckAbnormalTypes)

// 行业类型选项
const industryOptions = ref(industryTypes)

// 搜索表单配置
const searchFormConfig = computed(() => {
  return createPersonnelSearchConfig(industryOptions.value, abnormalTypeOptions.value)
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 获取人员列表
const fetchPersonnelList = async () => {
  try {
    loading.value = true
    const { entryDateRange, ...formData } = searchForm
    const params = {
      ...formData,
      personnelType: activeTab.value === 'all' ? undefined : Number(activeTab.value.replace('type', '')),
      // 处理日期范围
      entryDateStart: entryDateRange && entryDateRange.length > 0 ? entryDateRange[0] : undefined,
      entryDateEnd: entryDateRange && entryDateRange.length > 1 ? entryDateRange[1] : undefined,
      page: pagination.page,
      size: pagination.size
    }

    const response = await getPersonnelList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = (formData?: Record<string, any>) => {
  // 如果传递了 formData，更新 searchForm
  if (formData) {
    Object.assign(searchForm, formData)
  }
  pagination.page = 1
  fetchPersonnelList()
}

// 重置
const handleReset = () => {
  pagination.page = 1
  fetchPersonnelList()
}

// 字段变化处理
const handleFieldChange = (field: FieldConfig, value: any, formData: any) => {
  // 这里可以处理特定字段的变化逻辑
  console.log('Field changed:', field.key, value)
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  pagination.page = 1
  fetchPersonnelList()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const params = {
      ...searchForm,
      personnelType: activeTab.value === 'all' ? '' : activeTab.value.replace('type', '')
    }
    
    await exportPersonnelData(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 查看详情
const handleViewDetail = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  detailDialogVisible.value = true
}

// 从详情页面编辑数据
const handleEditFromDetail = (personnelId: number) => {
  // 关闭详情弹窗
  detailDialogVisible.value = false
  // 打开编辑弹窗
  selectedPersonnelId.value = personnelId
  editDialogVisible.value = true
}

// 编辑数据
const handleEditData = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  editDialogVisible.value = true
}

// 异常处理
const handleAddBlacklist = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  processingDrawerVisible.value = true
}

// 发送通知
const handleSendNotice = (personnelId: number) => {
  selectedPersonnelId.value = personnelId
  noticeDialogVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  editDialogVisible.value = false
  fetchPersonnelList()
}

// 异常处理完成回调
const handleProcessingCompleted = () => {
  // 不关闭抽屉，只刷新列表数据
  fetchPersonnelList()
}

// 通知发送成功回调
const handleNoticeSuccess = () => {
  noticeDialogVisible.value = false
  ElMessage.success('通知发送成功')
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchPersonnelList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchPersonnelList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchPersonnelList()
})
</script>

<style scoped>
.data-management {
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 导出按钮样式 */
.action-btn {
  min-width: 88px;
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
}

/* 标签页卡片样式 */
.tabs-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tabs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tabs-nav {
  flex: 1;
}

.tab-content {
  padding-top: 20px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  /* margin-top: 24px; */
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Element Plus 组件样式覆盖 */
:deep(.el-tabs__header) {
  margin: 0 !important;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-button) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #337ecc 0%, #5aa3e6 100%);
}

:deep(.el-drawer__header) {
  margin-bottom: 0px !important;
}

/* 动画效果 */
.tabs-card,
.pagination-wrapper {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>