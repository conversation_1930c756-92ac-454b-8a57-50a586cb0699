<template>
  <div class="data-management">
    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-header">
        <h3 class="search-title">
          <el-icon><Search /></el-icon>
          搜索筛选
        </h3>
        <div class="search-actions">
          <el-button type="primary" @click="handleSearch" :loading="loading" class="action-btn">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset" class="action-btn">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleExport" :loading="exportLoading" class="action-btn">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>

      <div class="search-container">
        <el-form :model="searchForm" class="search-form" label-width="100px">
          <!-- 第一行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名">
                <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证号">
                <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属单位">
                <el-input v-model="searchForm.organization" placeholder="请输入所属单位" clearable />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="所属行业">
                <el-select
                  v-model="searchForm.industry"
                  placeholder="请选择所属行业"
                  multiple
                  clearable
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in industryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="区域">
                <el-select v-model="searchForm.region" placeholder="请选择区域" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="竞秀区" value="竞秀区" />
                  <el-option label="莲池区" value="莲池区" />
                  <el-option label="满城区" value="满城区" />
                  <el-option label="清苑区" value="清苑区" />
                  <el-option label="徐水区" value="徐水区" />
                  <el-option label="涞水县" value="涞水县" />
                  <el-option label="阜平县" value="阜平县" />
                  <el-option label="定兴县" value="定兴县" />
                  <el-option label="唐县" value="唐县" />
                  <el-option label="高阳县" value="高阳县" />
                  <el-option label="容城县" value="容城县" />
                  <el-option label="涞源县" value="涞源县" />
                  <el-option label="望都县" value="望都县" />
                  <el-option label="安新县" value="安新县" />
                  <el-option label="易县" value="易县" />
                  <el-option label="曲阳县" value="曲阳县" />
                  <el-option label="蠡县" value="蠡县" />
                  <el-option label="顺平县" value="顺平县" />
                  <el-option label="博野县" value="博野县" />
                  <el-option label="雄县" value="雄县" />
                  <el-option label="高碑店市" value="高碑店市" />
                  <el-option label="安国市" value="安国市" />
                  <el-option label="定州市" value="定州市" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="人员状态">
                <el-select v-model="searchForm.status" placeholder="请选择人员状态" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="在职" value="1" />
                  <el-option label="离职" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="背审结果">
                <el-select v-model="searchForm.backgroundCheckResult" placeholder="请选择背景审查结果" clearable style="width: 100%" @change="handleBackgroundCheckResultChange">
                  <el-option label="全部" value="" />
                  <el-option label="未审查" value="0" />
                  <el-option label="正常" value="1" />
                  <el-option label="异常" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="searchForm.backgroundCheckResult === '2'">
              <el-form-item label="异常类型">
                <el-select
                  v-model="searchForm.abnormalTypes"
                  placeholder="请选择异常类型，默认全选"
                  multiple
                  clearable
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="item in abnormalTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

          </el-row>

          <!-- 第四行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="入职时间">
                <el-date-picker
                  v-model="searchForm.entryDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                  :shortcuts="entryDateShortcuts"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <!-- 人员类型标签页和数据表格 -->
    <el-card class="tabs-card" shadow="never">
      <div class="tabs-header">
        <div class="tabs-nav">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部人员" name="all"></el-tab-pane>
            <el-tab-pane label="教师" name="type1"></el-tab-pane>
            <el-tab-pane label="行政人员" name="type2"></el-tab-pane>
            <el-tab-pane label="后勤人员" name="type3"></el-tab-pane>
            <el-tab-pane label="其他人员" name="type4"></el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <div class="tab-content">
        <!-- 数据表格 -->
        <div class="personnel-table">
          <el-table :data="tableData" :loading="loading" stripe border>
            <el-table-column prop="region" label="区域" width="150" />
            <el-table-column prop="name" label="姓名" width="100" />
            <el-table-column prop="idCard" label="身份证号" width="200" />
            <el-table-column prop="phone" label="手机号" width="130" />
            <el-table-column prop="organization" label="所属单位" />
            <el-table-column prop="entryDate" label="入职日期" width="120" />

            <!-- 在职状态 -->
            <el-table-column prop="status" label="在职状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getEmploymentStatusType(row.status)">
                  {{ getEmploymentStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- 背审结果 -->
            <el-table-column prop="backgroundCheckResult" label="背审结果" width="150">
              <template #default="{ row }">
                <!-- 未审查或正常状态显示主标签 -->
                <el-tag
                  v-if="row.backgroundCheckResult !== 2"
                  :type="getBackgroundCheckResultType(row.backgroundCheckResult)"
                >
                  {{ getBackgroundCheckResultText(row.backgroundCheckResult) }}
                </el-tag>

                <!-- 异常状态直接显示异常类型标签 -->
                <el-tag
                  v-else-if="row.abnormalTypes && row.abnormalTypes.length > 0"
                  type="danger"
                >
                  {{ getSingleAbnormalTypeText(row.abnormalTypes[0]) }}
                </el-tag>

                <!-- 异常但没有具体类型时显示异常标签 -->
                <el-tag
                  v-else
                  type="danger"
                >
                  异常
                </el-tag>
              </template>
            </el-table-column>


          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import {
  backgroundCheckAbnormalTypes,
  industryTypes,
  getBackgroundCheckResultText,
  getSingleAbnormalTypeText,
  getEmploymentStatusText,
  getBackgroundCheckResultType,
  getEmploymentStatusType
} from '@/data/personnelMockData'

// 模拟API函数
const getEducationPersonnelList = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = generateEducationMockData(params)
      resolve({
        data: {
          records: mockData.records,
          total: mockData.total
        }
      })
    }, 500)
  })
}

const exportEducationPersonnelData = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      ElMessage.success('教职工数据导出成功')
      resolve(true)
    }, 1000)
  })
}

// 生成教职工模拟数据
const generateEducationMockData = (params: any) => {
  const educationOrganizations = [
    '保定市第一中学', '保定市第二中学', '保定市第三中学',
    '保定市实验小学', '保定市师范附属小学', '保定市育德中学',
    '竞秀区第一小学', '莲池区实验中学', '满城区中学',
    '清苑区第一中学', '徐水区实验小学', '涞水县中学'
  ]

  const educationNames = [
    '李老师', '王老师', '张老师', '刘老师', '陈老师', '赵老师',
    '孙老师', '周老师', '吴老师', '郑老师', '冯老师', '蒋老师'
  ]

  const regions = ['竞秀区', '莲池区', '满城区', '清苑区', '徐水区', '涞水县']

  const records = []
  const pageSize = params.size || 10
  const currentPage = params.page || 1
  const totalRecords = 234 // 模拟总数

  for (let i = 0; i < pageSize; i++) {
    const index = (currentPage - 1) * pageSize + i
    if (index >= totalRecords) break

    const backgroundCheckResult = Math.random() > 0.85 ? 2 : Math.random() > 0.5 ? 1 : 0
    const abnormalTypes = backgroundCheckResult === 2 ?
      [backgroundCheckAbnormalTypes[Math.floor(Math.random() * backgroundCheckAbnormalTypes.length)].value] : []

    records.push({
      id: index + 1,
      name: educationNames[Math.floor(Math.random() * educationNames.length)],
      idCard: `130602${1980 + Math.floor(Math.random() * 30)}${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}${String(Math.floor(Math.random() * 9999)).padStart(4, '0')}`,
      phone: `138${String(Math.floor(Math.random() * 99999999)).padStart(8, '0')}`,
      organization: educationOrganizations[Math.floor(Math.random() * educationOrganizations.length)],
      region: regions[Math.floor(Math.random() * regions.length)],
      entryDate: `${2018 + Math.floor(Math.random() * 6)}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      status: Math.random() > 0.05 ? 1 : 2,
      backgroundCheckResult,
      abnormalTypes,
      processingStatus: backgroundCheckResult === 2 ? Math.floor(Math.random() * 4) : 1,
      industry: 'education'
    })
  }

  return {
    records,
    total: totalRecords
  }
}

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const activeTab = ref('all')
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  idCard: '',
  organization: '',
  industry: [] as string[],
  region: '',
  status: '',
  backgroundCheckResult: '',
  abnormalTypes: [] as string[],
  entryDateRange: [] as string[]
})

// 入职时间快捷选项
const entryDateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '最近半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
      return [start, end]
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  }
]

// 异常类型选项
const abnormalTypeOptions = ref(backgroundCheckAbnormalTypes)

// 行业类型选项
const industryOptions = ref(industryTypes)

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 获取教职工列表
const fetchEducationPersonnelList = async () => {
  try {
    loading.value = true
    const { entryDateRange, ...formData } = searchForm
    const params = {
      ...formData,
      personnelType: activeTab.value === 'all' ? undefined : Number(activeTab.value.replace('type', '')),
      entryDateStart: entryDateRange && entryDateRange.length > 0 ? entryDateRange[0] : undefined,
      entryDateEnd: entryDateRange && entryDateRange.length > 1 ? entryDateRange[1] : undefined,
      page: pagination.page,
      size: pagination.size
    }

    const response = await getEducationPersonnelList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取教职工列表失败:', error)
    ElMessage.error('获取教职工列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchEducationPersonnelList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    idCard: '',
    organization: '',
    industry: [],
    region: '',
    status: '',
    backgroundCheckResult: '',
    abnormalTypes: [],
    entryDateRange: []
  })
  pagination.page = 1
  fetchEducationPersonnelList()
}

// 背景审查结果变化处理
const handleBackgroundCheckResultChange = (value: string) => {
  if (value !== '2') {
    searchForm.abnormalTypes = []
  }
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  pagination.page = 1
  fetchEducationPersonnelList()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const params = {
      ...searchForm,
      personnelType: activeTab.value === 'all' ? '' : activeTab.value.replace('type', '')
    }

    await exportEducationPersonnelData(params)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchEducationPersonnelList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchEducationPersonnelList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchEducationPersonnelList()
})
</script>

<style scoped>
.data-management {
  padding: 0;
}

.search-card {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
}

.search-container {
  background: #fafafa;
  padding: 20px;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.tabs-card {
  border: 1px solid #e4e7ed;
}

.tabs-header {
  margin-bottom: 16px;
}

.tabs-nav {
  border-bottom: 1px solid #e4e7ed;
}

.tab-content {
  padding: 0;
}

.personnel-table {
  margin-top: 16px;
}



.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__active-bar) {
  background-color: #409eff;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-card__body) {
  padding: 20px;
}

/* 过渡动画已在全局样式中禁用 */
</style>
