<template>
  <div class="dashboard">
    <!-- 顶部区域：统计卡片 + 快捷操作 -->
    <div class="top-section">
      <el-row :gutter="20">
        <!-- 左侧：数据统计卡片 -->
        <el-col :span="10">
          <el-card class="stats-container-card">
            <template #header>
              <div class="card-header">
                <span>数据统计</span>
              </div>
            </template>
            <div class="stats-cards">
              <el-row :gutter="20">
                <el-col :span="6">
                  <div class="stats-item total">
                    <div class="stats-icon">
                      <el-icon size="24" color="#409eff"><User /></el-icon>
                    </div>
                    <div class="stats-label">安保人员</div>
                    <div class="stats-number">{{ statsData.totalPersonnel }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stats-item abnormal">
                    <div class="stats-icon">
                      <el-icon size="24" color="#f56c6c"><Warning /></el-icon>
                    </div>
                    <div class="stats-label">异常人员</div>
                    <div class="stats-number">{{ statsData.abnormalPersonnel }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stats-item pending">
                    <div class="stats-icon">
                      <el-icon size="24" color="#e6a23c"><Clock /></el-icon>
                    </div>
                    <div class="stats-label">待处理人员</div>
                    <div class="stats-number">{{ statsData.pendingPersonnel }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stats-item focus">
                    <div class="stats-icon">
                      <el-icon size="24" color="#67c23a"><View /></el-icon>
                    </div>
                    <div class="stats-label">关注人员</div>
                    <div class="stats-number">{{ statsData.focusPersonnel }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：快捷操作 -->
        <el-col :span="14">
          <el-card class="quick-actions-card">
            <template #header>
              <div class="card-header">
                <span>快捷操作</span>
              </div>
            </template>
            <div class="quick-actions-grid">
              <div
                v-for="action in quickActions"
                :key="action.name"
                class="quick-action-item"
                @click="handleQuickAction(action.path)"
              >
                <div class="action-icon" :style="{ backgroundColor: action.color }">
                  <el-icon size="20" color="white">
                    <component :is="action.icon" />
                  </el-icon>
                </div>
                <div class="action-name">{{ action.name }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 底部区域：图表 + 表格 -->
    <div class="bottom-section">
      <el-row :gutter="20">
        <!-- 左侧：图表区域 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>异常人员趋势分析</span>
                <span class="chart-subtitle">最近6个月</span>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="chartOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：表格区域 -->
        <el-col :span="12">
          <el-card class="table-card">
            <template #header>
              <div class="card-header">
                <span>待处理异常人员</span>
                <el-button type="primary" @click="$router.push('/pending-personnel')">
                  查看全部
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="table-container">
              <el-table :data="pendingList" :loading="pendingLoading" stripe size="small">
                <el-table-column prop="name" label="姓名" width="80" />
                <el-table-column prop="organization" label="所属单位" show-overflow-tooltip />
                <el-table-column prop="abnormalTypes" label="异常类型" width="120">
                  <template #default="{ row }">
                    <el-tag
                      v-for="type in row.abnormalTypes"
                      :key="type"
                      type="danger"
                      size="small"
                      class="type-tag"
                    >
                      {{ getSingleAbnormalTypeText(type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="handleViewDetail(row.id)">
                      查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="pagination.page"
                  v-model:page-size="pagination.size"
                  :page-sizes="[5, 10]"
                  :total="pagination.total"
                  layout="total, prev, pager, next"
                  small
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  User,
  Warning,
  Clock,
  View,
  ArrowRight,
  Document,
  DataAnalysis,
  Setting
} from '@element-plus/icons-vue'
import { getSingleAbnormalTypeText } from '@/data/personnelMockData'
import { getPersonnelList } from '@/api/background-check'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const router = useRouter()

// 统计数据
const statsData = ref({
  totalPersonnel: 0,
  abnormalPersonnel: 0,
  pendingPersonnel: 0,
  focusPersonnel: 0
})

// 快捷功能
const quickActions = [
  { name: '人员管理', path: '/security-personnel', icon: User, color: '#409eff' },
  { name: '待处理', path: '/pending-personnel', icon: Warning, color: '#f56c6c' },
  { name: '关注人员', path: '/focus-personnel', icon: View, color: '#e6a23c' },
  { name: '数据看板', path: '/data-dashboard', icon: DataAnalysis, color: '#67c23a' },
  { name: '统计报表', path: '/statistical-reports', icon: Document, color: '#909399' },
  { name: '系统设置', path: '/settings', icon: Setting, color: '#606266' }
]

// 图表相关
const chartLoading = ref(false)

// 图表数据
const chartData = ref({
  months: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'],
  currentYear: [12, 8, 15, 22, 18, 25],
  lastYear: [10, 6, 12, 18, 15, 20]
})

// 图表配置
const chartOption = computed(() => ({
  title: {
    text: '异常人员检测趋势',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#303133'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['本年度', '去年同期'],
    top: 30,
    textStyle: {
      color: '#303133'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: chartData.value.months,
    axisLine: {
      lineStyle: {
        color: '#dcdfe6'
      }
    },
    axisLabel: {
      color: '#606266'
    }
  },
  yAxis: {
    type: 'value',
    name: '人数',
    nameTextStyle: {
      color: '#606266'
    },
    axisLine: {
      lineStyle: {
        color: '#dcdfe6'
      }
    },
    axisLabel: {
      color: '#606266'
    },
    splitLine: {
      lineStyle: {
        color: '#f0f0f0'
      }
    }
  },
  series: [
    {
      name: '本年度',
      type: 'line',
      data: chartData.value.currentYear,
      smooth: true,
      lineStyle: {
        color: '#667eea',
        width: 3
      },
      itemStyle: {
        color: '#667eea'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(102, 126, 234, 0.3)' },
            { offset: 1, color: 'rgba(102, 126, 234, 0.1)' }
          ]
        }
      }
    },
    {
      name: '去年同期',
      type: 'line',
      data: chartData.value.lastYear,
      smooth: true,
      lineStyle: {
        color: '#f56c6c',
        width: 2,
        type: 'dashed'
      },
      itemStyle: {
        color: '#f56c6c'
      }
    }
  ]
}))

// 分页数据
const pagination = ref({
  page: 1,
  size: 5,
  total: 0
})

// 待处理人员列表
const pendingList = ref([])
const pendingLoading = ref(false)



// 获取统计数据
const fetchStatsData = async () => {
  try {
    // 获取总人员数据
    const totalResponse = await getPersonnelList({ current: 1, size: 1 })
    statsData.value.totalPersonnel = totalResponse.data.total

    // 获取异常人员数据
    const abnormalResponse = await getPersonnelList({ 
      current: 1, 
      size: 1, 
      backgroundCheckResult: '2' 
    })
    statsData.value.abnormalPersonnel = abnormalResponse.data.total

    // 获取待处理人员数据
    const pendingResponse = await getPersonnelList({ 
      current: 1, 
      size: 1, 
      backgroundCheckResult: '2',
      processingStatus: '0'
    })
    statsData.value.pendingPersonnel = pendingResponse.data.total

    // 获取关注人员数据
    const focusResponse = await getPersonnelList({ 
      current: 1, 
      size: 1, 
      processingStatus: '2,3'
    })
    statsData.value.focusPersonnel = focusResponse.data.total
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取待处理人员列表
const fetchPendingList = async () => {
  try {
    pendingLoading.value = true
    const response = await getPersonnelList({
      current: 1,
      size: 5,
      backgroundCheckResult: '2',
      processingStatus: '0'
    })
    pendingList.value = response.data.records
  } catch (error) {
    console.error('获取待处理人员列表失败:', error)
  } finally {
    pendingLoading.value = false
  }
}

// 快捷功能点击
const handleQuickAction = (path: string) => {
  router.push(path)
}

// 查看详情
const handleViewDetail = (id: number) => {
  router.push(`/pending-personnel?id=${id}`)
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchPendingList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchPendingList()
}

onMounted(() => {
  fetchStatsData()
  fetchPendingList()
})
</script>

<style scoped>
.dashboard {
  background-color: #f5f7fa;
  /* min-height: 100vh; */
  animation: fadeInUp 0.6s ease-out;
  padding: 20px;
}

/* 顶部区域 */
.top-section {
  margin-bottom: 20px;
}

/* 统计容器卡片 */
.stats-container-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: none;
  height: 280px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 0;
  height: 100%;
  display: flex;
  align-items: stretch;
}

.stats-cards .el-row {
  width: 100%;
  height: 100%;
  margin: 0 !important;
}

.stats-cards .el-col {
  height: 100%;
  display: flex;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 24px 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  height: 100%;
  width: 100%;
  gap: 12px;
  position: relative;
  overflow: hidden;
}

.stats-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--stats-color);
  border-radius: 12px 12px 0 0;
}

.stats-item:hover {
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f0ff 100%);
  border-color: var(--stats-color);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stats-item.total {
  --stats-color: #409eff;
}

.stats-item.abnormal {
  --stats-color: #f56c6c;
}

.stats-item.pending {
  --stats-color: #e6a23c;
}

.stats-item.focus {
  --stats-color: #67c23a;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 3px solid var(--stats-color);
}

.stats-label {
  font-size: 16px;
  color: #606266;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

/* 快捷操作 */
.quick-actions-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: none;
  height: 280px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  height: 100%;
  align-content: space-around;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  height: 80px;
  justify-content: center;
}

.quick-action-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.action-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-name {
  font-size: 11px;
  color: #303133;
  font-weight: 500;
  text-align: center;
}

/* 底部区域 */
.bottom-section {
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: none;
  height: 450px;
}

.chart-subtitle {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.chart-container {
  height: 350px;
  padding: 10px 0;
}

.chart {
  height: 100%;
  width: 100%;
}

.table-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: none;
  height: 450px;
}

.table-container {
  height: 350px;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
  width: 100%;
}

.type-tag {
  margin-right: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding: 12px 0;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__body) {
  padding: 16px;
  height: calc(100% - 60px);
}

:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #dfe3fb;
  height: 60px;
  background-color: white;
  display: flex;
  align-items: center;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  flex: 1;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
  padding: 8px 0;
}

:deep(.el-table td) {
  padding: 6px 0;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .dashboard {
    padding: 16px;
  }

  .stats-container-card,
  .quick-actions-card,
  .chart-card,
  .table-card {
    height: 260px;
  }

  .chart-container {
    height: 320px;
  }

  .table-container {
    height: 320px;
  }
}

@media (max-width: 1200px) {
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 12px;
  }

  .top-section .el-col:first-child {
    margin-bottom: 16px;
  }

  .bottom-section .el-col:first-child {
    margin-bottom: 16px;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .action-name {
    font-size: 10px;
  }

  .chart-container {
    height: 250px;
  }

  .table-container {
    height: 250px;
  }
}
</style>
