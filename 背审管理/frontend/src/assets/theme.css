:root {
  /**
 * 字体
 */
  --el-font-size-base: 15px !important;
  --el-tag-font-size: 14px !important;

  /* 公安蓝主题色 - 使用更深沉的蓝色 */
  --el-color-primary: #0d2f7e;
  --el-color-primary-light-3: #2c4a8c;
  --el-color-primary-light-5: #4c689b;
  --el-color-primary-light-7: #7185aa;
  --el-color-primary-light-8: #8999b7;
  --el-color-primary-light-9: #a6b1c5;
  --el-color-primary-dark-2: #0a2665;

  /* 系统特定颜色 */
  --system-header-bg: #0d2f7e;
  --system-sidebar-bg: #14295a;
  --system-sidebar-active: #0a2665;
  /* --system-card-header: #c7d8fb; */
  --system-card-border: #d0d7e5;

  /* 边框颜色 */
  --el-border-color: #d0d7e5;
  --el-border-color-light: #e4e7ed;

  /* 标签颜色 */
  --el-tag-info-bg-color: #f0f3fa;
  --el-tag-info-border-color: #d0d7e5;

  /* 警告色和成功色也调整为蓝色系 */
  --el-color-warning: #3a65a8;
  --el-color-danger: #19376d;
  --el-color-success: #295ba7;
  --el-color-info: #5e7fbb;
}

/* 自定义组件样式 */
.el-button--primary {
  --el-button-bg-color: var(--el-color-primary);
  --el-button-border-color: var(--el-color-primary);
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
  --el-button-active-bg-color: var(--el-color-primary-dark-2);
  --el-button-active-border-color: var(--el-color-primary-dark-2);
}

/* 其他按钮类型也统一为蓝色系 */
.el-button--success,
.el-button--warning,
.el-button--danger,
.el-button--info {
  --el-button-text-color: #ffffff;
}

.el-button--success {
  --el-button-bg-color: #295ba7;
  --el-button-border-color: #295ba7;
  --el-button-hover-bg-color: #3a65a8;
  --el-button-hover-border-color: #3a65a8;
}

.el-button--warning {
  --el-button-bg-color: #3a65a8;
  --el-button-border-color: #3a65a8;
  --el-button-hover-bg-color: #4b70ad;
  --el-button-hover-border-color: #4b70ad;
}

.el-button--danger {
  --el-button-bg-color: #19376d;
  --el-button-border-color: #19376d;
  --el-button-hover-bg-color: #224586;
  --el-button-hover-border-color: #224586;
}

/* 导航菜单样式 */
.el-menu {
  --el-menu-bg-color: var(--system-sidebar-bg);
  --el-menu-text-color: #ffffff;
  --el-menu-hover-bg-color: rgba(255, 255, 255, 0.1);
  --el-menu-active-color: #ffffff;
  --el-menu-item-height: 50px;
  /* 更紧凑的菜单高度 */
}

.el-menu-item.is-active {
  background-color: var(--system-sidebar-active);
}

/* 头部导航样式 */
.app-header {
  background-color: var(--system-header-bg);
  color: #ffffff;
  height: 56px;
  /* 更紧凑的头部高度 */
}

/* 卡片样式 */
.el-card {
  --el-card-border-color: var(--system-card-border);
  margin-bottom: 15px;
  /* 更紧凑的卡片间距 */
}

.el-card__header {
  background-color: var(--system-card-header);
  padding: 12px 15px;
  /* 更紧凑的内边距 */
}

.el-card__body {
  padding: 15px;
  /* 更紧凑的内边距 */
}

/* 表格样式 */
.el-table th.el-table__cell {
  background-color: #e4eaf5;
  padding: 8px 0;
  /* 更紧凑的表头 */
}

.el-table td.el-table__cell {
  padding: 8px 0;
  /* 更紧凑的单元格 */
}

/* 标签样式 */
.el-tag--info {
  --el-tag-bg-color: var(--el-tag-info-bg-color);
  --el-tag-border-color: var(--el-tag-info-border-color);
  --el-tag-hover-bg-color: #e9e9eb;
}

/* 表单元素样式 - 更紧凑 */
.el-form-item {
  margin-bottom: 15px;
}

.el-form--inline .el-form-item {
  margin-right: 10px;
}

/* 使表单元素更紧凑，并默认采用行内布局 */
.el-form--inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.el-form--inline .el-form-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.el-form-item__label {
  font-weight: normal;
  padding-right: 8px;
}

.el-form-item__content {
  display: flex;
  align-items: center;
}

/* 使下拉框、输入框等表单元素更加紧凑 */
.el-input__inner,
.el-select__input,
.el-select-dropdown__item,
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  padding: 0 10px;
  height: 32px;
  line-height: 32px;
}

.el-input-number.is-controls-right .el-input__inner {
  padding-left: 10px;
  padding-right: 30px;
}

/* 表格样式更加紧凑 */
.el-table .cell {
  padding-left: 10px;
  padding-right: 10px;
}

/* 卡片样式更加紧凑 */
.el-card__body {
  padding: 15px;
}

/* 按钮样式紧凑化 */
.el-button {
  padding: 8px 12px;
}

.el-button--small {
  padding: 6px 10px;
  font-size: 12px;
}

/* 紧凑型分页 */
.el-pagination {
  margin-top: 15px;
  padding: 0;
}

/* 表格头部样式 */
.el-table th.el-table__cell {
  background-color: #e4eaf5;
  font-weight: bold;
  color: #0d2f7e;
}