// 背审结果处理模块配置

// 异常类型配置
export const abnormalTypeConfig = {
  'criminal_record': { 
    label: '犯罪记录', 
    level: 'critical', 
    color: 'danger',
    description: '存在犯罪记录或违法行为'
  },
  'credit_issue': { 
    label: '信用问题', 
    level: 'high', 
    color: 'warning',
    description: '个人征信存在不良记录'
  },
  'education_fraud': { 
    label: '学历造假', 
    level: 'medium', 
    color: 'warning',
    description: '学历信息虚假或无法验证'
  },
  'work_experience': { 
    label: '工作经历问题', 
    level: 'low', 
    color: 'info',
    description: '工作经历存在疑点或虚假'
  },
  'identity_issue': {
    label: '身份信息问题',
    level: 'high',
    color: 'warning',
    description: '身份证信息异常或无法验证'
  },
  'reference_problem': {
    label: '推荐人问题',
    level: 'medium',
    color: 'warning', 
    description: '推荐人信息虚假或存在问题'
  }
} as const

// 异常等级配置
export const abnormalLevelConfig = {
  'critical': { label: '严重', color: 'danger', priority: 4 },
  'high': { label: '高', color: 'warning', priority: 3 },
  'medium': { label: '中', color: 'warning', priority: 2 },
  'low': { label: '低', color: 'info', priority: 1 }
} as const

// 处理状态配置
export const processingStatusConfig = {
  'pending': { label: '待处理', color: 'info' },
  'assigned': { label: '已分配', color: 'primary' },
  'processing': { label: '处理中', color: 'warning' },
  'completed': { label: '已完成', color: 'success' },
  'rejected': { label: '已驳回', color: 'danger' }
} as const

// 任务状态配置
export const taskStatusConfig = {
  'pending': { label: '待分配', color: 'info' },
  'assigned': { label: '已分配', color: 'primary' },
  'in_progress': { label: '进行中', color: 'warning' },
  'completed': { label: '已完成', color: 'success' },
  'cancelled': { label: '已取消', color: 'info' }
} as const

// 优先级配置
export const priorityConfig = {
  'low': { label: '普通', color: 'info' },
  'medium': { label: '中等', color: 'warning' },
  'high': { label: '高', color: 'danger' },
  'urgent': { label: '紧急', color: 'danger' }
} as const

// 表格列配置
export const abnormalPersonnelColumns = [
  { prop: 'avatar', label: '照片', width: 80, slot: 'avatar' },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'idCard', label: '身份证号', width: 180, slot: 'idCard' },
  { prop: 'phone', label: '联系方式', width: 140 },
  { prop: 'organization', label: '所属机构', width: 150 },
  { prop: 'abnormalType', label: '异常类型', width: 120, slot: 'abnormalType' },
  { prop: 'abnormalLevel', label: '异常等级', width: 100, slot: 'abnormalLevel' },
  { prop: 'discoveredAt', label: '发现时间', width: 120, slot: 'discoveredAt' },
  { prop: 'processingStatus', label: '处理状态', width: 100, slot: 'processingStatus' },
  { prop: 'assignedToName', label: '处理人', width: 120 },
  { prop: 'actions', label: '操作', width: 150, slot: 'actions', fixed: 'right' }
]

export const processingTaskColumns = [
  { prop: 'taskNo', label: '任务编号', width: 140 },
  { prop: 'title', label: '任务标题', minWidth: 150 },
  { prop: 'personnelCount', label: '人员数量', width: 100, slot: 'personnelCount' },
  { prop: 'assignedToUserName', label: '处理人', width: 120 },
  { prop: 'createdAt', label: '创建时间', width: 120, slot: 'createdAt' },
  { prop: 'progress', label: '进度', width: 150, slot: 'progress' },
  { prop: 'status', label: '状态', width: 100, slot: 'status' },
  { prop: 'reminderCount', label: '催办次数', width: 100, slot: 'reminderCount' },
  { prop: 'dueDate', label: '截止时间', width: 120, slot: 'dueDate' },
  { prop: 'actions', label: '操作', width: 200, slot: 'actions', fixed: 'right' }
]

// 获取异常类型选项
export const getAbnormalTypeOptions = () => {
  return Object.entries(abnormalTypeConfig).map(([value, config]) => ({
    label: config.label,
    value
  }))
}

// 获取处理状态选项
export const getProcessingStatusOptions = () => {
  return Object.entries(processingStatusConfig).map(([value, config]) => ({
    label: config.label,
    value
  }))
}

// 获取任务状态选项
export const getTaskStatusOptions = () => {
  return Object.entries(taskStatusConfig).map(([value, config]) => ({
    label: config.label,
    value
  }))
}

// 获取优先级选项
export const getPriorityOptions = () => {
  return Object.entries(priorityConfig).map(([value, config]) => ({
    label: config.label,
    value
  }))
}
