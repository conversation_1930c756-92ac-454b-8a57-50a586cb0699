import type { FieldConfig } from '@/types/searchForm'

// 区域选项
export const regionOptions = [
  { label: '保定市竞秀区', value: '保定市竞秀区' },
  { label: '保定市莲池区', value: '保定市莲池区' },
  { label: '保定市满城区', value: '保定市满城区' },
  { label: '保定市清苑区', value: '保定市清苑区' },
  { label: '保定市徐水区', value: '保定市徐水区' }
]

// 人员类型选项
export const personnelTypeOptions = [
  { label: '专职保卫', value: 1 },
  { label: '保安人员', value: 2 },
  { label: '物流人员', value: 3 }
]

// 负责人选项
export const responsiblePersonOptions = [
  { label: '李审查员', value: 'user-001' },
  { label: '王审查员', value: 'user-002' },
  { label: '张审查员', value: 'user-003' },
  { label: '刘审查员', value: 'user-004' }
]

// 负责单位选项
export const responsibleOrgOptions = [
  { label: '莲池分局', value: 'org-001' },
  { label: '竞秀分局', value: 'org-002' },
  { label: '满城分局', value: 'org-003' },
  { label: '清苑分局', value: 'org-004' },
  { label: '徐水分局', value: 'org-005' }
]

// 背审结果选项
export const backgroundCheckResultOptions = [
  { label: '正常', value: 'normal' },
  { label: '异常', value: 'abnormal' },
  { label: '风险', value: 'risk' }
]

// 异常类型选项
export const abnormalTypeOptions = [
  { label: '身份信息异常', value: 'identity_abnormal' },
  { label: '犯罪记录', value: 'criminal_record' },
  { label: '信用问题', value: 'credit_issue' },
  { label: '其他异常', value: 'other_abnormal' }
]

// 入职日期快捷选项
export const entryDateShortcuts = [
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '最近半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
      return [start, end]
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  }
]

// 待背审状态的筛选配置
export const pendingSearchConfig: FieldConfig[][] = [
  [
    {
      key: 'name',
      label: '姓名',
      type: 'input',
      placeholder: '请输入姓名',
      span: 6
    },
    {
      key: 'idCard',
      label: '身份证号',
      type: 'input',
      placeholder: '请输入身份证号',
      span: 6
    },
    {
      key: 'organization',
      label: '所属单位',
      type: 'input',
      placeholder: '请输入所属单位',
      span: 6
    },
    {
      key: 'region',
      label: '区域',
      type: 'multiSelect',
      placeholder: '请选择区域',
      span: 6,
      options: regionOptions
    }
  ],
  [
    {
      key: 'personnelType',
      label: '人员类型',
      type: 'multiSelect',
      placeholder: '请选择人员类型',
      span: 6,
      options: personnelTypeOptions
    },
    {
      key: 'entryDate',
      label: '入职日期',
      type: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      span: 6,
      shortcuts: entryDateShortcuts
    }
  ]
]

// 背审中状态的筛选配置
export const inProgressSearchConfig: FieldConfig[][] = [
  [
    {
      key: 'name',
      label: '姓名',
      type: 'input',
      placeholder: '请输入姓名',
      span: 6
    },
    {
      key: 'idCard',
      label: '身份证号',
      type: 'input',
      placeholder: '请输入身份证号',
      span: 6
    },
    {
      key: 'organization',
      label: '所属单位',
      type: 'input',
      placeholder: '请输入所属单位',
      span: 6
    },
    {
      key: 'region',
      label: '区域',
      type: 'multiSelect',
      placeholder: '请选择区域',
      span: 6,
      options: regionOptions
    }
  ],
  [
    {
      key: 'personnelType',
      label: '人员类型',
      type: 'multiSelect',
      placeholder: '请选择人员类型',
      span: 6,
      options: personnelTypeOptions
    },
    {
      key: 'entryDate',
      label: '入职日期',
      type: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      span: 6,
      shortcuts: entryDateShortcuts
    },
    {
      key: 'responsiblePerson',
      label: '负责人',
      type: 'select',
      placeholder: '请选择负责人',
      span: 6,
      options: responsiblePersonOptions
    },
    {
      key: 'responsibleOrg',
      label: '负责单位',
      type: 'select',
      placeholder: '请选择负责单位',
      span: 6,
      options: responsibleOrgOptions
    }
  ]
]

// 已完成状态的筛选配置
export const completedSearchConfig: FieldConfig[][] = [
  [
    {
      key: 'name',
      label: '姓名',
      type: 'input',
      placeholder: '请输入姓名',
      span: 6
    },
    {
      key: 'idCard',
      label: '身份证号',
      type: 'input',
      placeholder: '请输入身份证号',
      span: 6
    },
    {
      key: 'organization',
      label: '所属单位',
      type: 'input',
      placeholder: '请输入所属单位',
      span: 6
    },
    {
      key: 'region',
      label: '区域',
      type: 'multiSelect',
      placeholder: '请选择区域',
      span: 6,
      options: regionOptions
    }
  ],
  [
    {
      key: 'personnelType',
      label: '人员类型',
      type: 'multiSelect',
      placeholder: '请选择人员类型',
      span: 6,
      options: personnelTypeOptions
    },
    {
      key: 'entryDate',
      label: '入职日期',
      type: 'daterange',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      span: 6,
      shortcuts: entryDateShortcuts
    },
    {
      key: 'responsiblePerson',
      label: '负责人',
      type: 'select',
      placeholder: '请选择负责人',
      span: 6,
      options: responsiblePersonOptions
    },
    {
      key: 'responsibleOrg',
      label: '负责单位',
      type: 'select',
      placeholder: '请选择负责单位',
      span: 6,
      options: responsibleOrgOptions
    }
  ],
  [
    {
      key: 'backgroundCheckResult',
      label: '背审结果',
      type: 'select',
      placeholder: '请选择背审结果',
      span: 6,
      options: backgroundCheckResultOptions
    },
    {
      key: 'abnormalType',
      label: '异常类型',
      type: 'multiSelect',
      placeholder: '请选择异常类型',
      span: 6,
      options: abnormalTypeOptions,
      showWhen: {
        field: 'backgroundCheckResult',
        value: 'abnormal'
      }
    }
  ]
]

// 表格列配置接口
export interface TableColumnConfig {
  prop?: string
  label?: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | string
  type?: 'selection' | 'index' | 'expand'
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  slot?: string
}

// 待背审状态的表格列配置
export const pendingTableColumns: TableColumnConfig[] = [
  { type: 'selection', width: 55 },
  { prop: 'avatar', label: '照片', width: 80, slot: 'avatar' },
  { prop: 'name', label: '姓名', width: 100 },
  { prop: 'gender', label: '性别', width: 60 },
  { prop: 'idCard', label: '身份证号', width: 180, slot: 'idCard' },
  { prop: 'phone', label: '联系方式', width: 140 },
  { prop: 'personnelType', label: '人员类型', width: 120, slot: 'personnelType' },
  { prop: 'region', label: '区域', width: 120 },
  { prop: 'department', label: '所属单位', minWidth: 150 },
  { prop: 'entryDate', label: '入职时间', width: 120 },
  { label: '操作', width: 100, fixed: 'right', slot: 'action' }
]

// 背审中状态的表格列配置
export const inProgressTableColumns: TableColumnConfig[] = [
  { type: 'selection', width: 55 },
  { prop: 'avatar', label: '照片', width: 75, slot: 'avatar' },
  { prop: 'name', label: '姓名', width: 100 },
  { prop: 'gender', label: '性别', width: 60 },
  { prop: 'idCard', label: '身份证号', width: 150, slot: 'idCard' },
  { prop: 'phone', label: '联系方式', width: 120 },
  { prop: 'personnelType', label: '人员类型', width: 100, slot: 'personnelType' },
  { prop: 'region', label: '区域', width: 120 },
  { prop: 'department', label: '所属单位', width: 150 },
  { prop: 'entryDate', label: '入职时间', width: 120 },
  { prop: 'responsible', label: '负责方', width: 120, slot: 'responsible' },
  { prop: 'dueDate', label: '截止时间', width: 120 },
  { label: '操作', width: 100, fixed: 'right', slot: 'action' }
]

// 已完成状态的表格列配置
export const completedTableColumns: TableColumnConfig[] = [
  { type: 'selection', width: 55 },
  { prop: 'avatar', label: '照片', width: 75, slot: 'avatar' },
  { prop: 'name', label: '姓名', width: 100 },
  { prop: 'gender', label: '性别', width: 60 },
  { prop: 'idCard', label: '身份证号', width: 150, slot: 'idCard' },
  { prop: 'phone', label: '联系方式', width: 120 },
  { prop: 'personnelType', label: '人员类型', width: 100, slot: 'personnelType' },
  { prop: 'region', label: '区域', width: 120 },
  { prop: 'department', label: '所属单位', width: 150 },
  { prop: 'entryDate', label: '入职时间', width: 120 },
  { prop: 'responsible', label: '负责方', width: 120, slot: 'responsible' },
  { prop: 'backgroundCheckResult', label: '背审结果', width: 120, slot: 'backgroundCheckResult' },
  { prop: 'completedDate', label: '完成时间', width: 120 },
  { label: '操作', width: 100, fixed: 'right', slot: 'action' }
]

// 获取状态对应的配置
export const getStatusConfig = (status: string) => {
  const configs = {
    pending: {
      searchConfig: pendingSearchConfig,
      tableColumns: pendingTableColumns
    },
    in_progress: {
      searchConfig: inProgressSearchConfig,
      tableColumns: inProgressTableColumns
    },
    completed: {
      searchConfig: completedSearchConfig,
      tableColumns: completedTableColumns
    }
  }
  
  return configs[status as keyof typeof configs] || configs.pending
}
