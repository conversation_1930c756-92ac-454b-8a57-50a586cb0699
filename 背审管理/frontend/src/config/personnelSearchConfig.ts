import type { FieldConfig } from '@/types/searchForm'

// 入职时间快捷选项
export const entryDateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '最近半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
      return [start, end]
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  }
]

// 区域选项
export const regionOptions = [
  { label: '全部', value: '' },
  { label: '竞秀区', value: '竞秀区' },
  { label: '莲池区', value: '莲池区' },
  { label: '满城区', value: '满城区' },
  { label: '清苑区', value: '清苑区' },
  { label: '徐水区', value: '徐水区' },
  { label: '涞水县', value: '涞水县' },
  { label: '阜平县', value: '阜平县' },
  { label: '定兴县', value: '定兴县' },
  { label: '唐县', value: '唐县' },
  { label: '高阳县', value: '高阳县' },
  { label: '容城县', value: '容城县' },
  { label: '涞源县', value: '涞源县' },
  { label: '望都县', value: '望都县' },
  { label: '安新县', value: '安新县' },
  { label: '易县', value: '易县' },
  { label: '曲阳县', value: '曲阳县' },
  { label: '蠡县', value: '蠡县' },
  { label: '顺平县', value: '顺平县' },
  { label: '博野县', value: '博野县' },
  { label: '雄县', value: '雄县' },
  { label: '高碑店市', value: '高碑店市' },
  { label: '安国市', value: '安国市' },
  { label: '定州市', value: '定州市' }
]

// 人员状态选项
export const statusOptions = [
  { label: '全部', value: '' },
  { label: '在职', value: '1' },
  { label: '离职', value: '2' }
]

// 背景审查结果选项
export const backgroundCheckResultOptions = [
  { label: '全部', value: '' },
  { label: '未审查', value: '0' },
  { label: '正常', value: '1' },
  { label: '异常', value: '2' }
]

// 处理结果选项
export const processingStatusOptions = [
  { label: '全部', value: '' },
  { label: '未处理', value: '0' },
  { label: '无需处理', value: '1' },
  { label: '重点关注', value: '2' },
  { label: '调岗/劝退', value: '3' }
]

// 创建人员搜索表单配置
export const createPersonnelSearchConfig = (
  industryOptions: any[],
  abnormalTypeOptions: any[]
): FieldConfig[][] => {
  return [
    // 第一行
    [
      {
        key: 'name',
        label: '姓名',
        type: 'input',
        placeholder: '请输入姓名',
        span: 8
      },
      {
        key: 'idCard',
        label: '身份证号',
        type: 'input',
        placeholder: '请输入身份证号',
        span: 8
      },
      {
        key: 'organization',
        label: '所属单位',
        type: 'input',
        placeholder: '请输入所属单位',
        span: 8
      }
    ],
    // 第二行
    [
      {
        key: 'industry',
        label: '所属行业',
        type: 'multiSelect',
        placeholder: '请选择所属行业',
        span: 8,
        options: industryOptions
      },
      {
        key: 'region',
        label: '区域',
        type: 'select',
        placeholder: '请选择区域',
        span: 8,
        options: regionOptions
      },
      {
        key: 'status',
        label: '人员状态',
        type: 'select',
        placeholder: '请选择人员状态',
        span: 8,
        options: statusOptions
      }
    ],
    // 第三行
    [
      {
        key: 'backgroundCheckResult',
        label: '背审结果',
        type: 'select',
        placeholder: '请选择背景审查结果',
        span: 8,
        options: backgroundCheckResultOptions,
        onChange: (value: string, formData: any) => {
          // 如果不是选择异常，清空异常类型
          if (value !== '2') {
            formData.abnormalTypes = []
          }
        }
      },
      {
        key: 'abnormalTypes',
        label: '异常类型',
        type: 'multiSelect',
        placeholder: '请选择异常类型，默认全选',
        span: 8,
        options: abnormalTypeOptions,
        showWhen: {
          field: 'backgroundCheckResult',
          value: '2'
        }
      },
      {
        key: 'processingStatus',
        label: '处理结果',
        type: 'select',
        placeholder: '请选择处理结果',
        span: 8,
        options: processingStatusOptions
      }
    ],
    // 第四行
    [
      {
        key: 'entryDateRange',
        label: '入职时间',
        type: 'daterange',
        span: 8,
        shortcuts: entryDateShortcuts
      }
    ]
  ]
}