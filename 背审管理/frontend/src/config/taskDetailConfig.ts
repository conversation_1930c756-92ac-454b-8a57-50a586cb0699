// 通用任务详情配置

// 任务状态配置
export const taskStatusConfig = {
  // 背审任务状态
  'pending': { label: '待分配', color: 'info' },
  'assigned': { label: '已分配', color: 'primary' },
  'in_progress': { label: '进行中', color: 'warning' },
  'completed': { label: '已完成', color: 'success' },
  'cancelled': { label: '已取消', color: 'info' },
  'overdue': { label: '已逾期', color: 'danger' }
} as const

// 优先级配置
export const priorityConfig = {
  'low': { label: '普通', color: 'info' },
  'medium': { label: '中等', color: 'warning' },
  'high': { label: '高', color: 'danger' },
  'urgent': { label: '紧急', color: 'danger' }
} as const

// 背审状态配置
export const backgroundCheckStatusConfig = {
  'pending': { label: '未完成', color: 'info' },
  'normal': { label: '正常', color: 'success' },
  'abnormal': { label: '异常', color: 'danger' },
  'completed': { label: '已完成', color: 'success' }
} as const

// 异常类型配置
export const abnormalTypeConfig = {
  'criminal_record': { 
    label: '犯罪记录', 
    color: 'danger',
    description: '存在犯罪记录或违法行为'
  },
  'credit_issue': { 
    label: '信用问题', 
    color: 'warning',
    description: '个人征信存在不良记录'
  },
  'education_fraud': { 
    label: '学历造假', 
    color: 'warning',
    description: '学历信息虚假或无法验证'
  },
  'work_experience': { 
    label: '工作经历问题', 
    color: 'info',
    description: '工作经历存在疑点或虚假'
  },
  'identity_issue': {
    label: '身份信息问题',
    color: 'warning',
    description: '身份证信息异常或无法验证'
  },
  'reference_problem': {
    label: '推荐人问题',
    color: 'warning', 
    description: '推荐人信息虚假或存在问题'
  }
} as const

// 处理状态配置
export const processingStatusConfig = {
  'pending': { label: '未处理', color: 'info' },
  'processing': { label: '处理中', color: 'warning' },
  'completed': { label: '已处理', color: 'success' },
  'rejected': { label: '已驳回', color: 'danger' }
} as const

// 处理结果配置
export const processingResultConfig = {
  'focus': { label: '重点关注', color: 'warning' },
  'transfer': { label: '调岗', color: 'primary' },
  'dismiss': { label: '劝退', color: 'danger' },
  'normal': { label: '正常', color: 'success' }
} as const

// 人员完成状态配置
export const personnelCompletionStatusConfig = {
  'incomplete': { label: '未完成', color: 'info' },
  'completed': { label: '已完成', color: 'success' }
} as const

// 工具函数
export const getTaskStatusText = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.label || status
}

export const getTaskStatusColor = (status: string) => {
  return taskStatusConfig[status as keyof typeof taskStatusConfig]?.color || 'info'
}

export const getPriorityText = (priority: string) => {
  return priorityConfig[priority as keyof typeof priorityConfig]?.label || priority
}

export const getPriorityColor = (priority: string) => {
  return priorityConfig[priority as keyof typeof priorityConfig]?.color || 'info'
}

export const getBackgroundCheckStatusText = (status: string) => {
  return backgroundCheckStatusConfig[status as keyof typeof backgroundCheckStatusConfig]?.label || status
}

export const getBackgroundCheckStatusColor = (status: string) => {
  return backgroundCheckStatusConfig[status as keyof typeof backgroundCheckStatusConfig]?.color || 'info'
}

export const getAbnormalTypeText = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.label || type
}

export const getAbnormalTypeColor = (type: string) => {
  return abnormalTypeConfig[type as keyof typeof abnormalTypeConfig]?.color || 'warning'
}

export const getProcessingStatusText = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.label || status
}

export const getProcessingStatusColor = (status: string) => {
  return processingStatusConfig[status as keyof typeof processingStatusConfig]?.color || 'info'
}

export const getProcessingResultText = (result: string) => {
  return processingResultConfig[result as keyof typeof processingResultConfig]?.label || result
}

export const getProcessingResultColor = (result: string) => {
  return processingResultConfig[result as keyof typeof processingResultConfig]?.color || 'success'
}

export const getPersonnelCompletionStatusText = (status: string) => {
  return personnelCompletionStatusConfig[status as keyof typeof personnelCompletionStatusConfig]?.label || status
}

export const getPersonnelCompletionStatusColor = (status: string) => {
  return personnelCompletionStatusConfig[status as keyof typeof personnelCompletionStatusConfig]?.color || 'info'
}

// 获取进度状态
export const getProgressStatus = (task: any) => {
  if (!task.progress) return undefined
  if (task.status === 'completed') return 'success'
  if (task.status === 'cancelled') return 'exception'
  if (task.isOverdue) return 'exception'
  return undefined
}

// 获取历史记录类型
export const getHistoryType = (type: string) => {
  const typeMap: Record<string, string> = {
    'create': 'primary',
    'assign': 'success',
    'process': 'warning',
    'complete': 'success',
    'cancel': 'info',
    'reminder': 'warning'
  }
  return typeMap[type] || 'primary'
}

// 身份证脱敏
export const maskIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

// 获取任务类型配置
export const getTaskTypeConfig = (taskType: 'background-check' | 'result-processing') => {
  if (taskType === 'background-check') {
    return {
      drawerTitle: '背审任务详情',
      historyTabLabel: '催办历史',
      personnelStatusConfig: backgroundCheckStatusConfig,
      getPersonnelStatusText: getBackgroundCheckStatusText,
      getPersonnelStatusColor: getBackgroundCheckStatusColor
    }
  } else {
    return {
      drawerTitle: '处理任务详情',
      historyTabLabel: '处理历史',
      personnelStatusConfig: processingStatusConfig,
      getPersonnelStatusText: getProcessingStatusText,
      getPersonnelStatusColor: getProcessingStatusColor
    }
  }
}

// 获取人员状态选项
export const getPersonnelStatusOptions = (taskType: 'background-check' | 'result-processing') => {
  if (taskType === 'background-check') {
    return [
      { label: '未完成', value: 'pending' },
      { label: '正常', value: 'normal' },
      { label: '异常', value: 'abnormal' }
    ]
  } else {
    return [
      { label: '未处理', value: 'pending' },
      { label: '已处理', value: 'completed' }
    ]
  }
}

// 获取完成状态选项
export const getCompletionStatusOptions = () => {
  return [
    { label: '未完成', value: 'incomplete' },
    { label: '已完成', value: 'completed' }
  ]
}
