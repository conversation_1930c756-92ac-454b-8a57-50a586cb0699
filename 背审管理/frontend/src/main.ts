import './assets/main.css'
import './assets/theme.css'  // 导入自定义主题
import './styles/index.scss'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 引入权限相关
import permissionDirectives from '@/directives/permission'
import { useAuthStore } from '@/stores/authStore'

// 引入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// 引入中文语言包
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// 引入ECharts
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart, LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'

// 注册必要的组件
use([
  CanvasRenderer,
  Bar<PERSON>hart,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const app = createApp(App)

// 全局注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局注册VChart组件
app.component('VChart', VChart)

const pinia = createPinia()
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

// 注册权限指令
app.use(permissionDirectives)

// 初始化权限
const authStore = useAuthStore()
authStore.initAuth().catch(error => {
  console.error('权限初始化失败:', error)
})

// 在子应用的路由守卫中
router.beforeEach((to, from, next) => {
  // 当路由变化时，通知父应用
  const event = new CustomEvent('subPageChange', {
    detail: {
      index: to.path // 或者 to.name，取决于菜单配置中的index格式
    }
  });
  if (window?.parent && window.__POWERED_BY_WUJIE__) {
    window.parent.dispatchEvent(event);
    console.log('子应用路由变化', to.path);
    // 如果不是从主页调过来 且 路由发生了变化 则不跳转 交由父应用处理
    if (from.path !== '/') {
      return;
    }
  } 
  next();

});

app.mount('#app')
