<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="main-header">
      <div class="header-left">
        <el-icon class="menu-toggle" @click="toggleSidebar">
          <Fold v-if="!sidebarCollapsed" />
          <Expand v-else />
        </el-icon>
        <h1 class="system-title">安保背景审查管理系统</h1>
      </div>
      <div class="header-right">
        <el-dropdown>
          <span class="user-info">
            <el-icon><User /></el-icon>
            管理员
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人设置</el-dropdown-item>
              <el-dropdown-item divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="main-sidebar">
        <el-menu
          :default-active="activeMenu"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <!-- 安保背审 -->
          <el-sub-menu index="security-audit">
            <template #title>
              <el-icon><UserFilled /></el-icon>
              <span>安保背审</span>
            </template>
            <el-menu-item index="/dashboard">
              <el-icon><Odometer /></el-icon>
              <span>工作台</span>
            </el-menu-item>
            <el-menu-item index="/security-personnel">
              <el-icon><User /></el-icon>
              <span>安保人员管理</span>
            </el-menu-item>
            <el-menu-item index="/pending-personnel">
              <el-icon><Warning /></el-icon>
              <span>待处理人员</span>
            </el-menu-item>
            <el-menu-item index="/focus-personnel">
              <el-icon><View /></el-icon>
              <span>背审关注人员</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 统计报表 -->
          <el-sub-menu index="statistics">
            <template #title>
              <el-icon><DataAnalysis /></el-icon>
              <span>统计报表</span>
            </template>
            <el-menu-item index="/data-dashboard">
              <el-icon><DataAnalysis /></el-icon>
              <span>数据看板</span>
            </el-menu-item>
            <el-menu-item index="/statistical-reports">
              <el-icon><Document /></el-icon>
              <span>统计报表</span>
            </el-menu-item>
            <el-menu-item index="/special-reports">
              <el-icon><Files /></el-icon>
              <span>专项报表</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 医疗机构背审 -->
          <el-sub-menu index="medical">
            <template #title>
              <el-icon><FirstAidKit /></el-icon>
              <span>医疗机构背审</span>
            </template>
            <el-menu-item index="/medical-personnel">
              <el-icon><User /></el-icon>
              <span>医疗从业人员</span>
            </el-menu-item>
            <el-menu-item index="/medical-security">
              <el-icon><UserFilled /></el-icon>
              <span>医疗安保人员</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 中小幼背审 -->
          <el-sub-menu index="education">
            <template #title>
              <el-icon><School /></el-icon>
              <span>中小幼背审</span>
            </template>
            <el-menu-item index="/education-personnel">
              <el-icon><User /></el-icon>
              <span>教职工背审</span>
            </el-menu-item>
            <el-menu-item index="/education-security">
              <el-icon><UserFilled /></el-icon>
              <span>中小幼安保人员</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 寄递人员背审 -->
          <el-sub-menu index="logistics">
            <template #title>
              <el-icon><Box /></el-icon>
              <span>寄递人员背审</span>
            </template>
            <el-menu-item index="/delivery-personnel">
              <el-icon><User /></el-icon>
              <span>快递人员背审管理</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import {
  Fold,
  Expand,
  User,
  ArrowDown,
  UserFilled,
  Odometer,
  Warning,
  View,
  DataAnalysis,
  Document,
  Files,
  FirstAidKit,
  School,
  Box
} from '@element-plus/icons-vue'

const route = useRoute()

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 计算侧边栏宽度
const sidebarWidth = computed(() => {
  return sidebarCollapsed.value ? '64px' : '240px'
})

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

onMounted(() => {
  // 可以在这里添加初始化逻辑
})
</script>

<style scoped>
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menu-toggle {
  font-size: 18px;
  cursor: pointer;
  color: #606266;
  transition: color 0.3s;
}

.menu-toggle:hover {
  color: #409eff;
}

.system-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.main-sidebar {
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.main-content {
  background: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
}

/* Element Plus 菜单样式覆盖 */
:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-sub-menu .el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-menu-item.is-active) {
  background-color: #ecf5ff;
  color: #409eff;
}

:deep(.el-menu-item:hover) {
  background-color: #f5f7fa;
}
</style>
