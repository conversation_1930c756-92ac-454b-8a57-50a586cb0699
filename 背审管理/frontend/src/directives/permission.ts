// 权限指令

import type { Directive, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/authStore'

/**
 * 权限指令
 * 用法: 
 * v-permission="'permission:code'" - 单个权限
 * v-permission="['permission1', 'permission2']" - 多个权限（任意一个即可）
 * v-permission:all="['permission1', 'permission2']" - 多个权限（必须全部拥有）
 */
export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  }
}

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const { value, arg } = binding
  
  if (!value) {
    console.warn('v-permission 指令需要提供权限值')
    return
  }
  
  const authStore = useAuthStore()
  let hasPermission = false
  
  if (Array.isArray(value)) {
    if (arg === 'all') {
      // 需要拥有所有权限
      hasPermission = value.every(permission => authStore.hasPermission(permission))
    } else {
      // 需要拥有任意一个权限
      hasPermission = value.some(permission => authStore.hasPermission(permission))
    }
  } else {
    // 单个权限
    hasPermission = authStore.hasPermission(value)
  }
  
  if (!hasPermission) {
    // 没有权限则隐藏元素
    el.style.display = 'none'
    el.setAttribute('data-permission-hidden', 'true')
  } else {
    // 有权限则显示元素
    el.style.display = ''
    el.removeAttribute('data-permission-hidden')
  }
}

/**
 * 角色指令
 * 用法:
 * v-role="'admin'" - 仅管理员可见
 * v-role="'unit'" - 仅下级单位可见
 * v-role="['admin', 'unit']" - 多个角色可见
 */
export const role: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkRole(el, binding)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkRole(el, binding)
  }
}

function checkRole(el: HTMLElement, binding: DirectiveBinding) {
  const { value } = binding
  
  if (!value) {
    console.warn('v-role 指令需要提供角色值')
    return
  }
  
  const authStore = useAuthStore()
  const currentRole = authStore.currentRole
  let hasRole = false
  
  if (Array.isArray(value)) {
    hasRole = value.includes(currentRole)
  } else {
    hasRole = currentRole === value
  }
  
  if (!hasRole) {
    el.style.display = 'none'
    el.setAttribute('data-role-hidden', 'true')
  } else {
    el.style.display = ''
    el.removeAttribute('data-role-hidden')
  }
}

// 权限指令插件
export default {
  install(app: any) {
    app.directive('permission', permission)
    app.directive('role', role)
  }
}
