import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    // 安保背审模块
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('../views/security-audit/Dashboard.vue'),
      meta: { title: '工作台', requiresAuth: true }
    },
    {
      path: '/security-personnel',
      name: 'SecurityPersonnel',
      component: () => import('../views/security-audit/SecurityPersonnel.vue'),
      meta: { title: '安保人员管理', requiresAuth: true }
    },
    {
      path: '/pending-personnel',
      name: 'PendingPersonnel',
      component: () => import('../views/security-audit/PendingPersonnel.vue'),
      meta: { title: '待处理人员', requiresAuth: true }
    },
    {
      path: '/focus-personnel',
      name: 'FocusPersonnel',
      component: () => import('../views/security-audit/FocusPersonnel.vue'),
      meta: { title: '背审关注人员', requiresAuth: true }
    },
    {
      path: '/medical-personnel',
      name: 'MedicalPersonnel',
      component: () => import('../views/security-audit/MedicalPersonnel.vue'),
      meta: { title: '医疗从业人员背景审查', requiresAuth: true }
    },
    {
      path: '/education-personnel',
      name: 'EducationPersonnel',
      component: () => import('../views/security-audit/EducationPersonnel.vue'),
      meta: { title: '教职工背景审查', requiresAuth: true }
    },
    {
      path: '/delivery-personnel',
      name: 'DeliveryPersonnel',
      component: () => import('../views/security-audit/DeliveryPersonnel.vue'),
      meta: { title: '快递人员背景审查', requiresAuth: true }
    },

    // 统计报表模块
    {
      path: '/data-dashboard',
      name: 'DataDashboard',
      component: () => import('../views/statistics/DataDashboard.vue'),
      meta: { title: '数据看板', requiresAuth: true }
    },
    {
      path: '/statistical-reports',
      name: 'StatisticalReports',
      component: () => import('../views/statistics/StatisticalReports.vue'),
      meta: { title: '统计报表', requiresAuth: true }
    },
    {
      path: '/special-reports',
      name: 'SpecialReports',
      component: () => import('../views/statistics/SpecialReports.vue'),
      meta: { title: '专项报表', requiresAuth: true }
    },

    // 医疗机构背审模块
    {
      path: '/medical-staff',
      name: 'MedicalStaff',
      component: () => import('../views/medical/MedicalStaff.vue'),
      meta: { title: '医疗从业人员', requiresAuth: true }
    },
    {
      path: '/medical-security',
      name: 'MedicalSecurity',
      component: () => import('../views/medical/MedicalSecurity.vue'),
      meta: { title: '医疗安保人员', requiresAuth: true }
    },

    // 中小幼背审模块
    {
      path: '/education-staff',
      name: 'EducationStaff',
      component: () => import('../views/education/EducationStaff.vue'),
      meta: { title: '教职工背审', requiresAuth: true }
    },
    {
      path: '/education-security',
      name: 'EducationSecurity',
      component: () => import('../views/education/EducationSecurity.vue'),
      meta: { title: '中小幼安保人员', requiresAuth: true }
    },

    // 寄递人员背审模块
    {
      path: '/logistics-staff',
      name: 'LogisticsStaff',
      component: () => import('../views/logistics/LogisticsStaff.vue'),
      meta: { title: '快递人员背审管理', requiresAuth: true }
    },

    // 测试页面
    {
      path: '/test',
      name: 'TestPage',
      component: () => import('../views/TestPage.vue'),
      meta: { title: '测试页面' }
    },

    // 权限测试页面
    {
      path: '/auth-test',
      name: 'AuthTestPage',
      component: () => import('../views/AuthTestPage.vue'),
      meta: { title: '权限测试页面' }
    }
  ]
})

export default router

