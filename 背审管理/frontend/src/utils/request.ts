import axios from 'axios';
import { ElMessage } from 'element-plus';

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL, // 使用环境变量配置的baseURL
  timeout: 10000, // 请求超时时间
  withCredentials: true, // 跨域请求时是否需要使用凭证
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在请求发送之前做一些处理，例如添加token等
    // const token = localStorage.getItem('token');
    // 从cookie获取token
    console.log(document.cookie);
    const token = document.cookie.split('; ').find(row => row.startsWith('token='))?.split('=')[1];
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // 处理请求错误
    console.error('请求错误', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 如果响应正常，直接返回数据
    return response;
  },
  (error) => {
    // 处理响应错误
    const { response } = error;
    if (response) {
      const { status, data } = response;
      let message = '请求错误';
      
      // 根据状态码处理不同的错误
      switch (status) {
        case 400:
          message = data.message || '请求参数错误';
          break;
        case 401:
          message = '未授权，请登录';
          // 可以在这里处理登录跳转
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求资源不存在';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        default:
          message = `连接错误${status}`;
      }
      
      // 显示错误消息
      ElMessage.error(message);
    } else {
      // 请求被取消或者网络错误
      ElMessage.error('网络错误，请检查您的网络连接');
    }
    
    return Promise.reject(error);
  }
);

export default request; 