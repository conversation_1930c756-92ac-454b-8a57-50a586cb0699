// localStorage功能测试脚本

import { storage, STORAGE_KEYS } from './storage'
import authService from '@/services/authService'
import type { UserRole } from '@/types/auth'

/**
 * 测试localStorage基本功能
 */
export function testStorageBasics() {
  console.log('🧪 开始测试localStorage基本功能...')
  
  // 测试设置和获取
  const testKey = 'test_key'
  const testValue = { name: '测试数据', timestamp: Date.now() }
  
  storage.set(testKey, testValue)
  const retrieved = storage.get(testKey)
  
  console.log('✅ 设置数据:', testValue)
  console.log('✅ 获取数据:', retrieved)
  console.log('✅ 数据一致性:', JSON.stringify(testValue) === JSON.stringify(retrieved))
  
  // 测试过期功能
  storage.set('expire_test', 'will expire', 100) // 100ms后过期
  setTimeout(() => {
    const expiredData = storage.get('expire_test')
    console.log('✅ 过期测试:', expiredData === null ? '数据已过期' : '数据未过期')
  }, 150)
  
  // 清理测试数据
  storage.remove(testKey)
  storage.remove('expire_test')
  
  console.log('🎉 localStorage基本功能测试完成')
}

/**
 * 测试权限缓存功能
 */
export async function testAuthCache() {
  console.log('🧪 开始测试权限缓存功能...')
  
  // 获取初始状态
  const initialStatus = authService.getCacheStatus()
  console.log('📊 初始缓存状态:', initialStatus)
  
  // 测试角色切换
  console.log('🔄 测试角色切换...')
  await authService.switchRole('unit')
  
  const unitUser = await authService.getCurrentUser()
  console.log('✅ 切换到下级单位:', unitUser.name, unitUser.roles)
  
  // 检查localStorage
  const storedRole = storage.get<UserRole>(STORAGE_KEYS.CURRENT_ROLE)
  const storedUser = storage.get(STORAGE_KEYS.USER_INFO)
  console.log('💾 存储的角色:', storedRole)
  console.log('💾 存储的用户:', storedUser?.value?.name)
  
  // 切换回管理员
  await authService.switchRole('admin')
  const adminUser = await authService.getCurrentUser()
  console.log('✅ 切换到管理员:', adminUser.name, adminUser.roles)
  
  // 测试缓存状态
  const finalStatus = authService.getCacheStatus()
  console.log('📊 最终缓存状态:', finalStatus)
  
  console.log('🎉 权限缓存功能测试完成')
}

/**
 * 测试权限检查功能
 */
export async function testPermissionCheck() {
  console.log('🧪 开始测试权限检查功能...')
  
  // 测试管理员权限
  await authService.switchRole('admin')
  console.log('👑 管理员权限测试:')
  console.log('  - 是否管理员:', authService.isAdmin())
  console.log('  - 查看所有人员:', authService.hasPermission('personnel:view:all'))
  console.log('  - 任务管理:', authService.hasPermission('task:manage'))
  console.log('  - 数据范围:', authService.getDataScope())
  
  // 测试下级单位权限
  await authService.switchRole('unit')
  console.log('🏢 下级单位权限测试:')
  console.log('  - 是否管理员:', authService.isAdmin())
  console.log('  - 查看所有人员:', authService.hasPermission('personnel:view:all'))
  console.log('  - 查看本组织人员:', authService.hasPermission('personnel:view:org'))
  console.log('  - 任务处理:', authService.hasPermission('task:process'))
  console.log('  - 数据范围:', authService.getDataScope())
  
  console.log('🎉 权限检查功能测试完成')
}

/**
 * 测试缓存持久化
 */
export async function testCachePersistence() {
  console.log('🧪 开始测试缓存持久化...')
  
  // 设置特定角色
  await authService.switchRole('unit')
  const beforeUser = await authService.getCurrentUser()
  console.log('📝 刷新前用户:', beforeUser.name, beforeUser.roles)
  
  // 模拟页面刷新（清除内存状态）
  authService.reset()
  
  // 重新初始化（应该从localStorage恢复）
  const afterUser = await authService.getCurrentUser()
  console.log('🔄 刷新后用户:', afterUser.name, afterUser.roles)
  
  // 验证数据一致性
  const isConsistent = beforeUser.userId === afterUser.userId && 
                      beforeUser.roles[0] === afterUser.roles[0]
  console.log('✅ 数据持久化:', isConsistent ? '成功' : '失败')
  
  console.log('🎉 缓存持久化测试完成')
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行localStorage功能完整测试...')
  console.log('=' .repeat(50))
  
  try {
    testStorageBasics()
    
    await new Promise(resolve => setTimeout(resolve, 200)) // 等待过期测试
    
    await testAuthCache()
    await testPermissionCheck()
    await testCachePersistence()
    
    console.log('=' .repeat(50))
    console.log('🎉 所有测试完成！localStorage功能正常工作')
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  }
}

/**
 * 清理所有测试数据
 */
export function cleanupTestData() {
  console.log('🧹 清理测试数据...')
  storage.clear()
  authService.reset()
  console.log('✅ 清理完成')
}

// 在开发环境下自动暴露到全局
if (import.meta.env.DEV) {
  (window as any).testLocalStorage = {
    testStorageBasics,
    testAuthCache,
    testPermissionCheck,
    testCachePersistence,
    runAllTests,
    cleanupTestData
  }
  
  console.log('🔧 localStorage测试工具已加载到 window.testLocalStorage')
  console.log('💡 使用 window.testLocalStorage.runAllTests() 运行完整测试')
}
