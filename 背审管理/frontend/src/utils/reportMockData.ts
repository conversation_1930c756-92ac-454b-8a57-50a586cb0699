// 报表Mock数据生成器
import { 
  baodingRegions, 
  baodingDepartments, 
  industryTypes,
  backgroundCheckAbnormalTypes,
  getSingleAbnormalTypeText,
  getProcessingStatusText
} from '@/data/personnelMockData'

// 生成随机姓名
const generateRandomName = () => {
  const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
  const names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞']
  return surnames[Math.floor(Math.random() * surnames.length)] + names[Math.floor(Math.random() * names.length)]
}

// 生成随机身份证号
const generateRandomIdCard = () => {
  const prefix = '130602' // 保定市竞秀区
  const year = 1970 + Math.floor(Math.random() * 40) // 1970-2010年
  const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')
  const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')
  const suffix = String(Math.floor(Math.random() * 9999)).padStart(4, '0')
  return `${prefix}${year}${month}${day}${suffix}`
}

// 生成异常人员名单Mock数据
export const generateAbnormalPersonnelMockData = (params: any) => {
  const count = 50 + Math.floor(Math.random() * 100) // 50-150条数据
  const data = []

  for (let i = 0; i < count; i++) {
    // 随机选择异常类型
    const abnormalTypeCount = 1 + Math.floor(Math.random() * 3) // 1-3个异常类型
    const selectedAbnormalTypes = []
    for (let j = 0; j < abnormalTypeCount; j++) {
      const randomType = backgroundCheckAbnormalTypes[Math.floor(Math.random() * backgroundCheckAbnormalTypes.length)]
      if (!selectedAbnormalTypes.includes(randomType.value)) {
        selectedAbnormalTypes.push(randomType.value)
      }
    }

    // 如果参数中指定了异常类型，则过滤
    if (params.abnormalTypes && params.abnormalTypes.length > 0) {
      const hasMatchingType = selectedAbnormalTypes.some(type => params.abnormalTypes.includes(type))
      if (!hasMatchingType) continue
    }

    const region = baodingRegions[Math.floor(Math.random() * baodingRegions.length)]
    const organization = baodingDepartments[Math.floor(Math.random() * baodingDepartments.length)]
    const processingStatus = Math.floor(Math.random() * 4) // 0-3

    // 如果参数中指定了区域，则过滤
    if (params.region && params.region.length > 0 && !params.region.includes(region)) {
      continue
    }

    // 如果参数中指定了处理状态，则过滤
    if (params.processingStatus && params.processingStatus !== String(processingStatus)) {
      continue
    }

    data.push({
      id: i + 1,
      name: generateRandomName(),
      idCard: generateRandomIdCard(),
      organization,
      region,
      abnormalTypes: selectedAbnormalTypes,
      processingStatus,
      entryDate: `${2018 + Math.floor(Math.random() * 6)}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      backgroundCheckResult: 2, // 异常
      industry: industryTypes[Math.floor(Math.random() * industryTypes.length)].value
    })
  }

  return data
}

// 生成单位统计Mock数据
export const generateUnitStatisticsMockData = (params: any) => {
  const data = []
  const organizations = baodingDepartments

  organizations.forEach((org, index) => {
    const region = baodingRegions[Math.floor(Math.random() * baodingRegions.length)]
    const industry = industryTypes[Math.floor(Math.random() * industryTypes.length)]
    
    // 如果参数中指定了区域，则过滤
    if (params.region && params.region.length > 0 && !params.region.includes(region)) {
      return
    }

    // 如果参数中指定了行业，则过滤
    if (params.industry && params.industry.length > 0 && !params.industry.includes(industry.value)) {
      return
    }

    const totalPersonnel = 20 + Math.floor(Math.random() * 200) // 20-220人
    const abnormalPersonnel = Math.floor(totalPersonnel * (0.02 + Math.random() * 0.15)) // 2%-17%异常率
    const pendingPersonnel = Math.floor(abnormalPersonnel * (0.3 + Math.random() * 0.5)) // 30%-80%待处理
    const abnormalRate = totalPersonnel > 0 ? ((abnormalPersonnel / totalPersonnel) * 100).toFixed(2) : '0.00'

    data.push({
      id: index + 1,
      organization: org,
      region,
      industry: industry.label,
      totalPersonnel,
      abnormalPersonnel,
      pendingPersonnel,
      abnormalRate: parseFloat(abnormalRate)
    })
  })

  return data
}

// 生成行业统计Mock数据
export const generateIndustryStatisticsMockData = (params: any) => {
  const data = []

  industryTypes.forEach((industry, index) => {
    // 如果参数中指定了行业，则过滤
    if (params.industry && params.industry.length > 0 && !params.industry.includes(industry.value)) {
      return
    }

    const totalPersonnel = 100 + Math.floor(Math.random() * 500) // 100-600人
    const abnormalPersonnel = Math.floor(totalPersonnel * (0.03 + Math.random() * 0.12)) // 3%-15%异常率
    const pendingPersonnel = Math.floor(abnormalPersonnel * (0.2 + Math.random() * 0.6)) // 20%-80%待处理

    data.push({
      id: index + 1,
      name: industry.label,
      value: industry.value,
      totalPersonnel,
      abnormalPersonnel,
      pendingPersonnel,
      abnormalRate: totalPersonnel > 0 ? ((abnormalPersonnel / totalPersonnel) * 100).toFixed(2) : '0.00'
    })
  })

  return data
}

// 生成异常类型统计Mock数据
export const generateAbnormalTypeStatisticsMockData = (params: any) => {
  const data = []
  let totalAbnormal = 0

  // 先计算总的异常人员数
  backgroundCheckAbnormalTypes.forEach(() => {
    totalAbnormal += 10 + Math.floor(Math.random() * 50) // 每种类型10-60人
  })

  backgroundCheckAbnormalTypes.forEach((type, index) => {
    // 如果参数中指定了异常类型，则过滤
    if (params.abnormalTypes && params.abnormalTypes.length > 0 && !params.abnormalTypes.includes(type.value)) {
      return
    }

    const count = 10 + Math.floor(Math.random() * 50) // 10-60人
    const pendingCount = Math.floor(count * (0.2 + Math.random() * 0.5)) // 20%-70%待处理
    const processedCount = count - pendingCount
    const percentage = totalAbnormal > 0 ? ((count / totalAbnormal) * 100).toFixed(2) : '0.00'

    data.push({
      id: index + 1,
      typeName: type.label,
      typeValue: type.value,
      count,
      pendingCount,
      processedCount,
      percentage: parseFloat(percentage)
    })
  })

  return data
}

// 根据报表类型生成对应的Mock数据
export const generateReportMockData = (reportType: string, params: any) => {
  switch (reportType) {
    case 'abnormal-personnel':
      return generateAbnormalPersonnelMockData(params)
    case 'unit-statistics':
      return generateUnitStatisticsMockData(params)
    case 'industry-statistics':
      return generateIndustryStatisticsMockData(params)
    case 'abnormal-type-statistics':
      return generateAbnormalTypeStatisticsMockData(params)
    default:
      return []
  }
}
