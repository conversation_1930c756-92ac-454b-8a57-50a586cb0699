import request from './request';

// 任务管理相关API
export const taskApi = {
  // 获取任务列表
  getTasks: (params: any) => request.get('/data-access/task', { params }),
  // 创建任务
  createTask: (data: any) => request.post('/data-access/task', data),
  // 更新任务
  updateTask: (id: string, data: any) => request.put(`/data-access/task/${id}`, data),
  // 删除任务
  deleteTask: (id: string) => request.delete(`/data-access/task/${id}`),
  // 启动任务
  startTask: (id: string) => request.post(`/data-access/task/${id}/start`),
  // 暂停任务
  pauseTask: (id: string) => request.post(`/data-access/task/${id}/pause`),
  // 恢复任务
  resumeTask: (id: string) => request.post(`/data-access/task/${id}/resume`),
  // 停止任务
  stopTask: (id: string) => request.post(`/data-access/task/${id}/stop`),
  // 获取任务日志
  getTaskLogs: (id: string, params: any) => request.get(`/data-access/task/${id}/logs`, { params })
}; 

// 单位管理相关API
export const unitApi = {
  // 获取单位列表
  getUnits: (params: any) => request.post('/magic-api/biz/unit/getUnit', params),
  // 获取单位详情
  getUnitDetail: (id: string) => request.get(`/magic-api/biz/unit/detail/${id}`),
  // 创建单位
  createUnit: (data: any) => request.post('/magic-api/biz/unit/create', data),
  // 更新单位
  updateUnit: (id: string, data: any) => request.put(`/magic-api/biz/unit/update/${id}`, data),
  // 删除单位
  deleteUnit: (id: string) => request.delete(`/magic-api/biz/unit/delete/${id}`),
  // 按名称搜索单位
  getUnitByName: (name: string) => request.get(`/magic-api/biz/unit/getUnitByName`, { params: { name } }),
}; 

// 背景审查相关API
export const backgroundCheckApi = {
  // 获取单位审查概览
  getUnitOverview: (unitId: string) => request.get(`/magic-api/biz/background-check/overview-unit/${unitId}`),
  // 获取背景审查列表
  getChecks: (params: any) => request.post('/magic-api/biz/background-check/list', params),
  // 获取背景审查详情 包括了审查发现在foundList参数
  getCheckDetail: (id: string) => request.get(`/magic-api/biz/background-check/detail/${id}`),
  // 保存背景审查 传递id则是保存 否则为创建
  saveCheck: (data: any) => request.post('/magic-api/biz/background-check/save', data),
  // 删除背景审查
  deleteCheck: (id: string) => request.post(`/magic-api/biz/background-check/delete/${id}`),
  // 完成背景审查
  completeCheck: (id: string) => request.post(`/magic-api/biz/background-check/complete/${id}`),
  // 保存发现 传递id则是保存 否则为创建
  saveFinding: (data: any) => request.post('/magic-api/biz/background-check/finding/save', data),
  // 删除审查发现
  deleteFinding: (id: string) => request.delete(`/magic-api/biz/background-check/finding/${id}`)
}; 

// 预警管理相关API
export const alertApi = {
  // 获取预警列表
  getAlerts: (params: any) => request.post('/magic-api/biz/back-alert/list', params),
  // 获取预警详情
  getAlertDetail: (id: string) => request.get(`/magic-api/biz/back-alert/detail/${id}`),
  // 保存预警 传递id则是保存 否则为创建
  saveAlert: (data: any) => request.post('/magic-api/biz/back-alert/save', data),
  // 删除预警
  deleteAlert: (id: string) => request.post(`/magic-api/biz/back-alert/delete/${id}`),
  // 处理预警
  processAlert: (data: any) => request.post('/magic-api/biz/back-alert/process', data),
  // 忽略预警
  ignoreAlert: (id: string) => request.post(`/magic-api/biz/back-alert/ignore/${id}`),
  // 获取预警统计信息
  getAlertStats: () => request.get('/magic-api/biz/back-alert/stats'),
  // 获取单位预警统计
  getUnitAlertStats: (unitId: string) => request.get(`/magic-api/biz/back-alert/unit-stats/${unitId}`)
}; 
