import { personnelMockData, type PersonnelData } from '@/data/personnelMockData'

// Mock数据管理器
// 使用localStorage存储所有数据，方便用户调整

// 生成唯一ID
const generateId = () => Date.now() + Math.random()

// 存储键名常量
const STORAGE_KEYS = {
  PERSONNEL: 'bg_personnel_list',
  BLACKLIST: 'bg_blacklist',
  NOTIFICATIONS: 'bg_notifications',
  STATISTICS: 'bg_statistics'
}

// 初始化mock数据
const initMockData = () => {
  // 人员数据
  if (!localStorage.getItem(STORAGE_KEYS.PERSONNEL)) {
    localStorage.setItem(STORAGE_KEYS.PERSONNEL, JSON.stringify(personnelMockData))
  }

  if (!localStorage.getItem('bg_blacklist')) {
    const mockBlacklist = [
      {
        id: 1,
        personnelId: 1,
        personnelName: '张三',
        personnelIdCard: '110101199001011234',
        blacklistType: 1, // 1-违纪违规 2-安全风险 3-其他
        reason: '违反公司规定',
        operator: '管理员',
        effectiveDate: '2023-06-01',
        expiryDate: '2024-06-01',
        status: 1, // 1-生效 2-失效
        createTime: '2023-06-01 14:00:00',
        updateTime: '2023-06-01 14:00:00'
      }
    ]
    localStorage.setItem('bg_blacklist', JSON.stringify(mockBlacklist))
  }

  if (!localStorage.getItem('bg_notifications')) {
    const mockNotifications = [
      {
        id: 1,
        title: '系统维护通知',
        content: '系统将于今晚进行维护，请提前保存工作。',
        type: 1, // 1-系统通知 2-安全提醒 3-紧急通知
        organizationId: 1,
        personnelIds: [1, 2],
        status: 1, // 1-已发送 2-草稿
        createTime: '2023-12-01 09:00:00'
      }
    ]
    localStorage.setItem('bg_notifications', JSON.stringify(mockNotifications))
  }

  if (!localStorage.getItem('bg_statistics')) {
    const mockStatistics = {
      personnelTypeData: [
        { name: '正式员工', value: 150 },
        { name: '临时员工', value: 80 },
        { name: '外包员工', value: 45 }
      ],
      riskLevelData: [
        { name: '低风险', value: 200 },
        { name: '中风险', value: 60 },
        { name: '高风险', value: 15 }
      ],
      regionData: [
        { name: '北京', value: 120 },
        { name: '上海', value: 85 },
        { name: '广州', value: 70 }
      ],
      totalPersonnel: 275,
      blacklistCount: 5,
      riskPersonnel: 75,
      newPersonnel: 12
    }
    localStorage.setItem('bg_statistics', JSON.stringify(mockStatistics))
  }
}

// Mock API函数
export const mockApi = {
  // 人员管理
  getPersonnelList: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_personnel_list') || '[]')
    const {
      current = 1,
      page = 1,
      size = 10,
      name,
      idCard,
      department,
      personnelType,
      organization,
      region,
      backgroundCheckResult,
      abnormalTypes,
      industry,
      status,
      processingStatus,
      entryDateStart,
      entryDateEnd,
      focusDateStart,
      focusDateEnd
    } = params

    let filteredData = data

    // 姓名筛选
    if (name) {
      filteredData = filteredData.filter((item: any) => item.name.includes(name))
    }

    // 身份证号筛选
    if (idCard) {
      filteredData = filteredData.filter((item: any) => item.idCard.includes(idCard))
    }

    // 部门筛选
    if (department) {
      filteredData = filteredData.filter((item: any) => item.department.includes(department))
    }

    // 所属单位筛选
    if (organization) {
      filteredData = filteredData.filter((item: any) => item.organization && item.organization.includes(organization))
    }

    // 区域筛选
    if (region) {
      filteredData = filteredData.filter((item: any) => item.region && item.region.includes(region))
    }

    // 所属行业筛选（支持多选）
    if (industry && industry.length > 0) {
      filteredData = filteredData.filter((item: any) => {
        return industry.includes(item.industry)
      })
    }

    // 在职状态筛选
    if (status !== undefined && status !== '') {
      filteredData = filteredData.filter((item: any) => +item.status === +status)
    }

    // 背景审查结果筛选
    if (backgroundCheckResult !== undefined && backgroundCheckResult !== '') {
      filteredData = filteredData.filter((item: any) => +item.backgroundCheckResult === +backgroundCheckResult)
    }

    // 异常类型筛选
    if (abnormalTypes && abnormalTypes.length > 0) {
      filteredData = filteredData.filter((item: any) => {
        if (!item.abnormalTypes || item.abnormalTypes.length === 0) return false
        return abnormalTypes.some((type: string) => item.abnormalTypes.includes(type))
      })
    }

    // 处理状态筛选
    if (processingStatus !== undefined && processingStatus !== '') {
      filteredData = filteredData.filter((item: any) => +item.processingStatus === +processingStatus)
    }

    // 人员类型筛选
    if (personnelType) {
      filteredData = filteredData.filter((item: any) => item.personnelType === parseInt(personnelType))
    }

    // 入职日期筛选
    if (entryDateStart) {
      filteredData = filteredData.filter((item: any) => item.entryDate >= entryDateStart)
    }
    if (entryDateEnd) {
      filteredData = filteredData.filter((item: any) => item.entryDate <= entryDateEnd)
    }

    // 关注日期筛选（用于关注人员页面）
    if (focusDateStart) {
      filteredData = filteredData.filter((item: any) => {
        // 使用updateTime作为关注日期
        const focusDate = item.updateTime ? item.updateTime.split(' ')[0] : item.createTime.split(' ')[0]
        return focusDate >= focusDateStart
      })
    }
    if (focusDateEnd) {
      filteredData = filteredData.filter((item: any) => {
        // 使用updateTime作为关注日期
        const focusDate = item.updateTime ? item.updateTime.split(' ')[0] : item.createTime.split(' ')[0]
        return focusDate <= focusDateEnd
      })
    }

    // 支持page和current两种参数
    const currentPage = page || current
    const start = (currentPage - 1) * size
    const end = start + size
    const records = filteredData.slice(start, end)

    return Promise.resolve({
      data: {
        records,
        total: filteredData.length,
        current: currentPage,
        size
      }
    })
  },

  getPersonnelDetail: (id: number) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_personnel_list') || '[]')
    const personnel = data.find((item: any) => item.id === id)
    return Promise.resolve({ data: personnel })
  },

  updatePersonnel: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_personnel_list') || '[]')
    const index = data.findIndex((item: any) => item.id === params.id)
    if (index !== -1) {
      data[index] = { ...data[index], ...params, updateTime: new Date().toLocaleString() }
      localStorage.setItem('bg_personnel_list', JSON.stringify(data))
    }
    return Promise.resolve({ data: 'success' })
  },

  exportPersonnelData: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_personnel_list') || '[]')
    const csvContent = 'data:text/csv;charset=utf-8,' + 
      '姓名,身份证号,电话,邮箱,部门,职位\n' +
      data.map((item: any) => `${item.name},${item.idCard},${item.phone},${item.email},${item.department},${item.position}`).join('\n')
    
    const encodedUri = encodeURI(csvContent)
    const link = document.createElement('a')
    link.setAttribute('href', encodedUri)
    link.setAttribute('download', '人员数据.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    return Promise.resolve({ data: 'success' })
  },

  // 黑名单管理
  getBlacklistList: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_blacklist') || '[]')
    const { current = 1, size = 10 } = params
    
    const start = (current - 1) * size
    const end = start + size
    const records = data.slice(start, end)
    
    return Promise.resolve({
      data: {
        records,
        total: data.length,
        current,
        size
      }
    })
  },

  getBlacklistDetail: (id: number) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_blacklist') || '[]')
    const blacklist = data.find((item: any) => item.id === id)
    return Promise.resolve({ data: blacklist })
  },

  addToBlacklist: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_blacklist') || '[]')
    const newItem = {
      id: generateId(),
      ...params,
      operator: '当前用户',
      status: 1,
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    }
    data.push(newItem)
    localStorage.setItem('bg_blacklist', JSON.stringify(data))
    return Promise.resolve({ data: 'success' })
  },

  updateBlacklist: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_blacklist') || '[]')
    const index = data.findIndex((item: any) => item.id === params.id)
    if (index !== -1) {
      data[index] = { ...data[index], ...params, updateTime: new Date().toLocaleString() }
      localStorage.setItem('bg_blacklist', JSON.stringify(data))
    }
    return Promise.resolve({ data: 'success' })
  },

  createBlacklist: (params: any) => {
    return mockApi.addToBlacklist(params)
  },

  removeFromBlacklist: (id: number, reason?: string) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_blacklist') || '[]')
    const index = data.findIndex((item: any) => item.id === id)
    if (index !== -1) {
      data[index].status = 2
      data[index].updateTime = new Date().toLocaleString()
      localStorage.setItem('bg_blacklist', JSON.stringify(data))
    }
    return Promise.resolve({ data: 'success' })
  },

  batchRemoveFromBlacklist: (ids: number[], reason: string) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_blacklist') || '[]')
    ids.forEach(id => {
      const index = data.findIndex((item: any) => item.id === id)
      if (index !== -1) {
        data[index].status = 2
        data[index].updateTime = new Date().toLocaleString()
      }
    })
    localStorage.setItem('bg_blacklist', JSON.stringify(data))
    return Promise.resolve({ data: 'success' })
  },

  exportBlacklistData: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_blacklist') || '[]')
    const csvContent = 'data:text/csv;charset=utf-8,' + 
      '姓名,身份证号,黑名单类型,加入原因,生效日期,失效日期\n' +
      data.map((item: any) => `${item.personnelName},${item.personnelIdCard},${item.blacklistType},${item.reason},${item.effectiveDate},${item.expiryDate}`).join('\n')
    
    const encodedUri = encodeURI(csvContent)
    const link = document.createElement('a')
    link.setAttribute('href', encodedUri)
    link.setAttribute('download', '黑名单数据.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    return Promise.resolve({ data: 'success' })
  },

  // 通知管理
  sendNotification: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_notifications') || '[]')
    const newItem = {
      id: generateId(),
      ...params,
      status: 1,
      createTime: new Date().toLocaleString()
    }
    data.push(newItem)
    localStorage.setItem('bg_notifications', JSON.stringify(data))
    return Promise.resolve({ data: 'success' })
  },

  // 统计数据
  getStatistics: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_statistics') || '{}')
    return Promise.resolve({ data })
  },

  getStatisticsOverview: (params: any) => {
    initMockData()
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        overview: {
          totalPersonnel: 1250,
          normalPersonnel: 1100,
          riskPersonnel: 120,
          blacklistPersonnel: 30
        },
        personnelTypeDistribution: [
          { type: 1, name: '正式员工', count: 800 },
          { type: 2, name: '合同工', count: 300 },
          { type: 3, name: '实习生', count: 150 }
        ],
        riskLevelDistribution: [
          { level: 1, name: '低风险', count: 80 },
          { level: 2, name: '中风险', count: 30 },
          { level: 3, name: '高风险', count: 10 }
        ],
        regionDistribution: [
          { region: '北京', count: 300 },
          { region: '上海', count: 250 },
          { region: '广州', count: 200 },
          { region: '深圳', count: 180 },
          { region: '成都', count: 150 },
          { region: '其他', count: 170 }
        ],
        trendData: [
          { date: '2023-01', count: 50 },
          { date: '2023-02', count: 60 },
          { date: '2023-03', count: 75 },
          { date: '2023-04', count: 90 },
          { date: '2023-05', count: 110 },
          { date: '2023-06', count: 130 }
        ],
        organizationStatistics: [
          {
            organization: '技术部',
            totalCount: 300,
            normalCount: 280,
            riskCount: 15,
            blacklistCount: 5,
            riskRate: 0.067
          },
          {
            organization: '市场部',
            totalCount: 200,
            normalCount: 185,
            riskCount: 12,
            blacklistCount: 3,
            riskRate: 0.075
          },
          {
            organization: '人力资源部',
            totalCount: 100,
            normalCount: 95,
            riskCount: 4,
            blacklistCount: 1,
            riskRate: 0.05
          },
          {
            organization: '财务部',
            totalCount: 80,
            normalCount: 76,
            riskCount: 3,
            blacklistCount: 1,
            riskRate: 0.05
          },
          {
            organization: '行政部',
            totalCount: 70,
            normalCount: 68,
            riskCount: 2,
            blacklistCount: 0,
            riskRate: 0.029
          }
        ]
      }
    })
  },

  getPersonnelTypeDistribution: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_statistics') || '{}')
    return Promise.resolve({ data: data.personnelTypeData || [] })
  },

  getRiskLevelDistribution: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_statistics') || '{}')
    return Promise.resolve({ data: data.riskLevelData || [] })
  },

  getRegionDistribution: (params: any) => {
    initMockData()
    const data = JSON.parse(localStorage.getItem('bg_statistics') || '{}')
    return Promise.resolve({ data: data.regionData || [] })
  },

  getPersonnelTrend: (params: any) => {
    const trendData = [
      { date: '2023-01', count: 100 },
      { date: '2023-02', count: 120 },
      { date: '2023-03', count: 150 },
      { date: '2023-04', count: 180 },
      { date: '2023-05', count: 200 },
      { date: '2023-06', count: 220 }
    ]
    return Promise.resolve({ data: trendData })
  },

  getOrganizationAbnormal: (params: any) => {
    const abnormalData = [
      { organizationName: '技术部', abnormalCount: 5, totalCount: 50 },
      { organizationName: '市场部', abnormalCount: 3, totalCount: 30 },
      { organizationName: '财务部', abnormalCount: 2, totalCount: 20 }
    ]
    return Promise.resolve({ data: abnormalData })
  },

  // 报表管理
  generateReport: (params: any) => {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        id: Math.floor(Math.random() * 1000),
        reportName: params.reportName || '背景审查报表',
        status: 1 // 生成中
      }
    })
  },

  getReportList: (params: any) => {
    const mockReports = [
      {
        id: 1,
        reportName: '2023年第一季度人员背景审查报告',
        reportType: 'quarterly',
        format: 'pdf',
        generateTime: '2023-04-01 10:00:00',
        status: 2, // 已完成
        fileSize: 1024 * 1024 * 2.5, // 2.5MB
        downloadUrl: 'https://example.com/reports/1.pdf',
        createdAt: '2023-04-01 09:55:00',
        updatedAt: '2023-04-01 10:00:00'
      },
      {
        id: 2,
        reportName: '2023年5月人员背景审查月报',
        reportType: 'monthly',
        format: 'excel',
        generateTime: '2023-06-01 14:30:00',
        status: 2, // 已完成
        fileSize: 1024 * 512, // 512KB
        downloadUrl: 'https://example.com/reports/2.xlsx',
        createdAt: '2023-06-01 14:25:00',
        updatedAt: '2023-06-01 14:30:00'
      },
      {
        id: 3,
        reportName: '技术部人员风险分析报告',
        reportType: 'custom',
        format: 'pdf',
        generateTime: '2023-06-15 16:45:00',
        status: 3, // 失败
        errorMessage: '数据处理过程中发生错误',
        createdAt: '2023-06-15 16:40:00',
        updatedAt: '2023-06-15 16:45:00'
      },
      {
        id: 4,
        reportName: '2023年上半年黑名单分析报告',
        reportType: 'custom',
        format: 'word',
        generateTime: '2023-06-30 09:00:00',
        status: 1, // 生成中
        createdAt: '2023-06-30 09:00:00',
        updatedAt: '2023-06-30 09:00:00'
      }
    ]
    
    // 简单的筛选逻辑
    let filteredReports = [...mockReports]
    
    if (params.reportName) {
      filteredReports = filteredReports.filter(report => 
        report.reportName.includes(params.reportName))
    }
    
    if (params.reportType) {
      filteredReports = filteredReports.filter(report => 
        report.reportType === params.reportType)
    }
    
    if (params.status !== undefined) {
      filteredReports = filteredReports.filter(report => 
        report.status === params.status)
    }

    // 分页
    const start = (params.page - 1) * params.size
    const end = start + params.size
    const paginatedReports = filteredReports.slice(start, end)

    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        records: paginatedReports,
        total: filteredReports.length,
        page: params.page,
        size: params.size
      }
    })
  },

  downloadReport: (id: number) => {
    const link = document.createElement('a')
    link.setAttribute('href', 'data:text/plain;charset=utf-8,这是一个模拟的报表文件内容')
    link.setAttribute('download', `报表_${id}.txt`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    return Promise.resolve({ data: 'success' })
  },

  deleteReport: (id: number) => {
    return Promise.resolve({ data: 'success' })
  },

  retryReport: (id: number) => {
    return Promise.resolve({ data: 'success' })
  },

  getReportPreview: (id: number) => {
    return Promise.resolve({ 
      data: {
        content: '这是报表预览内容',
        charts: [],
        tables: []
      }
    })
  },

  // 其他工具函数
  getOrganizationList: () => {
    const organizations = [
      { id: 1, name: '技术部' },
      { id: 2, name: '市场部' },
      { id: 3, name: '财务部' },
      { id: 4, name: '人事部' }
    ]
    return Promise.resolve({ data: organizations })
  },

  getRegionList: () => {
    const regions = [
      { id: 1, name: '北京' },
      { id: 2, name: '上海' },
      { id: 3, name: '广州' },
      { id: 4, name: '深圳' }
    ]
    return Promise.resolve({ data: regions })
  },

  uploadFile: (file: File) => {
    return Promise.resolve({ 
      data: {
        url: 'mock-file-url',
        filename: file.name,
        size: file.size
      }
    })
  },

  exportData: (params: any) => {
    const csvContent = 'data:text/csv;charset=utf-8,模拟导出数据\n数据1,数据2,数据3'
    const encodedUri = encodeURI(csvContent)
    const link = document.createElement('a')
    link.setAttribute('href', encodedUri)
    link.setAttribute('download', '导出数据.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    return Promise.resolve({ data: 'success' })
  },

  getSyncStatus: () => {
    return Promise.resolve({ 
      data: {
        status: 'completed',
        lastSyncTime: new Date().toLocaleString(),
        nextSyncTime: new Date(Date.now() + 3600000).toLocaleString()
      }
    })
  },

  triggerSync: () => {
    return Promise.resolve({ data: 'success' })
  },

  // 处理操作相关API
  updateProcessingStatus: (data: any) => {
    initMockData()
    const { personnelId, fromStatus, toStatus, reason } = data

    // 更新人员处理状态
    const personnelData = JSON.parse(localStorage.getItem(STORAGE_KEYS.PERSONNEL) || '[]')
    const personnelIndex = personnelData.findIndex((item: any) => item.id === personnelId)
    if (personnelIndex !== -1) {
      personnelData[personnelIndex].processingStatus = toStatus
      personnelData[personnelIndex].updateTime = new Date().toLocaleString()
      localStorage.setItem(STORAGE_KEYS.PERSONNEL, JSON.stringify(personnelData))
    }

    // 添加处理记录
    const processingRecords = JSON.parse(localStorage.getItem('bg_processing_records') || '[]')
    const newRecord = {
      id: Date.now(),
      personnelId,
      operatorId: 1,
      operatorName: '系统管理员',
      fromStatus,
      toStatus,
      reason,
      operateTime: new Date().toISOString(),
      createTime: new Date().toISOString()
    }
    processingRecords.unshift(newRecord)
    localStorage.setItem('bg_processing_records', JSON.stringify(processingRecords))

    return Promise.resolve({ data: 'success' })
  },

  getProcessingHistory: (personnelId: number) => {
    initMockData()
    const processingRecords = JSON.parse(localStorage.getItem('bg_processing_records') || '[]')
    const records = processingRecords.filter((record: any) => record.personnelId === personnelId)
    return Promise.resolve({ data: records })
  },

  // 批量更新正常人员的处理状态为"无需处理"
  batchUpdateNormalPersonnelStatus: () => {
    initMockData()
    const personnelData = JSON.parse(localStorage.getItem(STORAGE_KEYS.PERSONNEL) || '[]')
    let updatedCount = 0

    // 找到所有背景审查结果为正常(1)且处理状态不是无需处理(1)的人员
    personnelData.forEach((person: any) => {
      if (person.backgroundCheckResult === 1 && person.processingStatus !== 1) {
        person.processingStatus = 1 // 设置为无需处理
        person.updateTime = new Date().toLocaleString()
        updatedCount++
      }
    })

    localStorage.setItem(STORAGE_KEYS.PERSONNEL, JSON.stringify(personnelData))

    return Promise.resolve({
      data: {
        message: `成功更新 ${updatedCount} 名正常状态人员的处置状态为"无需处理"`,
        updatedCount
      }
    })
  },

  // 通知管理相关API
  sendPersonnelNotification: (data: any) => {
    initMockData()
    const { personnelId, organizationName, notificationType, notificationContent } = data

    const notificationRecords = JSON.parse(localStorage.getItem('bg_notification_records') || '[]')
    const newNotification = {
      id: Date.now(),
      personnelId,
      personnelName: '', // 需要从人员数据中获取
      organizationId: 1,
      organizationName,
      notificationType,
      notificationContent,
      sendStatus: 2, // 已发送
      sendTime: new Date().toISOString(),
      readStatus: 1, // 未读
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    // 获取人员姓名
    const personnelData = JSON.parse(localStorage.getItem(STORAGE_KEYS.PERSONNEL) || '[]')
    const personnel = personnelData.find((item: any) => item.id === personnelId)
    if (personnel) {
      newNotification.personnelName = personnel.name
    }

    notificationRecords.unshift(newNotification)
    localStorage.setItem('bg_notification_records', JSON.stringify(notificationRecords))

    return Promise.resolve({ data: 'success' })
  },

  getNotificationHistory: (personnelId: number) => {
    initMockData()
    const notificationRecords = JSON.parse(localStorage.getItem('bg_notification_records') || '[]')
    const records = notificationRecords.filter((record: any) => record.personnelId === personnelId)
    return Promise.resolve({ data: records })
  },

  getAllNotificationRecords: (params: any) => {
    initMockData()
    const { current = 1, size = 10, personnelId, organizationId, notificationType, sendStatus } = params
    let data = JSON.parse(localStorage.getItem('bg_notification_records') || '[]')

    // 筛选
    if (personnelId) {
      data = data.filter((item: any) => item.personnelId === personnelId)
    }
    if (organizationId) {
      data = data.filter((item: any) => item.organizationId === organizationId)
    }
    if (notificationType) {
      data = data.filter((item: any) => item.notificationType === notificationType)
    }
    if (sendStatus) {
      data = data.filter((item: any) => item.sendStatus === sendStatus)
    }

    const start = (current - 1) * size
    const end = start + size
    const records = data.slice(start, end)

    return Promise.resolve({
      data: {
        records,
        total: data.length,
        current,
        size
      }
    })
  }
}

// 导出初始化函数
export { initMockData }