// 权限工具函数

import { useAuthStore } from '@/stores/authStore'
import type { UserRole } from '@/types/auth'

/**
 * 检查是否有指定权限
 */
export function hasPermission(permission: string): boolean {
  const authStore = useAuthStore()
  return authStore.hasPermission(permission)
}

/**
 * 检查是否有任意一个权限
 */
export function hasAnyPermission(permissions: string[]): boolean {
  const authStore = useAuthStore()
  return permissions.some(permission => authStore.hasPermission(permission))
}

/**
 * 检查是否有所有权限
 */
export function hasAllPermissions(permissions: string[]): boolean {
  const authStore = useAuthStore()
  return permissions.every(permission => authStore.hasPermission(permission))
}

/**
 * 检查是否为指定角色
 */
export function hasRole(role: UserRole): boolean {
  const authStore = useAuthStore()
  return authStore.currentRole === role
}

/**
 * 检查是否为管理员
 */
export function isAdmin(): boolean {
  const authStore = useAuthStore()
  return authStore.isAdmin
}

/**
 * 检查是否为下级单位
 */
export function isUnit(): boolean {
  const authStore = useAuthStore()
  return authStore.currentRole === 'unit'
}

/**
 * 获取当前用户信息
 */
export function getCurrentUser() {
  const authStore = useAuthStore()
  return authStore.user
}

/**
 * 获取当前用户角色
 */
export function getCurrentRole(): UserRole {
  const authStore = useAuthStore()
  return authStore.currentRole
}

/**
 * 获取数据访问范围
 */
export function getDataScope() {
  const authStore = useAuthStore()
  return authStore.dataScope
}

/**
 * 根据权限过滤菜单项
 */
export function filterMenuByPermission<T extends { permission?: string | string[] }>(
  menus: T[]
): T[] {
  const authStore = useAuthStore()
  
  return menus.filter(menu => {
    if (!menu.permission) return true
    
    if (Array.isArray(menu.permission)) {
      return menu.permission.some(p => authStore.hasPermission(p))
    } else {
      return authStore.hasPermission(menu.permission)
    }
  })
}

/**
 * 根据角色过滤菜单项
 */
export function filterMenuByRole<T extends { roles?: UserRole[] }>(
  menus: T[]
): T[] {
  const authStore = useAuthStore()
  const currentRole = authStore.currentRole
  
  return menus.filter(menu => {
    if (!menu.roles || menu.roles.length === 0) return true
    return menu.roles.includes(currentRole)
  })
}

/**
 * 权限检查装饰器（用于组合式API）
 */
export function withPermissionCheck(permission: string | string[]) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args: any[]) {
      const authStore = useAuthStore()
      
      let hasRequiredPermission = false
      if (Array.isArray(permission)) {
        hasRequiredPermission = permission.some(p => authStore.hasPermission(p))
      } else {
        hasRequiredPermission = authStore.hasPermission(permission)
      }
      
      if (!hasRequiredPermission) {
        console.warn(`权限不足，无法执行操作: ${propertyKey}`)
        return Promise.reject(new Error('权限不足'))
      }
      
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 人员管理权限
  PERSONNEL_VIEW_ALL: 'personnel:view:all',
  PERSONNEL_VIEW_ORG: 'personnel:view:org',
  PERSONNEL_EDIT: 'personnel:edit',
  PERSONNEL_DELETE: 'personnel:delete',
  
  // 任务管理权限
  TASK_MANAGE: 'task:manage',
  TASK_ASSIGN: 'task:assign',
  TASK_PROCESS: 'task:process',
  TASK_SUBMIT: 'task:submit',
  TASK_REVIEW: 'task:review',
  
  // 工作台权限
  DASHBOARD_ADMIN: 'dashboard:admin',
  DASHBOARD_UNIT: 'dashboard:unit',
  
  // 统计分析权限
  STATISTICS_VIEW: 'statistics:view',
  REPORTS_GENERATE: 'reports:generate'
} as const

/**
 * 角色常量定义
 */
export const ROLES = {
  ADMIN: 'admin' as const,
  UNIT: 'unit' as const
} as const
