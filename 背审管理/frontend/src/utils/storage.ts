// localStorage 工具类

/**
 * 存储项接口
 */
interface StorageItem<T = any> {
  value: T
  timestamp: number
  expiry?: number // 过期时间（毫秒）
}

/**
 * localStorage 工具类
 */
class StorageUtil {
  private prefix = 'auth_system_'

  /**
   * 设置存储项
   */
  set<T>(key: string, value: T, expiry?: number): void {
    try {
      const item: StorageItem<T> = {
        value,
        timestamp: Date.now(),
        expiry
      }
      
      const fullKey = this.prefix + key
      localStorage.setItem(fullKey, JSON.stringify(item))
    } catch (error) {
      console.error('localStorage 设置失败:', error)
    }
  }

  /**
   * 获取存储项
   */
  get<T>(key: string): T | null {
    try {
      const fullKey = this.prefix + key
      const itemStr = localStorage.getItem(fullKey)
      
      if (!itemStr) {
        return null
      }

      const item: StorageItem<T> = JSON.parse(itemStr)
      
      // 检查是否过期
      if (item.expiry && Date.now() - item.timestamp > item.expiry) {
        this.remove(key)
        return null
      }

      return item.value
    } catch (error) {
      console.error('localStorage 获取失败:', error)
      return null
    }
  }

  /**
   * 移除存储项
   */
  remove(key: string): void {
    try {
      const fullKey = this.prefix + key
      localStorage.removeItem(fullKey)
    } catch (error) {
      console.error('localStorage 移除失败:', error)
    }
  }

  /**
   * 清除所有存储项
   */
  clear(): void {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.error('localStorage 清除失败:', error)
    }
  }

  /**
   * 检查存储项是否存在
   */
  has(key: string): boolean {
    return this.get(key) !== null
  }

  /**
   * 获取存储项的时间戳
   */
  getTimestamp(key: string): number | null {
    try {
      const fullKey = this.prefix + key
      const itemStr = localStorage.getItem(fullKey)
      
      if (!itemStr) {
        return null
      }

      const item: StorageItem = JSON.parse(itemStr)
      return item.timestamp
    } catch (error) {
      console.error('获取时间戳失败:', error)
      return null
    }
  }

  /**
   * 检查存储项是否过期
   */
  isExpired(key: string): boolean {
    try {
      const fullKey = this.prefix + key
      const itemStr = localStorage.getItem(fullKey)
      
      if (!itemStr) {
        return true
      }

      const item: StorageItem = JSON.parse(itemStr)
      
      if (!item.expiry) {
        return false // 没有设置过期时间，永不过期
      }

      return Date.now() - item.timestamp > item.expiry
    } catch (error) {
      console.error('检查过期状态失败:', error)
      return true
    }
  }
}

// 创建单例实例
export const storage = new StorageUtil()

// 权限相关的存储键
export const STORAGE_KEYS = {
  CURRENT_ROLE: 'current_role',
  USER_INFO: 'user_info',
  PERMISSIONS: 'permissions',
  LAST_LOGIN: 'last_login'
} as const

export default storage
