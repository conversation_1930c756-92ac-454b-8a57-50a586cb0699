// 通用状态类型
export type Status = 'active' | 'inactive' | 'pending' | 'resolved' | 'investigating'

// 字典相关类型
export interface DictionaryEntry {
  code: string
  value: string
  sort: number
  status: Status
}

export interface Dictionary {
  id: number
  name: string
  label: string
  description: string
  entries: DictionaryEntry[]
  updateTime: string
}

// 重点区域相关类型
export interface AreaMetrics {
  dataSize: string
  recordCount: string
  growthRate: string
  usageFrequency: string
}

export interface Area {
  id: number
  name: string
  code: string
  category: string
  description: string
  priority: string
  responsible: string
  status: Status
  relatedTables: string[]
  metrics: AreaMetrics
  createTime: string
  updateTime: string
}

// 人员相关类型
export interface Member {
  id: number
  username: string
  realName: string
  email: string
  phone: string
  department: string
  role: string
  password?: string
  confirmPassword?: string
  status: Status
}

export interface MemberSearchForm {
  username: string
  realName: string
  department: string
  role: string
  status: string
}

// 元数据相关类型
export interface Metadata {
  id: number
  name: string
  type: string
  description: string
  status: Status
}

export interface MetadataCategory {
  id: number
  name: string
  label: string
  icon: string
  description: string
}

// 数据保护相关类型
export interface BreachRecord {
  id: number
  tableName: string
  field: string
  time: string
  user: string
  operation: string
  status: Status
}

export interface ProtectionPolicy {
  id: number
  name: string
  description: string
  type: string
  rules: string
  level: string
  status: Status
  appliedTables: string[]
}

export interface PolicySearchForm {
  name: string
  type: string
  level: string
  status: string
}

// 单位相关类型
export interface Unit {
  id: number
  name: string
  code: string
  type: string
  address: string
  contactPerson: string
  contactPhone: string
  email: string
  status: Status
  description: string
  createTime: string
}

export interface UnitSearchForm {
  name: string
  code: string
  type: string
  status: string
  contactPerson: string
}

// 表相关类型
export interface Table {
  id: number
  name: string
  description: string
  department: string
  owner: string
  createTime: string
  updateTime: string
  status: Status
  fields: number
  records: number
}

// 统计相关类型
export interface ResourceStats {
  tables: {
    byCategory: Record<string, number>
  }
  fields: {
    byType: Record<string, number>
  }
  quality: {
    issuesByType: Record<string, number>
  }
}

// 图表相关类型
export type EChartsType = any // 这里使用 any 是因为 ECharts 的类型定义比较复杂，实际项目中应该导入 echarts 的类型定义 