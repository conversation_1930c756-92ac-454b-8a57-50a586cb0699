/* 全局样式文件 */

/* 全局字体大小设置 - 在原有基础上增加2px */
html, body {
  font-size: 17px !important;
}

/* 确保所有元素继承字体大小 */
* {
  font-size: inherit;
}

/* 下拉框样式调整 */
.el-select-dropdown {
  min-width: 200px !important;
}

.el-select-dropdown__item {
  padding: 0 20px !important;
}

/* 全局禁用Element Plus组件的过渡动画，避免表格翻页时的跳动效果 */

/* 禁用Element Plus标签的过渡动画 */
.el-tag {
  transition: none !important;
  animation: none !important;
}

/* 禁用表格内所有过渡效果 */
.el-table {
  transition: none !important;
}

.el-table * {
  transition: none !important;
  animation: none !important;
}

/* 禁用表格行的过渡效果 */
.el-table__row {
  transition: none !important;
}

.el-table__body-wrapper {
  transition: none !important;
}

/* 禁用表格单元格的过渡效果 */
.el-table td,
.el-table th {
  transition: none !important;
}

/* 禁用分页组件的过渡效果 */
.el-pagination {
  transition: none !important;
}

.el-pagination * {
  transition: none !important;
  animation: none !important;
}

/* 禁用按钮的过渡效果（可选，如果按钮动画也影响体验） */
.el-button {
  transition: none !important;
}

/* 禁用输入框的过渡效果（可选） */
.el-input__inner {
  transition: none !important;
}

/* 禁用选择器的过渡效果（可选） */
.el-select {
  transition: none !important;
}

.el-select * {
  transition: none !important;
}