# 内保单位背景审查系统 - 前端

这是保定市公安局内保单位背景审查系统的前端项目，基于Vue 3 + Vite + TypeScript + Element Plus开发。

## 功能模块

### 1. 仪表盘
- 提供系统整体数据概览
- 单位风险分布统计
- 背景审查完成情况统计
- 高风险单位分布
- 最新预警与待处理任务显示
- 快捷操作入口

### 2. 单位管理
- 单位信息的增删改查
- 单位风险等级管理
- 内保单位详情查看

### 3. 背景审查
- 创建各类背景审查任务
- 审查任务管理（分配/跟踪/更新）
- 审查流程记录与报告生成
- 问题整改跟踪

### 4. 风险评估
- 单位风险评估与分级
- 风险趋势分析
- 风险因素详细分析
- 单位风险排名

### 5. 预警管理
- 自动与手动预警生成
- 预警处理与跟踪
- 预警统计分析
- 预警处理流程记录

## 项目设置

### 安装依赖
```sh
npm install
```

### 开发环境启动
```sh
npm run dev
```

### 生产环境构建
```sh
npm run build
```

## 技术栈
- Vue 3: 前端框架
- Vite: 构建工具
- TypeScript: 类型检查
- Element Plus: UI组件库
- Echarts: 数据可视化图表
- Vue Router: 路由管理
- Pinia: 状态管理

## 目录结构
```
.
├── public             # 静态资源
├── src
│   ├── api            # API接口
│   ├── assets         # 项目资源
│   ├── components     # 公共组件
│   ├── router         # 路由配置
│   ├── stores         # 状态管理
│   ├── styles         # 样式文件
│   ├── types          # 类型定义
│   ├── utils          # 工具函数
│   ├── views          # 页面组件
│   │   ├── dashboard          # 仪表盘
│   │   ├── unit-management    # 单位管理
│   │   ├── background-check   # 背景审查
│   │   ├── risk-assessment    # 风险评估
│   │   └── alert-management   # 预警管理
│   ├── App.vue        # 主组件
│   └── main.ts        # 入口文件
└── ...
```

## 开发团队
本项目由保定市公安局内保部门开发维护。

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).
