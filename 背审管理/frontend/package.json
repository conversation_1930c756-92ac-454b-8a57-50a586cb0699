{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "preview": "vite preview", "build-only": "vite build --mode production", "type-check": "vue-tsc --build", "publish": "scp -r dist/* root@***************:/www/wwwroot/front/beishen/"}, "dependencies": {"axios": "^1.8.4", "echarts": "^5.6.0", "element-plus": "^2.9.7", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.13.9", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "sass": "^1.86.0", "typescript": "~5.8.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}