#!/bin/bash

# 创建target目录
rm -rf target
mkdir -p target/app/backend
# 前端目录
mkdir -p target/app/backend/public

echo "开始打包前端项目..."
cd frontend
npm install
npm run build
cd ..
cp -r frontend/dist/* target/app/backend/public/

echo "开始打包后端项目..."
cd backend

# 安装依赖
npm install

# 使用 @vercel/ncc 打包成单个文件
if ! npm list -g @vercel/ncc &>/dev/null; then
  echo "安装 @vercel/ncc 打包工具..."
  npm install -g @vercel/ncc
fi

# 生成单文件构建
echo "开始构建单文件后端..."
npx ncc build src/main.ts -o ../target/app/backend/dist --source-map --minify

# 复制必要的配置文件
cp package.json ../target/app/backend/

cd ..

# 创建Docker配置文件
cat > target/Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

COPY app /app

EXPOSE 3000

CMD ["node", "backend/dist/index.js"]
EOF

# 创建docker-compose文件
cat > target/docker-compose.yml << 'EOF'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - NODE_ENV=${NODE_ENV}
      - PORT=${PORT}
    restart: unless-stopped
EOF

# 复制.env文件
cp .env target/

# 创建run.sh脚本
cat > target/run.sh << 'EOF'
#!/bin/bash

# 检查.env文件是否存在
if [ ! -f .env ]; then
    echo "错误: .env 文件不存在！"
    echo "请确保 .env 文件包含必要的环境变量配置。"
    exit 1
fi

# 停止旧容器（如果存在）
echo "停止旧容器（如果存在）..."
docker-compose down

# 构建新镜像
echo "构建新镜像..."
docker-compose build

# 启动服务
echo "启动服务..."
docker-compose up -d

# 检查服务状态
echo "检查服务状态..."
if ! docker-compose ps | grep -q "Up"; then
    echo "错误: 服务未能正常启动！"
    docker-compose logs
    exit 1
fi

echo "应用已成功部署！"
echo "访问地址: http://localhost:3000"
EOF

# 设置执行权限
chmod +x target/run.sh

echo "打包完成！所有文件已复制到target目录。"
echo "请将target目录传输到服务器，然后在服务器上执行./run.sh" 