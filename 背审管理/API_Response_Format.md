# 预警管理 API 接口返回值格式

## 1. 获取预警列表

**接口**: `POST /magic-api/biz/back-alert/list`

**请求参数**:
```json
{
  "page": 1,
  "size": 10,
  "unitId": 123,
  "alertType": "personnel",
  "level": "high",
  "status": "pending",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31"
}
```

**返回值格式**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "unitId": 123,
        "unitName": "测试单位",
        "title": "人员异常预警",
        "alertType": "personnel",
        "level": "high",
        "description": "发现人员异常行为",
        "source": "system",
        "status": "pending",
        "suggestedHandler": "张三",
        "actualHandler": "",
        "deadline": "2024-12-31",
        "actionTaken": "",
        "processResult": "",
        "processNote": "",
        "createdBy": "系统",
        "updatedBy": "",
        "createdAt": "2024-01-15 10:30:00",
        "updatedAt": "2024-01-15 10:30:00",
        "resolvedAt": ""
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

**字段说明**:
- `unitId`: 单位ID
- `unitName`: 单位名称
- `alertType`: 预警类型 (personnel/financial/operational/information/related/facility)
- `level`: 预警级别 (high/medium/low)
- `source`: 预警来源 (manual/system/inspection/report)
- `status`: 处理状态 (pending/processing/resolved/ignored)
- `suggestedHandler`: 建议处理人
- `actualHandler`: 实际处理人
- `actionTaken`: 已采取措施
- `processResult`: 处理结果
- `processNote`: 处理备注
- `createdBy`: 创建人
- `updatedBy`: 更新人
- `createdAt`: 创建时间
- `updatedAt`: 更新时间
- `resolvedAt`: 解决时间

## 2. 获取预警详情

**接口**: `GET /magic-api/biz/back-alert/detail/{id}`

**请求参数**: 路径参数 `id` (预警ID)

**返回值格式**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "unitId": 123,
    "unitName": "测试单位",
    "title": "人员异常预警",
    "alertType": "personnel",
    "level": "high",
    "description": "发现人员异常行为，需要重点关注",
    "source": "system",
    "status": "pending",
    "suggestedHandler": "张三",
    "actualHandler": "",
    "deadline": "2024-12-31",
    "actionTaken": "",
    "processResult": "",
    "processNote": "",
    "createdBy": "系统",
    "updatedBy": "",
    "createdAt": "2024-01-15 10:30:00",
    "updatedAt": "2024-01-15 10:30:00",
    "resolvedAt": ""
  }
}
```

## 3. 保存预警

**接口**: `POST /magic-api/biz/back-alert/save`

**请求参数**:
```json
{
  "unitId": 123,
  "unitName": "测试单位",
  "title": "人员异常预警",
  "alertType": "personnel",
  "level": "high",
  "description": "发现人员异常行为",
  "source": "manual",
  "status": "pending",
  "suggestedHandler": "张三",
  "deadline": "2024-12-31",
  "createdBy": "当前用户"
}
```

**返回值格式**:
```json
{
  "code": 200,
  "message": "预警创建成功",
  "data": {
    "id": 1,
    "unitId": 123,
    "unitName": "测试单位",
    "title": "人员异常预警",
    "alertType": "personnel",
    "level": "high",
    "description": "发现人员异常行为",
    "source": "manual",
    "status": "pending",
    "suggestedHandler": "张三",
    "actualHandler": "",
    "deadline": "2024-12-31",
    "actionTaken": "",
    "processResult": "",
    "processNote": "",
    "createdBy": "当前用户",
    "updatedBy": "",
    "createdAt": "2024-01-15 10:30:00",
    "updatedAt": "2024-01-15 10:30:00",
    "resolvedAt": ""
  }
}
```

## 4. 删除预警

**接口**: `POST /magic-api/biz/back-alert/delete/{id}`

**请求参数**: 路径参数 `id` (预警ID)

**返回值格式**:
```json
{
  "code": 200,
  "message": "预警删除成功",
  "data": null
}
```

## 5. 处理预警

**接口**: `POST /magic-api/biz/back-alert/process`

**请求参数**:
```json
{
  "id": 1,
  "status": "resolved",
  "actualHandler": "李四",
  "actionTaken": "已联系相关部门进行核实，并采取相应措施",
  "processResult": "经核实，该预警为误报，已解除",
  "processNote": "后续加强监控",
  "updatedBy": "当前用户"
}
```

**返回值格式**:
```json
{
  "code": 200,
  "message": "预警处理成功",
  "data": {
    "id": 1,
    "unitId": 123,
    "unitName": "测试单位",
    "title": "人员异常预警",
    "alertType": "personnel",
    "level": "high",
    "description": "发现人员异常行为",
    "source": "manual",
    "status": "resolved",
    "suggestedHandler": "张三",
    "actualHandler": "李四",
    "deadline": "2024-12-31",
    "actionTaken": "已联系相关部门进行核实，并采取相应措施",
    "processResult": "经核实，该预警为误报，已解除",
    "processNote": "后续加强监控",
    "createdBy": "当前用户",
    "updatedBy": "当前用户",
    "createdAt": "2024-01-15 10:30:00",
    "updatedAt": "2024-01-15 15:45:00",
    "resolvedAt": "2024-01-15 15:45:00"
  }
}
```

## 6. 忽略预警

**接口**: `POST /magic-api/biz/back-alert/ignore/{id}`

**请求参数**: 路径参数 `id` (预警ID)

**返回值格式**:
```json
{
  "code": 200,
  "message": "预警已忽略",
  "data": {
    "id": 1,
    "status": "ignored",
    "updatedAt": "2024-01-15 15:45:00"
  }
}
```

## 7. 获取预警统计

**接口**: `GET /magic-api/biz/back-alert/stats`

**请求参数**: 无

**返回值格式**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalAlerts": 150,
    "pendingAlerts": 35,
    "processingAlerts": 28,
    "resolvedAlerts": 80,
    "ignoredAlerts": 7,
    "highAlerts": 45,
    "mediumAlerts": 60,
    "lowAlerts": 45,
    "overdueAlerts": 12
  }
}
```

**字段说明**:
- `totalAlerts`: 预警总数
- `pendingAlerts`: 未处理预警数
- `processingAlerts`: 处理中预警数
- `resolvedAlerts`: 已处理预警数
- `ignoredAlerts`: 已忽略预警数
- `highAlerts`: 高级别预警数
- `mediumAlerts`: 中级别预警数
- `lowAlerts`: 低级别预警数
- `overdueAlerts`: 超期预警数

## 8. 获取单位预警统计

**接口**: `GET /magic-api/biz/back-alert/unit-stats/{unitId}`

**请求参数**: 路径参数 `unitId` (单位ID)

**返回值格式**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "unitId": 123,
    "unitName": "测试单位",
    "totalAlerts": 25,
    "pendingAlerts": 8,
    "highAlerts": 5,
    "latestAlertTime": "2024-01-15 10:30:00"
  }
}
```

**字段说明**:
- `unitId`: 单位ID
- `unitName`: 单位名称
- `totalAlerts`: 该单位预警总数
- `pendingAlerts`: 该单位未处理预警数
- `highAlerts`: 该单位高级别预警数
- `latestAlertTime`: 最近预警时间

## 错误处理

**通用错误返回格式**:
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": null
}
```

**常见错误码**:
- `200`: 操作成功
- `400`: 参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 数据字典

### 预警类型 (alertType)
- `personnel`: 人员异常
- `financial`: 资金异常
- `operational`: 经营异常
- `information`: 信息安全
- `related`: 关联企业
- `facility`: 设施安全

### 预警级别 (level)
- `high`: 紧急
- `medium`: 重要
- `low`: 一般

### 预警来源 (source)
- `manual`: 手动添加
- `system`: 系统预警
- `inspection`: 检查发现
- `report`: 举报线索

### 处理状态 (status)
- `pending`: 未处理
- `processing`: 处理中
- `resolved`: 已处理
- `ignored`: 已忽略

### 软删除标记 (is_deleted)
- 0: 未删除
- 1: 已删除

注意：所有时间字段均采用 ISO 8601 格式（YYYY-MM-DDTHH:mm:ss） 